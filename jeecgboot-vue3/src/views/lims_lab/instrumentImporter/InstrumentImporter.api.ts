import { defHttp } from '/@/utils/http/axios';

enum Api {
  getTestsForImporter = '/dataCentre/getTestsForImporter',
  exportSequence = '/dataCentre/exportSequence',
}

/**
 * 获取Test列表
 */
export const getTestsForImporter = (instrumentId, taskId) =>
  defHttp.get({ url: Api.getTestsForImporter, params: { instrumentId: instrumentId, taskId: taskId } });

/**
 * 导出序列
 * @param testImporterVOs 测试导入数据列表
 * @returns 返回导出结果
 */
export const exportSequence = (testImporterVOs: any[]) =>
  defHttp.post({ url: Api.exportSequence, data: testImporterVOs });
