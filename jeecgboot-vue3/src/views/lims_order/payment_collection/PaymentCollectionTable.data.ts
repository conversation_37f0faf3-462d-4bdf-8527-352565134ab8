import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '期数',
    align: 'center',
    dataIndex: 'sortNum',
    width: 80,
  },
  {
    title: '回款类型',
    align: 'center',
    dataIndex: 'typeId_dictText',
    width: 100,
  },
  {
    title: '计划回款金额',
    align: 'center',
    dataIndex: 'planAmount',
  },
  {
    title: '关联报告',
    align: 'center',
    dataIndex: 'reportIds_dictText',
  },

  {
    title: '计划回款日期',
    align: 'center',
    dataIndex: 'planDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },

  {
    title: '已回款金额',
    align: 'center',
    dataIndex: 'actualAmount',
  },
  {
    title: '回款是否已核销',
    align: 'center',
    dataIndex: 'checked',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '是否完成工单',
    align: 'center',
    dataIndex: 'finished',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '负责人',
    align: 'center',
    dataIndex: 'responsibleBy_dictText',
  },
];
