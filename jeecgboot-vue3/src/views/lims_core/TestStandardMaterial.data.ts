import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '标准物质',
    align:"center",
    dataIndex: 'standardMaterialId_dictText'
   },
   {
    title: '标准物质使用量',
    align:"center",
    dataIndex: 'usageAmount'
   },
   {
    title: '使用量单位',
    align:"center",
    dataIndex: 'unitId_dictText'
   },
   {
    title: '记录状态id',
    align:"center",
    dataIndex: 'statusId_dictText'
   },
   {
    title: '测试id',
    align:"center",
    dataIndex: 'testId'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "标准物质",
      field: 'standardMaterialId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"standard_material,name,id"
      },
      //colProps: {span: 6},
 	},
	{
      label: "记录状态id",
      field: 'statusId',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"status"
      },
      //colProps: {span: 6},
 	},
	{
      label: "测试id",
      field: 'testId',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '标准物质',
    field: 'standardMaterialId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"standard_material,name,id"
    },
  },
  {
    label: '标准物质使用量',
    field: 'usageAmount',
    component: 'Input',
  },
  {
    label: '使用量单位',
    field: 'unitId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"sys_unit,unit_name,id"
    },
  },
  {
    label: '记录状态id',
    field: 'statusId',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"status"
     },
  },
  {
    label: '测试id',
    field: 'testId',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  standardMaterialId: {title: '标准物质',order: 0,view: 'sel_search', type: 'string',dictTable: "standard_material", dictCode: 'id', dictText: 'name',},
  usageAmount: {title: '标准物质使用量',order: 1,view: 'text', type: 'string',},
  unitId: {title: '使用量单位',order: 2,view: 'sel_search', type: 'string',dictTable: "sys_unit", dictCode: 'id', dictText: 'unit_name',},
  statusId: {title: '记录状态id',order: 3,view: 'list', type: 'string',dictCode: 'status',},
  testId: {title: '测试id',order: 4,view: 'text', type: 'string',},
  remark: {title: '备注',order: 5,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}