# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Hello Ɓennungo
previous_label=Ɓennuɗo
next.title=Hello faango
next_label=Yeeso

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Hello
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=e nder {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=Lonngo Woɗɗa
zoom_out_label=Lonngo Woɗɗa
zoom_in.title=Lonngo Ara
zoom_in_label=Lonngo Ara
zoom.title=Lonngo
presentation_mode.title=Faytu to  Presentation Mode
presentation_mode_label=Presentation Mode
open_file.title=Uddit Fiilde
open_file_label=Uddit
print.title=Winndito
print_label=Winndito
download.title=Aawto
download_label=Aawto
bookmark.title=Jiytol gonangol (natto walla uddit e henorde)
bookmark_label=Jiytol Gonangol

# Secondary toolbar and context menu
tools.title=Kuutorɗe
tools_label=Kuutorɗe
first_page.title=Yah to hello adanngo
first_page_label=Yah to hello adanngo
last_page.title=Yah to hello wattindiingo
last_page_label=Yah to hello wattindiingo
page_rotate_cw.title=Yiiltu Faya Ñaamo
page_rotate_cw_label=Yiiltu Faya Ñaamo
page_rotate_ccw.title=Yiiltu Faya Nano
page_rotate_ccw_label=Yiiltu Faya Nano

cursor_text_select_tool.title=Gollin kaɓirgel cuɓirgel binndi
cursor_text_select_tool_label=Kaɓirgel cuɓirgel binndi
cursor_hand_tool.title=Hurmin kuutorgal junngo
cursor_hand_tool_label=Kaɓirgel junngo

scroll_vertical.title=Huutoro gorwitol daringol
scroll_vertical_label=Gorwitol daringol
scroll_horizontal.title=Huutoro gorwitol lelingol
scroll_horizontal_label=Gorwitol daringol
scroll_wrapped.title=Huutoro gorwitol coomingol
scroll_wrapped_label=Gorwitol coomingol

spread_none.title=Hoto tawtu kelle kelle
spread_none_label=Alaa Spreads
spread_odd.title=Tawtu kelle puɗɗortooɗe kelle teelɗe
spread_odd_label=Kelle teelɗe
spread_even.title=Tawtu ɗereeji kelle puɗɗoriiɗi kelle teeltuɗe
spread_even_label=Kelle teeltuɗe

# Document properties dialog box
document_properties.title=Keeroraaɗi Winndannde…
document_properties_label=Keeroraaɗi Winndannde…
document_properties_file_name=Innde fiilde:
document_properties_file_size=Ɓetol fiilde:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bite)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bite)
document_properties_title=Tiitoonde:
document_properties_author=Binnduɗo:
document_properties_subject=Toɓɓere:
document_properties_keywords=Kelmekele jiytirɗe:
document_properties_creation_date=Ñalnde Sosaa:
document_properties_modification_date=Ñalnde Waylaa:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Cosɗo:
document_properties_producer=Paggiiɗo PDF:
document_properties_version=Yamre PDF:
document_properties_page_count=Limoore Kelle:
document_properties_page_size=Ɓeto Hello:
document_properties_page_size_unit_inches=nder
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=dariingo
document_properties_page_size_orientation_landscape=wertiingo
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Ɓataake
document_properties_page_size_name_legal=Laawol
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} × {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Ɗisngo geese yaawngo:
document_properties_linearized_yes=Eey
document_properties_linearized_no=Alaa
document_properties_close=Uddu

print_progress_message=Nana heboo winnditaade fiilannde…
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Haaytu

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Toggilo Palal Sawndo
toggle_sidebar_label=Toggilo Palal Sawndo
document_outline.title=Hollu Ƴiyal Fiilannde (dobdobo ngam wertude/taggude teme fof)
document_outline_label=Toɓɓe Fiilannde
attachments.title=Hollu Ɗisanɗe
attachments_label=Ɗisanɗe
thumbs.title=Hollu Dooɓe
thumbs_label=Dooɓe
findbar.title=Yiylo e fiilannde
findbar_label=Yiytu

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Hello {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Dooɓre Hello {{page}}

# Find panel button title and messages
find_input.title=Yiytu
find_input.placeholder=Yiylo nder dokimaa
find_previous.title=Yiylo cilol ɓennugol konngol ngol
find_previous_label=Ɓennuɗo
find_next.title=Yiylo cilol garowol konngol ngol
find_next_label=Yeeso
find_highlight=Jalbin fof
find_match_case_label=Jaaɓnu darnde
find_entire_word_label=Kelme timmuɗe tan
find_reached_top=Heɓii fuɗɗorde fiilannde, jokku faya les
find_reached_bottom=Heɓii hoore fiilannde, jokku faya les
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} wonande laabi {{total}}
find_match_count[two]={{current}} wonande laabi {{total}}
find_match_count[few]={{current}} wonande laabi {{total}}
find_match_count[many]={{current}} wonande laabi {{total}}
find_match_count[other]={{current}} wonande laabi {{total}}
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Ko ɓuri laabi {{limit}}
find_match_count_limit[one]=Ko ɓuri laani {{limit}}
find_match_count_limit[two]=Ko ɓuri laabi {{limit}}
find_match_count_limit[few]=Ko ɓuri laabi {{limit}}
find_match_count_limit[many]=Ko ɓuri laabi {{limit}}
find_match_count_limit[other]=Ko ɓuri laabi {{limit}}
find_not_found=Konngi njiyataa

# Error panel labels
error_more_info=Ɓeydu Humpito
error_less_info=Ustu Humpito
error_close=Uddu
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ɓatakuure: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Fiilde: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Gorol: {{line}}
rendering_error=Juumre waɗii tuma nde yoŋkittoo hello.

# Predefined zoom values
page_scale_width=Njaajeendi Hello
page_scale_fit=Keƴeendi Hello
page_scale_auto=Loongorde Jaajol
page_scale_actual=Ɓetol Jaati
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

loading_error=Juumre waɗii tuma nde loowata PDF oo.
invalid_file_error=Fiilde PDF moƴƴaani walla jiibii.
missing_file_error=Fiilde PDF ena ŋakki.
unexpected_response_error=Jaabtol sarworde tijjinooka.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 – Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Siiftannde]
password_label=Naatu finnde ngam uddite ndee fiilde PDF.
password_invalid=Finnde moƴƴaani. Tiiɗno eto kadi.
password_ok=OK
password_cancel=Haaytu

printing_not_supported=Reentino: Winnditagol tammbitaaka no feewi e ndee wanngorde.
printing_not_ready=Reentino: PDF oo loowaaki haa timmi ngam winnditagol.
web_fonts_disabled=Ponte geese ko daaƴaaɗe: horiima huutoraade ponte PDF coomtoraaɗe.
