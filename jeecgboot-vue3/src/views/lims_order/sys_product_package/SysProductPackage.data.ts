import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '业务类别',
    align: 'center',
    dataIndex: 'bizTypeId_dictText',
  },
  {
    title: '标准单价',
    align: 'center',
    dataIndex: 'stdPrice',
  },
  {
    title: '标准工时',
    align: 'center',
    dataIndex: 'stdTat',
  },
  {
    title: '分类',
    align: 'center',
    dataIndex: 'category_dictText',
  },
  {
    title: '标准',
    align: 'center',
    dataIndex: 'standardId_dictText',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'JInput',
  },
  {
    label: '业务类别',
    field: 'bizTypeId',
    component: 'JSearchSelect',
    componentProps:{
      dict:"biz_type,name,id",
      getPopupContainer: () => document.body,
    },
  },
];

//子表单数据
//子表表格配置
export const sysProductPackageDetailsColumns: JVxeColumn[] = [
  {
    title: '能力',
    key: 'capabilityId',
    type: JVxeTypes.selectSearch,
    options: [],
    dictCode: 'sys_capability,name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
    linkageKey: 'methodId',
  },
  {
    title: '指定方法',
    key: 'methodId',
    type: JVxeTypes.select,
    options: [],
    linkageKey: 'standardId',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  // {
  //   title: '指定标准',
  //   key: 'standardId',
  //   type: JVxeTypes.select,
  //   options: [],
  //   width: '200px',
  //   placeholder: '请输入${title}',
  //   defaultValue: '',
  // },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  bizTypeId: { title: '业务类别', order: 1, view: 'list', type: 'string', dictCode: 'C09' },
  stdPrice: { title: '标准单价', order: 2, view: 'text', type: 'string' },
  stdTat: { title: '标准工时', order: 3, view: 'text', type: 'string' },
  category: { title: '分类', order: 4, view: 'list', type: 'string', dictCode: 'package_category' },
  standardId: { title: '标准', order: 5, view: 'list', type: 'string', dictTable: 'sys_standard', dictCode: 'id', dictText: 'name' },
  //子表高级查询
  sysProductPackageDetails: {
    title: '套餐详情表',
    view: 'table',
    fields: {
      capabilityId: { title: '能力', order: 0, view: 'list', type: 'string', dictTable: 'capability', dictCode: 'id', dictText: 'name' },
      methodId: { title: '指定方法', order: 1, view: 'list', type: 'string', dictTable: 'sys_method', dictCode: 'id', dictText: 'name' },
      standardId: { title: '指定标准', order: 2, view: 'list', type: 'string', dictTable: 'sys_standard', dictCode: 'id', dictText: 'name' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
