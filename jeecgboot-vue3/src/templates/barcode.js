export default {
  panels: [
    {
      index: 0,
      name: 1,
      height: 40,
      width: 60,
      paperHeader: 49.5,
      paperFooter: 105.05050505050504,
      printElements: [
        {
          options: {
            left: 28.5,
            top: 10.5,
            height: 30,
            width: 120,
            testData: '测试',
            fontSize: 9.75,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'title',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            title: '',
            type: 'text',
          },
        },
        {
          options: {
            left: 28.5,
            top: 49.5,
            height: 40.5,
            width: 120,
            field: 'barcode',
            testData: 'XS888888888',
            fontSize: 10.5,
            lineHeight: 18,
            textAlign: 'center',
            textType: 'qrcode',
            qid: 'barcode',
            coordinateSync: false,
            widthHeightSync: false,
            barTextMode: 'svg',
            barWidth: '2',
            qrCodeLevel: 0,
            right: 145.5,
            bottom: 90,
            vCenter: 85.5,
            hCenter: 69.75,
          },
          printElementType: {
            title: '条形码',
            type: 'text',
          },
        },
      ],
      paperNumberLeft: 140,
      paperNumberTop: 91,
      paperNumberDisabled: true,
      paperNumberContinue: true,
      watermarkOptions: {
        content: '',
        fillStyle: 'rgba(184, 184, 184, 0.3)',
        fontSize: '14px',
        rotate: 25,
        width: 200,
        height: 200,
        timestamp: false,
        format: 'YYYY-MM-DD HH:mm',
      },
      panelLayoutOptions: {
        layoutType: 'column',
        layoutRowGap: 0,
        layoutColumnGap: 0,
      },
    },
  ],
};
