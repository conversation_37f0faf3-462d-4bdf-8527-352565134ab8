<template>
  <!-- 添加 @ok 事件处理器 -->
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" showFooter :title="getTitle" width="70%" @ok="handleOk">
    <div class="drawer-container">
      <!-- 使用vxe-toolbar替换原来的工具栏 -->
      <vxe-toolbar :buttons="toolbarButtons" @button-click="buttonClickEvent" />
      <vxe-table
        ref="tableRef"
        border
        keep-source
        :loading="loading"
        :data="tableData"
        :edit-config="editConfig"
        :menu-config="menuConfig"
        @menu-click="menuClickEvent"
        :edit-rules="validRules"
        :row-config="rowConfig"
        :checkbox-config="{ checkRowKeys: selectedRowKeys }"
        @checkbox-change="onCheckboxChange"
        @toggle-row-expand="onAnalyteRowExpand"
        @edit-closed="handleEditClosed"
        @mousedown="handleTableMousedown"
        @cell-click="handleCellClick"
      >
        <!-- 序号列 -->
        <vxe-column type="seq" width="60" fixed="left" drag-sort />

        <!-- 选择列 -->
        <vxe-column type="checkbox" width="60" fixed="left" />

        <!-- 展开列 -->
        <vxe-column type="expand" width="60" fixed="left">
          <template #content="{ row }">
            <div class="expand-wrapper">
              <a-tabs tabPosition="top" @change="(key) => handleTabChange(key, row)">
                <a-tab-pane v-for="tab in tabList" :key="tab.key" :tab="tab.title" forceRender>
                  <component
                    :is="tab.component"
                    :id="row.id"
                    :ref="(el) => setSubTableRef(el, tab.key, row.id)"
                    @add-row-success="handleSubTableAddSuccess"
                  />
                </a-tab-pane>
              </a-tabs>
            </div>
          </template>
        </vxe-column>

        <!-- 数据列 -->
        <vxe-column
          v-for="col in columns"
          :key="col.key"
          :field="col.key"
          :title="col.title"
          :width="col.width"
          :resizable="true"
          :edit-render="getEditRender(col)"
          :drag-sort="col.key === 'sortNum'"
        />

        <vxe-column title="操作" fixed="right" width="180">
          <template #default="{ row }">
            <div class="action-buttons">
              <a @click.stop.prevent="saveRow(row)">保存</a>
              <a-divider type="vertical" />
              <a-popconfirm title="是否确认删除" @confirm="handleDelete(row)" placement="topLeft">
                <a v-auth="'lims_core:sys_method_analyte:delete'"> 删除</a>
              </a-popconfirm>
              <a-divider type="vertical" />
              <a-dropdown>
                <template #overlay>
                  <a-menu>
                    <a-menu-item v-for="tab in tabList" :key="tab.key" @click="handleAddSubTable(row, tab.key)">
                      {{ tab.title }}
                    </a-menu-item>
                  </a-menu>
                </template>
                <a-button type="link" v-auth="'lims_core:sys_method_analyte:add'"> 新增 <Icon icon="mdi:chevron-down" /> </a-button>
              </a-dropdown>
            </div>
          </template>
        </vxe-column>
      </vxe-table>
      <vxe-pager v-model:currentPage="pageVO.currentPage" v-model:pageSize="pageVO.pageSize" :total="pageVO.total" @page-change="pageChange" />
    </div>

    <JPopupOnlReportModal
      @register="registerAnalyteSelector"
      code="sys_analyte_selector"
      valueFiled="id"
      :multi="true"
      :groupId="''"
      :selectKeys="selectKeys"
      @ok="callBack"
    />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, reactive, computed, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useModal } from '/@/components/Modal';
  import SysMethodAnalyteRoudingSubTable from '../subTables/SysMethodAnalyteRoudingSubTable.vue';
  import SysMethodAnalytePrecisionSubTable from '../subTables/SysMethodAnalytePrecisionSubTable.vue';
  import { columns } from '../SysMethodAnalyte.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, saveOrUpdate } from '../SysMethodAnalyte.api';
  import { useUserStore } from '/@/store/modules/user';
  import JPopupOnlReportModal from '/@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';
  import { Icon } from '/@/components/Icon';
  import { message } from 'ant-design-vue';
  import VXETable, { VxeToolbar, VxeToolbarPropTypes, VxeToolbarEvents, VxeTablePropTypes, VxePagerEvents, VxeTableEvents } from 'vxe-table';
  import { VxePager } from 'vxe-pc-ui';
  import { JVxeColumn, JVxeTypes } from '@/components/jeecg/JVxeTable/types';
  import { initDictOptions } from '@/utils/dict';
  import { buildUUID } from '/@/utils/uuid';
  import { useMethods } from '@/hooks/system/useMethods';
  import { useMessage } from '@/hooks/web/useMessage';
  import { filterObj } from '@/utils/common/compUtils';
  const { handleExportXls, handleImportXls } = useMethods();
  import { usePermission } from '/@/hooks/web/usePermission';

  const emit = defineEmits(['register', 'success']);
  const { createMessage, createConfirm } = useMessage();
  const { hasPermission } = usePermission();

  // 直接在组件中实现导出方法
  async function onExportXls() {
    try {
      // 构建导出参数
      const exportParams = {
        methodId: methodId.value,
        // 如果有选中行，添加到导出参数中
        ...(selectedRowKeys.value.length > 0 ? { selections: selectedRowKeys.value.join(',') } : {}),
        // 添加排序参数
        column: 'createTime',
        order: 'desc',
      };

      // 调用导出方法
      await handleExportXls('方法-检测指标', getExportUrl, filterObj(exportParams));
      createMessage.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      createMessage.error('导出失败');
    }
  }

  // 直接在组件中实现导入方法
  function onImportXls(file) {
    try {
      // 调用导入方法，成功后刷新表格
      const fileData = { file };
      return handleImportXls(
        fileData,
        getImportUrl,
        () => {
          createMessage.success('导入成功');
          reloadTable(1, pageVO.pageSize);
        },
        { methodId: methodId.value }
      );
    } catch (error) {
      console.error('导入失败:', error);
      createMessage.error('导入失败');
      return Promise.reject(error);
    }
  }
  // 批量删除方法
  async function batchHandleDelete() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.warning('请选择需要删除的记录');
      return;
    }
    try {
      loading.value = true;
      await batchDelete({ ids: selectedRowKeys.value.join(',') }, () => {
        tableData.value = tableData.value.filter((row) => !selectedRowKeys.value.includes(row.id));
        selectedRowKeys.value = [];
        tableRef.value?.refresh?.();
      });
    } catch (error) {
      console.error('批量删除失败:', error);
      createMessage.error('删除失败');
    } finally {
      loading.value = false;
    }
  }

  const rowConfig = {
    keyField: 'id',
    isHover: true,
    useKey: true,
    drag: true,
  };

  const methodId = ref('');
  const isUpdate = ref(true);
  const record = ref<Recordable>({});
  const selectKeys = ref('');
  const loading = ref(false);
  // 表格引用
  const tableRef = ref();
  // 表格数据
  const tableData = ref([]);
  // 选中行的key
  const selectedRowKeys = ref([]);

  // 定义标签页列表
  const tabList = [
    {
      key: 'sysMethodAnalyteRouding',
      title: '修约要求',
      component: SysMethodAnalyteRoudingSubTable,
    },
    {
      key: 'sysMethodAnalytePrecision',
      title: '精密度要求',
      component: SysMethodAnalytePrecisionSubTable,
    },
  ];

  // 工具栏按钮配置
  const toolbarButtons = ref<VxeToolbarPropTypes.Buttons>([
    { code: 'batchAdd', name: '批量添加', status: 'primary', icon: 'ant-design:plus-outlined' },
    { code: 'export', name: '导出', status: 'primary', icon: 'ant-design:export-outlined' },
    { code: 'import', name: '导入', status: 'primary', icon: 'ant-design:import-outlined' },
    {
      code: 'batchOps',
      name: '批量操作',
      status: 'default',
      icon: 'mdi:chevron-down',
      visible: computed(() => selectedRowKeys.value.length > 0),
      dropdowns: hasPermission('lims_core:sys_method_analyte:add')
        ? [{ code: 'batchDelete', name: '删除', status: 'error', icon: 'ant-design:delete-outlined' }]
        : [],
    },
  ]);

  // 工具栏按钮点击事件
  const buttonClickEvent: VxeToolbarEvents.ButtonClick = ({ code }) => {
    switch (code) {
      case 'batchAdd':
        handleOpen();
        break;
      case 'add':
        handleAdd();
        break;
      case 'export':
        onExportXls();
        break;
      case 'import':
        // 这里需要触发文件选择
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.xls,.xlsx';
        input.onchange = (e) => {
          const files = e.target.files;
          if (files && files.length > 0) {
            onImportXls(files[0]);
          }
        };
        input.click();
        break;
      case 'batchDelete':
        batchHandleDelete();
        break;
      case 'saveNumber':
        saveNumber(); // 调用保存序号方法
        break;
    }
  };
  async function saveNumber() {
    try {
      loading.value = true;
      const { tableData: allTableData } = tableRef.value.getTableData();
      if (!allTableData || allTableData.length === 0) {
        createMessage.warning('表格中没有数据可保存');
        return;
      }
      // 遍历表格数据，更新sortNum并保存
      for (let index = 0; index < allTableData.length; index++) {
        const row = allTableData[index];
        row.sortNum = index + 1; // 设置sortNum为索引+1
        await saveRow(row); // 调用现有saveRow方法保存
      }
    } catch (error) {
      console.error('保存序号失败:', error);
      createMessage.error('保存序号失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 修改编辑配置
  const editConfig = ref<VxeTablePropTypes.EditConfig>({
    trigger: 'click',
    mode: 'cell',
    showStatus: true,
    autoClear: true,
    beforeEditMethod: ({ row, column }) => {
      if (column.field === 'analyteId') {
        return false;
      }
      return true;
    },
  });

  const menuConfig = reactive<VxeTablePropTypes.MenuConfig>({
    body: {
      options: [
        [
          { code: 'btnCopydown', name: '向下复制' },
          { code: 'btnPaste', name: '粘贴' },
          { code: 'btnPasteColumn', name: '粘贴Excel列数据' },
        ],
      ],
    },
  });

  const pageVO = reactive({
    total: 0,
    currentPage: 1,
    pageSize: 600,
  });

  const insertRecords = [];

  const pageChange: VxePagerEvents.PageChange = ({ pageSize, currentPage }) => {
    console.log('currentPage', currentPage);
    console.log('pageSize', pageSize);
    pageVO.currentPage = currentPage;
    pageVO.pageSize = pageSize;
    reloadTable(currentPage, pageSize);
  };

  // 根据列类型获取编辑渲染器配置
  function getEditRender(col) {
    // 确保col有type属性
    const type = col.type || JVxeTypes.input;
    if (type === JVxeTypes.select) {
      return {
        name: 'select',
        options: [
          // 添加一个空选项作为默认值，设置为禁用状态
          { value: 0, label: `请选择${col.title}` },
          ...(col.options || []),
        ],
        props: {
          placeholder: `请选择${col.title}`,
          transfer: true,
          type: 'select',
          stopPropagation: true,
          clearable: true,
          autoFocus: true,
          defaultValue: col.defaultValue,
        },
        events: {
          change: (value, $event) => handleValueChange(value.row, $event.target.value, col.key),
          blur: handleBlur,
        },
      };
    } else if (type === JVxeTypes.input) {
      return {
        name: 'input',
        props: {
          placeholder: `请输入${col.title}`,
          type: 'text',
          stopPropagation: true,
          autoFocus: true,
          defaultValue: col.defaultValue,
        },
        events: {
          input: (value, $event) => handleValueChange(value.row, $event.target.value, col.key),
          blur: handleBlur,
          paste: (value, $event) => handlePaste(value, $event, col.key),
        },
      };
    }

    // 默认配置也需要修改
    return {
      name: 'input',
      props: {
        placeholder: `请输入`,
        type: 'text',
        stopPropagation: true,
        autoFocus: true,
        defaultValue: col.defaultValue,
      },
      events: {
        input: (value, $event) => handleValueChange(value.row, $event.target.value, col.key),
        blur: handleBlur,
        paste: (value, $event) => handlePaste(value, $event, 'defaultField'),
      },
    };
  }
  const validRules = ref<VxeTablePropTypes.EditRules<any>>({
    analyteId: [{ required: true, message: '必须填写' }],
    resultType: [{ required: true, message: '必须填写' }],
  });

  function handleOpen() {
    selectKeys.value = '';
    console.log('ddd', tableRef.value.getTableData());
    tableRef.value.getTableData().tableData.forEach((row) => {
      selectKeys.value += row.analyteId + ',';
    });
    // 如果最后一个字符是逗号就去掉
    if (selectKeys.value.charAt(selectKeys.value.length - 1) === ',') {
      selectKeys.value = selectKeys.value.substring(0, selectKeys.value.length - 1);
    }
    console.log(selectKeys.value);
    openAnalyteSelectModal(true);
  }

  async function callBack(data) {
    const { tableData: myTableData } = tableRef.value.getTableData();
    console.log('myTableData', myTableData);
    try {
      for (const row of data) {
        const index = myTableData.findIndex((item) => item.analyteId === row.id);
        if (index === -1) {
          console.log('新增', row.id);
          const newRecord = { methodId: methodId.value, analyteId: row.id, id: buildUUID() };
          columns.forEach((col) => {
            if (col.defaultValue !== undefined) {
              newRecord[col.key] = col.defaultValue;
            }
          });
          const { row: newRow } = await tableRef.value.insertAt(newRecord, -1);
          tableData.value.push(newRecord);
          insertRecords.push(newRow.id);
          await saveRow(newRow);
        }
      }
      await reloadTable(1, pageVO.pageSize);
      message.success('批量添加记录成功');
    } catch (error) {
      console.error('批量添加失败:', error);
      message.error('批量添加失败，请重试');
    }
  }

  // 处理编辑值变化
  function handleValueChange(row, selectedValue, fieldName) {
    // 更新行数据
    if (fieldName) {
      row[fieldName] = selectedValue;
      console.log(`已更新 ${fieldName} = ${selectedValue}`, row);
    }
  }

  // 处理失去焦点事件
  function handleBlur(params) {
    // 延迟执行，避免与其他事件冲突
    setTimeout(() => {
      if (tableRef.value && typeof tableRef.value.clearEdit === 'function') {
        tableRef.value.clearEdit();
      }
    }, 100);
  }

  // 处理粘贴事件
  function handlePaste(value, $event, fieldName) {
    try {
      // 获取剪贴板数据
      const clipboardData = $event.clipboardData || window.clipboardData;
      const pastedData = clipboardData.getData('text');

      if (!pastedData) {
        message.warning('剪贴板中没有数据');
        return;
      }

      // 分割粘贴的数据（按行分割）
      const lines = pastedData.split(/\r?\n/).filter((line) => line.trim() !== '');

      if (lines.length > 1) {
        // 阻止默认粘贴行为
        $event.preventDefault();
        // 多行数据，询问用户是否要填充整列
        createConfirm({
          iconType: 'warning',
          title: '检测到多行数据',
          content: `检测到 ${lines.length} 行数据，是否要将这些数据填充到当前列的多行中？`,
          onOk: () => {
            applyColumnPaste(value.row, fieldName, lines);
          },
          onCancel: () => {
            // 用户取消，只粘贴第一行数据到当前单元格
            const currentValue = lines[0].trim();
            value.row[fieldName] = currentValue;
            handleValueChange(value.row, currentValue, fieldName);
            message.info('已取消多行粘贴，仅粘贴第一行数据');
          },
        });
      }
    } catch (error) {
      console.error('粘贴处理失败:', error);
      message.error('粘贴失败，请重试');
    }
  }

  // 应用整列粘贴
  function applyColumnPaste(currentRow, fieldName, lines) {
    try {
      const { tableData: allTableData } = tableRef.value.getTableData();

      // 找到当前行在表格中的索引
      const currentRowIndex = allTableData.findIndex((item) => item.id === currentRow.id);
      if (currentRowIndex === -1) {
        message.error('无法确定当前行位置');
        return;
      }

      let updatedCount = 0;

      // 从当前行开始，依次填充数据
      for (let i = 0; i < lines.length && currentRowIndex + i < allTableData.length; i++) {
        const targetRow = allTableData[currentRowIndex + i];
        const newValue = lines[i].trim();

        if (newValue !== '') {
          targetRow[fieldName] = newValue;
          handleValueChange(targetRow, newValue, fieldName);
          updatedCount++;
        }
      }

      if (updatedCount > 0) {
        message.success(`已成功填充 ${updatedCount} 行数据到 ${fieldName} 列`);
        // 刷新表格显示
        tableRef.value.refreshData();
      } else {
        message.warning('没有有效数据被填充');
      }
    } catch (error) {
      console.error('整列粘贴失败:', error);
      message.error('整列粘贴失败，请重试');
    }
  }

  // 处理右键菜单触发的粘贴Excel列数据
  async function handlePasteColumnFromMenu(row, column) {
    try {
      const fieldName = column.field;
      if (!fieldName) {
        message.warning('无法确定要粘贴的列');
        return;
      }

      // 尝试读取剪贴板数据
      if (navigator.clipboard && navigator.clipboard.readText) {
        try {
          const clipboardData = await navigator.clipboard.readText();
          if (!clipboardData) {
            message.warning('剪贴板中没有数据');
            return;
          }

          // 分割粘贴的数据（按行分割）
          const lines = clipboardData.split(/\r?\n/).filter((line) => line.trim() !== '');

          if (lines.length === 0) {
            message.warning('剪贴板中没有有效数据');
            return;
          }

          if (lines.length === 1) {
            // 单行数据，直接设置到当前单元格
            const currentValue = lines[0].trim();
            row[fieldName] = currentValue;
            handleValueChange(row, currentValue, fieldName);
            message.success('已粘贴单行数据');
          } else {
            // 多行数据，询问用户是否要填充整列
            createConfirm({
              iconType: 'warning',
              title: '检测到多行数据',
              content: `检测到 ${lines.length} 行数据，是否要将这些数据填充到当前列的多行中？`,
              onOk: () => {
                applyColumnPaste(row, fieldName, lines);
              },
              onCancel: () => {
                // 用户取消，只粘贴第一行数据到当前单元格
                const currentValue = lines[0].trim();
                row[fieldName] = currentValue;
                handleValueChange(row, currentValue, fieldName);
                message.info('已取消多行粘贴，仅粘贴第一行数据');
              },
            });
          }
        } catch (clipboardError) {
          console.error('读取剪贴板失败:', clipboardError);
          message.error('无法读取剪贴板内容，请使用Ctrl+V直接粘贴');
        }
      } else {
        message.info('请使用Ctrl+V直接在输入框中粘贴数据');
      }
    } catch (error) {
      console.error('粘贴Excel列数据失败:', error);
      message.error('粘贴失败，请重试');
    }
  }

  async function loadDictData() {
    try {
      // 遍历列配置
      for (const col of columns) {
        if (col.dictCode && (col.type === JVxeTypes.select || col.type === JVxeTypes.selectSearch)) {
          // 使用getDictItems加载字典数据
          const dictData = await initDictOptions(col.dictCode);
          console.log('加载字典数据:', dictData);
          if (dictData && dictData.length > 0) {
            // 转换为options格式
            col.options = dictData.map((item) => ({
              value: item.value,
              label: item.text || item.label,
            }));
          }
        }
      }
    } catch (error) {
      message.error('加载字典数据失败');
    }
  }
  const userStore = useUserStore();
  const [registerAnalyteSelector, { openModal: openAnalyteSelectModal }] = useModal();

  // 初始化标题
  const getTitle = computed(() => `${record.value?.standardName + '-' + record.value?.name || '未命名方法'} - 检测指标管理`);

  // 注册drawer
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    console.log('drawer数据:', data);

    // 处理drawer传入的数据
    isUpdate.value = !!data?.isUpdate;

    // 重置状态
    selectedRowKeys.value = [];

    if (data?.record) {
      // 设置记录和方法ID
      record.value = { ...data.record };
      methodId.value = data.record.id;

      // 通知用户正在加载
      setDrawerProps({ loading: true });

      console.log('设置方法ID:', methodId.value);

      try {
        // 使用自定义参数手动获取数据
        loading.value = true;
        // 延迟执行以确保状态更新
        setTimeout(() => {
          reloadTable(1, pageVO.pageSize);
        }, 100);
      } catch (error) {
        console.error('加载数据错误:', error);
        loading.value = false;
        setDrawerProps({ loading: false });
      }
    } else {
      setDrawerProps({ loading: false });
      message.error('未接收到方法数据');
    }
  });

  /**
   * 处理确认按钮点击事件
   */
  async function handleOk() {
    try {
      const { tableData: allTableData } = tableRef.value.getTableData();
      // 获取表格更新的数据记录
      const updateRecords = tableRef.value.getUpdateRecords();
      console.log('更新的记录:', updateRecords);
      if (updateRecords && updateRecords.length > 0) {
        setDrawerProps({ confirmLoading: true });
        message.info(`正在保存 ${updateRecords.length} 条更新的数据，请稍候...`);
        // 循环保存每一行更新的数据
        for (const row of updateRecords) {
          await saveRow(row);
        }
        // message.success('所有更新的数据已保存');
      }
      // 2. 保存行顺序（sortNum）
      if (allTableData && allTableData.length > 0) {
        for (let index = 0; index < allTableData.length; index++) {
          const row = allTableData[index];
          if (row.sortNum !== index + 1) {
            row.sortNum = index + 1;
            await saveRow(row);
          }
        }
      }
      message.success('所有更新的数据已保存');
      setDrawerProps({ confirmLoading: false });
      closeDrawer();
    } catch (error) {
      console.error('保存数据错误:', error);
      message.error('保存数据失败，请检查后重试');
      setDrawerProps({ confirmLoading: false });
    }
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
    reloadTable(1, pageVO.pageSize);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    selectedRowKeys.value = [];
  }

  // 在reloadTable函数中修改数据加载方式
  async function reloadTable(pageNo, pageSize) {
    try {
      if (!methodId.value) {
        message.error('方法ID为空，无法加载数据');
        return;
      }
      // 重置表格加载状态
      loading.value = true;
      setDrawerProps({ loading: true });
      // 直接调用API获取数据
      const res = await list({ methodId: methodId.value, pageNo: pageNo, pageSize: pageSize, column: 'sortNum', order: 'asc' });
      console.log('加载数据:', res);
      pageVO.total = res.total;
      pageVO.currentPage = res.current;
      pageVO.pageSize = res.size;
      tableData.value = res.records || [];
      if (res && res.records) {
        tableData.value = res.records || [];
        loadDictData();
      } else {
        message.error('加载数据失败: 未找到records字段');
        tableData.value = [];
      }

      // 重置加载状态
      setTimeout(() => {
        loading.value = false;
        setDrawerProps({ loading: false });
      }, 500);
    } catch (error) {
      console.error('重新加载数据错误:', error);
      loading.value = false;
      setDrawerProps({ loading: false });
      message.error('加载数据失败');
    }
  }

  const onAnalyteRowExpand = (event) => {
    console.log('expand', event);
    // 当行展开时，加载第一个子表数据
    if (event.expanded && tabList && tabList.length > 0) {
      const row = event.row;
      const firstTabKey = tabList[0].key;

      // 延迟执行，确保子表已经渲染并且引用已经设置
      nextTick(async () => {
        // 获取第一个子表引用
        const key = `${firstTabKey}-${row.id}`;
        const subTableRef = subTableRefs.get(key);

        if (subTableRef) {
          // 调用子表的加载数据方法
          if (typeof subTableRef.loadData === 'function') {
            await subTableRef.loadData(row.id);
            console.log(`已加载第一个子表(${firstTabKey})数据`);
          } else {
            console.error('子表组件没有loadData方法');
          }
        } else {
          console.error('未找到子表引用，键名:', key);
        }
      });
    }
  };

  // 处理编辑完成
  function handleEditClosed(event) {
    console.log('编辑完成:', event);
    tableRef.value.clearEdit(); // 清除编辑状态
  }

  // 处理表格鼠标按下事件
  function handleTableMousedown(event) {
    event.stopPropagation();
  }

  // 处理单元格点击
  function handleCellClick({ row, column }) {
    // 只有在列有editRender属性时才设置编辑状态
    if (column.property && row && column.editRender) {
      tableRef.value.setEditCell(row, column.property);
    }
  }

  // 处理标签页切换
  async function handleTabChange(activeKey, row) {
    console.log('标签页切换:', activeKey, row);

    // 获取当前激活的子表引用
    const key = `${activeKey}-${row.id}`;
    const subTableRef = subTableRefs.get(key);

    if (subTableRef) {
      // 调用子表的加载数据方法
      if (typeof subTableRef.loadData === 'function') {
        await subTableRef.loadData(row.id);
        console.log(`已加载子表(${activeKey})数据`);
      } else {
        console.error('子表组件没有loadData方法');
      }
    } else {
      console.error('未找到子表引用，键名:', key);
    }
  }

  // 处理复选框变化
  function onCheckboxChange({ records }) {
    selectedRowKeys.value = records.map((item) => item.id);
  }

  // 子表引用映射
  const subTableRefs = reactive(new Map());

  // 设置子表引用
  function setSubTableRef(el, type, rowId) {
    if (el) {
      //console.log(`设置子表引用: ${type}-${rowId}`, el);
      subTableRefs.set(`${type}-${rowId}`, el);
    }
  }

  // 处理新增子表记录
  async function handleAddSubTable(row, type) {
    console.log('新增子表记录', row, type);

    // 自动展开当前行
    const $table = tableRef.value;
    if ($table && !$table.isRowExpandByRow(row)) {
      $table.toggleRowExpand(row, true); // 确保展开
    }

    // 创建新记录数据
    const newRecord = {
      maId: row.id, // 设置maId等于主表当前行ID
    };

    // 延迟执行，确保子表已经渲染并且引用已经设置
    nextTick(async () => {
      // 获取子表引用
      const key = `${type}-${row.id}`;
      const subTableRef = subTableRefs.get(key);

      if (subTableRef) {
        // 直接调用子组件暴露的方法
        if (typeof subTableRef.addRow === 'function') {
          await subTableRef.addRow(newRecord);
        } else {
          console.error('子表组件没有addRow方法');
          message.error('子表组件未提供添加行方法');
        }
      } else {
        console.error('未找到子表引用，键名:', key);
        message.warning('无法访问子表组件');
      }
    });
  }

  // 处理子表添加成功
  function handleSubTableAddSuccess(type) {
    message.success(`添加${type === 'sysMethodAnalyteRouding' ? '修约要求' : '精密度要求'}记录成功`);
  }

  async function saveRow(row) {
    console.log('保存行数据:', row);
    const { tableData: allTableData } = tableRef.value.getTableData();
    const rowIndex = allTableData.findIndex((item) => item.id === row.id);
    if (rowIndex !== -1) {
      row.sortNum = rowIndex + 1;
    } else {
      console.warn('Row not found in tableData, cannot assign sortNum');
    }
    console.log('insertRecords', insertRecords);
    if (insertRecords.includes(row.id)) {
      await saveOrUpdate(row, false).then((res) => {
        const index = insertRecords.indexOf(row.id);
        if (index > -1) {
          insertRecords.splice(index, 1);
        }
      });
    } else {
      await saveOrUpdate(row, true);
    }
    tableRef.value.reloadRow(row);
  }

  const menuClickEvent: VxeTableEvents.MenuClick<any> = ({ menu, type, row, rowIndex, column, columnIndex, $event }) => {
    switch (menu.code) {
      case 'btnCopydown':
        // 获取当前列的字段名
        const field = column.field;
        if (!field) {
          message.warning('无法确定要复制的列');
          return;
        }

        // 获取当前值
        const currentValue = row[field];

        // 获取所有选中行
        const selectedRows = tableRef.value.getCheckboxRecords(true);
        if (!selectedRows || selectedRows.length === 0) {
          message.warning('请先选择要复制的行');
          return;
        }

        // 获取表格所有数据
        const { tableData: allTableData } = tableRef.value.getTableData();

        // 找到当前行在表格中的索引
        const currentRowIndex = allTableData.findIndex((item) => item.id === row.id);
        if (currentRowIndex === -1) {
          message.warning('无法确定当前行位置');
          return;
        }

        // 获取选中行中在当前行之后的所有行
        const rowsToUpdate = selectedRows.filter((item) => {
          const itemIndex = allTableData.findIndex((tableRow) => tableRow.id === item.id);
          return itemIndex > currentRowIndex;
        });
        console.log('rowsToUpdate', rowsToUpdate);
        if (rowsToUpdate.length === 0) {
          message.warning('没有可更新的行，请选择当前行之后的行');
          return;
        }

        // 更新这些行的对应列的值
        rowsToUpdate.forEach((item) => {
          item[field] = currentValue;
          // item.reloadRow();
          // saveRow(item);
        });
        message.success(`已将 ${column.title || field} 列的值向下复制到 ${rowsToUpdate.length} 行`);
        break;
      case 'btnPaste':
        break;
      case 'btnPasteColumn':
        // 处理粘贴Excel列数据
        handlePasteColumnFromMenu(row, column);
        break;
      case 'myBtn3':
        break;
    }
  };
</script>

<style lang="less" scoped>
  .drawer-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .table-toolbar {
    display: flex;
    gap: 8px;
    margin: 16px 0;
  }

  .action-buttons {
    display: flex;
    align-items: center;

    .ant-btn {
      padding: 0 4px;
    }
  }

  .expand-wrapper {
    padding: 16px;
    background-color: #f8f8f8;
  }
</style>
