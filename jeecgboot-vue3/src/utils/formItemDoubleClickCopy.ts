/**
 * 为 BaseForm 的 FormItem 添加全局双击复制功能
 * 支持复制各种表单组件的值，包括 a-select、a-input 等
 */

/**
 * 初始化表单项双击复制功能
 */
export function initFormItemDoubleClickCopy() {
  console.log('初始化表单项双击复制功能');
  
  // 添加样式
  addCopyStyles();
  
  // 添加双击事件监听
  addDoubleClickListener();
  
  // 监听DOM变化
  observeFormChanges();
}

/**
 * 添加复制相关样式
 */
function addCopyStyles() {
  const style = document.createElement('style');
  style.id = 'form-item-copy-styles';
  style.textContent = `
    /* 表单项复制提示样式 */
    .ant-form-item.copy-enabled {
      position: relative;
    }
    
    .ant-form-item.copy-enabled:hover::after {
      content: '双击可复制';
      position: absolute;
      right: 0;
      top: 0;
      background: rgba(0, 0, 0, 0.75);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
      z-index: 1000;
      pointer-events: none;
      white-space: nowrap;
    }

    /* 针对输入框的特殊处理 */
    .ant-form-item.copy-enabled .ant-input:hover::after,
    .ant-form-item.copy-enabled .ant-input-number:hover::after,
    .ant-form-item.copy-enabled .ant-textarea:hover::after,
    .ant-form-item.copy-enabled .ant-input[data-copy-hover="true"]::after,
    .ant-form-item.copy-enabled .ant-input-number[data-copy-hover="true"]::after,
    .ant-form-item.copy-enabled .ant-textarea[data-copy-hover="true"]::after {
      content: '双击可复制';
      position: absolute;
      right: 5px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.75);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
      z-index: 1000;
      pointer-events: none;
      white-space: nowrap;
    }

    /* 针对选择器的特殊处理 */
    .ant-form-item.copy-enabled .ant-select-selector:hover::after,
    .ant-form-item.copy-enabled .ant-select-selector[data-copy-hover="true"]::after {
      content: '双击可复制';
      position: absolute;
      right: 25px;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(0, 0, 0, 0.75);
      color: white;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 12px;
      z-index: 1000;
      pointer-events: none;
      white-space: nowrap;
    }

    /* 确保组件容器有相对定位 */
    .ant-form-item.copy-enabled .ant-input,
    .ant-form-item.copy-enabled .ant-input-number,
    .ant-form-item.copy-enabled .ant-textarea,
    .ant-form-item.copy-enabled .ant-select-selector {
      position: relative;
    }

    /* 针对有值的输入框显示更明显的提示 */
    .ant-form-item.copy-enabled .ant-input:not(:placeholder-shown):hover::after,
    .ant-form-item.copy-enabled .ant-textarea:not(:placeholder-shown):hover::after {
      background: rgba(24, 144, 255, 0.8);
    }
    
    /* 复制成功提示 */
    .copy-success-toast {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #52c41a;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      animation: slideInRight 0.3s ease-out;
    }
    
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
  `;
  
  document.head.appendChild(style);
}

/**
 * 添加双击事件监听
 */
function addDoubleClickListener() {
  document.addEventListener('dblclick', (e) => {
    const target = e.target as HTMLElement;
    const formItem = getFormItemElement(target);
    
    if (formItem) {
      e.preventDefault();
      e.stopPropagation();
      
      const value = extractValueFromFormItem(formItem);
      if (value) {
        copyToClipboard(value);
        showCopySuccessMessage(value);
      }
    }
  }, true);
}

/**
 * 获取表单项元素
 */
function getFormItemElement(element: HTMLElement): HTMLElement | null {
  let current = element;
  let depth = 0;
  
  while (current && depth < 10) {
    if (current.classList.contains('ant-form-item')) {
      return current;
    }
    current = current.parentElement as HTMLElement;
    depth++;
  }
  
  return null;
}

/**
 * 从表单项中提取值
 */
function extractValueFromFormItem(formItem: HTMLElement): string {
  // 优先级顺序：选择器显示值 > 输入框值 > 文本内容
  
  // 1. 尝试从 a-select 获取显示值
  const selectValue = getSelectDisplayValue(formItem);
  if (selectValue) {
    return selectValue;
  }
  
  // 2. 尝试从输入框获取值
  const inputValue = getInputValue(formItem);
  if (inputValue) {
    return inputValue;
  }
  
  // 3. 尝试从其他组件获取值
  const otherValue = getOtherComponentValue(formItem);
  if (otherValue) {
    return otherValue;
  }
  
  // 4. 最后尝试获取纯文本内容
  const textValue = getTextContent(formItem);
  if (textValue) {
    return textValue;
  }
  
  return '';
}

/**
 * 获取 a-select 的显示值
 */
function getSelectDisplayValue(formItem: HTMLElement): string {
  // 查找选择器的选中项
  const selectionItem = formItem.querySelector('.ant-select-selection-item');
  if (selectionItem) {
    return (selectionItem.textContent || '').trim();
  }
  
  // 查找多选的选中项
  const selectionItems = formItem.querySelectorAll('.ant-select-selection-item');
  if (selectionItems.length > 0) {
    const values = Array.from(selectionItems).map(item => 
      (item.textContent || '').trim()
    ).filter(text => text);
    return values.join(', ');
  }
  
  return '';
}

/**
 * 获取输入框的值
 */
function getInputValue(formItem: HTMLElement): string {
  // 查找各种输入框
  const inputSelectors = [
    'input[type="text"]',
    'input[type="number"]',
    'input[type="email"]',
    'input[type="tel"]',
    'input[type="url"]',
    'textarea',
    '.ant-input',
    '.ant-input-number-input'
  ];
  
  for (const selector of inputSelectors) {
    const input = formItem.querySelector(selector) as HTMLInputElement;
    if (input && input.value) {
      return input.value.trim();
    }
  }
  
  return '';
}

/**
 * 获取其他组件的值
 */
function getOtherComponentValue(formItem: HTMLElement): string {
  // 日期选择器
  const datePicker = formItem.querySelector('.ant-picker-input input') as HTMLInputElement;
  if (datePicker && datePicker.value) {
    return datePicker.value.trim();
  }
  
  // 开关组件
  const switchElement = formItem.querySelector('.ant-switch');
  if (switchElement) {
    const isChecked = switchElement.classList.contains('ant-switch-checked');
    return isChecked ? '开启' : '关闭';
  }
  
  // 复选框
  const checkbox = formItem.querySelector('.ant-checkbox-checked + span');
  if (checkbox) {
    return (checkbox.textContent || '').trim();
  }
  
  // 单选框
  const radio = formItem.querySelector('.ant-radio-checked + span');
  if (radio) {
    return (radio.textContent || '').trim();
  }
  
  // 级联选择器
  const cascader = formItem.querySelector('.ant-cascader-picker-label');
  if (cascader) {
    return (cascader.textContent || '').trim();
  }
  
  // 树选择器
  const treeSelect = formItem.querySelector('.ant-tree-select-selection-item');
  if (treeSelect) {
    return (treeSelect.textContent || '').trim();
  }
  
  return '';
}

/**
 * 获取纯文本内容
 */
function getTextContent(formItem: HTMLElement): string {
  // 查找表单控件容器
  const formControl = formItem.querySelector('.ant-form-item-control-input-content');
  if (formControl) {
    // 排除一些不需要的元素
    const excludeSelectors = [
      '.ant-btn',
      '.ant-form-item-explain',
      '.ant-form-item-extra',
      'script',
      'style'
    ];
    
    let text = formControl.textContent || '';
    
    // 移除排除元素的文本
    excludeSelectors.forEach(selector => {
      const elements = formControl.querySelectorAll(selector);
      elements.forEach(el => {
        const elText = el.textContent || '';
        if (elText) {
          text = text.replace(elText, '');
        }
      });
    });
    
    return text.trim();
  }
  
  return '';
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text: string): Promise<void> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
    throw error;
  }
}

/**
 * 显示复制成功消息
 */
function showCopySuccessMessage(copiedText: string) {
  const toast = document.createElement('div');
  toast.className = 'copy-success-toast';
  
  // 限制显示的文本长度
  const displayText = copiedText.length > 20 ? 
    copiedText.substring(0, 20) + '...' : 
    copiedText;
  
  toast.textContent = `已复制: ${displayText}`;
  
  document.body.appendChild(toast);
  
  // 3秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 3000);
}

/**
 * 监听表单变化，为新的表单项添加复制功能
 */
function observeFormChanges() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          enableCopyForFormItems(element);
        }
      });
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 初始化现有的表单项
  enableCopyForFormItems(document.body);
}

/**
 * 为表单项启用复制功能
 */
function enableCopyForFormItems(container: HTMLElement) {
  const formItems = container.querySelectorAll('.ant-form-item');

  formItems.forEach(item => {
    const element = item as HTMLElement;
    if (!element.classList.contains('copy-enabled')) {
      element.classList.add('copy-enabled');

      // 为特定组件添加额外的处理
      addComponentSpecificHandling(element);
    }
  });
}

/**
 * 为特定组件添加额外的处理
 */
function addComponentSpecificHandling(formItem: HTMLElement) {
  // 为输入框添加悬停效果
  const inputs = formItem.querySelectorAll('.ant-input, .ant-input-number-input, .ant-textarea');
  inputs.forEach(input => {
    const element = input as HTMLElement;

    // 添加鼠标进入事件
    element.addEventListener('mouseenter', () => {
      element.setAttribute('data-copy-hover', 'true');
    });

    // 添加鼠标离开事件
    element.addEventListener('mouseleave', () => {
      element.removeAttribute('data-copy-hover');
    });
  });

  // 为选择器添加悬停效果
  const selectors = formItem.querySelectorAll('.ant-select-selector');
  selectors.forEach(selector => {
    const element = selector as HTMLElement;

    element.addEventListener('mouseenter', () => {
      element.setAttribute('data-copy-hover', 'true');
    });

    element.addEventListener('mouseleave', () => {
      element.removeAttribute('data-copy-hover');
    });
  });
}
