import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/dcs/dcsDoc/list',
  save = '/dcs/dcsDoc/add',
  edit = '/dcs/dcsDoc/edit',
  deleteOne = '/dcs/dcsDoc/delete',
  deleteBatch = '/dcs/dcsDoc/deleteBatch',
  invokeOneWorkflow = '/dcs/dcsDoc/apply',
  revise = '/dcs/dcsDoc/revise',
  review = '/dcs/dcsDoc/review',
  abandon = '/dcs/dcsDoc/abandon',
  publish = '/dcs/dcsDoc/publish',
  batchInvokeWorkflow = '/dcs/dcsDoc/batchApply',
  importExcel = '/dcs/dcsDoc/importExcel',
  exportXls = '/dcs/dcsDoc/exportXls',
  getOnlyOfficeEditorConfig = '/oo/editor-config',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 发审单个
 */
export const invokeOneWorkflow = (docId, applyTypeId, description, handleSuccess) => {
  return defHttp
    .post(
      {
        url: Api.invokeOneWorkflow,
        params: {
          docId: docId,
          applyTypeId: applyTypeId,
          description: description,
        },
      },
      { joinParamsToUrl: false }
    )
    .then(() => {
      handleSuccess();
    });
};

/**
 * 修订
 */
export const revise = (docId) => {
  return defHttp.post({ url: `${Api.revise}/${docId}` }, { joinParamsToUrl: false });
};

/**
 * 复审
 */
export const review = (docId, handleSuccess) => {
  return defHttp.post({ url: `${Api.review}/${docId}` }, { joinParamsToUrl: false }).then(() => {
    handleSuccess();
  });
};

/**
 * 作废
 */
export const abandon = (docId, handleSuccess) => {
  return defHttp.post({ url: `${Api.abandon}/${docId}` }, { joinParamsToUrl: false }).then(() => {
    handleSuccess();
  });
};

/**
 * 发布
 */
export const publish = (docId, handleSuccess) => {
  return defHttp.post({ url: `${Api.publish}/${docId}` }, { joinParamsToUrl: false }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量发审
 */
export const batchInvokeWorkflow = (params, handleSuccess) => {
  return defHttp.post({ url: Api.batchInvokeWorkflow, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

// 获取OnlyOffice EditorConfig
export const getOnlyOfficeEditorConfig = async (docId: number, permission: string) => {
  return await defHttp.get({ url: Api.getOnlyOfficeEditorConfig, params: { biz: 'dcs', bizId: docId, permission: permission } });
};
