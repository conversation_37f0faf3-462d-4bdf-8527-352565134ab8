<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <a-space style="margin-bottom: 5px">
      <a-button preIcon="ant-design:printer-outlined" type="primary" @click="handleTranslate">处理翻译</a-button>
    </a-space>
    <BasicForm @register="registerForm" ref="formRef" name="SysUnitForm" />
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <a-tab-pane tab="单位转换" key="sysUnitConversion" :forceRender="true">
        <JVxeTable
          keep-source
          resizable
          ref="sysUnitConversion"
          :loading="sysUnitConversionTable.loading"
          :columns="sysUnitConversionTable.columns"
          :dataSource="sysUnitConversionTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
  <TranslationModal @register="registerTranslationModal" />
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { formSchema, sysUnitConversionJVxeColumns } from '../SysUnit.data';
  import { saveOrUpdate, querySysUnitConversion } from '../SysUnit.api';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  import TranslationModal from '@/views/lims_core/components/TranslationModal.vue';
  import { message } from 'ant-design-vue';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const refKeys = ref(['sysUnitConversion']);
  const activeKey = ref('sysUnitConversion');
  const sysUnitConversion = ref();
  const tableRefs = { sysUnitConversion };
  const sysUnitConversionTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysUnitConversionJVxeColumns,
  });

  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      requestSubTableData(querySysUnitConversion, { id: data?.record?.id }, sysUnitConversionTable);
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

  async function reset() {
    await resetFields();
    activeKey.value = 'sysUnitConversion';
    sysUnitConversionTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);
    return {
      ...main, // 展开
      sysUnitConversionList: allValues.tablesValue[0].tableData,
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    try {
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  const [registerTranslationModal, { openModal: openTranslationModal }] = useModal();

  const handleTranslate = async () => {
    let values = await validate();
    try{
      await saveOrUpdate(values, isUpdate.value);
      openTranslationModal(true, {
        baseTable: 'sys_unit',
        baseId: values.id,
        recordvalue: { ...values },
        formSchema: formSchema,
      });
    }catch (e) {
      if (e.code === VALIDATE_FAILED) {
        message.error('请填入必填信息');
      } else {
        message.error(e.message);
      }
    }

  };
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
