<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #expandedRowRender="{ record }">
        <a-tabs tabPosition="top" v-model:activeKey="record.activeTabKey" animated>
          <a-tab-pane tab="余物退回申请记录" key="TestResultTable" :forceRender="true">
            <warehouseInApplySubTable :id="record.returnId" :key="record.id" />`
          </a-tab-pane>
        </a-tabs>
      </template>
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'lims_core:warehouse_out_apply:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls">
          导出</a-button
        >
        <j-upload-button type="primary" v-auth="'lims_core:warehouse_out_apply:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-button type="primary" preIcon="ant-design:user-delete-outlined" @click="handleReturnGoods">余物退回批量申请</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_core:warehouse_out_apply:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <WarehouseOutApplyModal @register="registerModal" @success="handleSuccess" />
    <WarehouseInApplyModal @register="registerInApplyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="lims_core-warehouseOutApply" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import WarehouseOutApplyModal from './components/WarehouseOutApplyModal.vue';
  import warehouseInApplySubTable from './Subtable/warehouseInApplySubTable.vue';
  import { columns, searchFormSchema, superQuerySchema } from './WarehouseOutApply.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl,updatestatus } from './WarehouseOutApply.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { message, Modal } from 'ant-design-vue';
  import { useUserStore } from '/@/store/modules/user';
  import WarehouseInApplyModal from './components/WarehouseInApplyModal.vue';
  import {inventoryQtyAndUnit} from "@/views/lims_order/sample/Sample.api";
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerInApplyModal, { openModal: InApplyModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '出库申请',
      api: list,
      columns,
      canResize: false,
      clickToRowSelect: true,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
      scroll: {
        y: 'calc(100vh - 380px)',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '出库申请',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:warehouse_out_apply:edit',
      },
      {
        label: '余物退回申请',
        onClick: handInApply.bind(null, record),
      },
    ];
  }
  async function handleReturnGoods() {
    if (selectedRows.value.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    message.warn('提示：标品不同效期或开启时间请不要使用批量退回');
    const ids = [];
    const articleNos = [];
    const articleTypeIds = [];
    let openerId;
    let openDates = '';
    let confirmValidDates = '';
    let isValid = true;
    selectedRows.value.forEach((item, index) => {
      const selectedRow = item;
      articleNos.push(selectedRow.articleNo);
      ids.push(selectedRow.id);
      articleTypeIds.push(selectedRow.articleTypeId);
      openerId = selectedRow.openerId;
      openDates = selectedRow.openDate;
      confirmValidDates = selectedRow.confirmValidDate;
    });
    if (!isValid) {
      return;
    }
    const record = {
      articleNo: articleNos.join(','),
      articleTypeId: articleTypeIds.join(','),
      operationReasonId: '余量退回',
      id: ids.join(','),
      openDate: openDates,
      confirmValidDate: confirmValidDates,
    };
    InApplyModal(true, {
      record,
      isUpdate: false,
      showFooter: true,
      isInitializationPart: true,
    });
  }

  function handInApply(re: Recordable) {
      const record = {
        articleNo: re.articleNo,
        articleTypeId: re.articleTypeId,
        operationReasonId:'余量退回',
        id: re.id,
        openerId: re.openerId,
        openDate: re.openDate,
        confirmValidDate: re.confirmValidDate,
      };
      InApplyModal(true, {
        record,
        isUpdate: false,
        showFooter: true,
        isInitializationPart: true,
      });
  }

  async function warehouserefused(re: Recordable) {
    Modal.confirm({
      title: '确认操作',
      content: '是否退回该出库申请？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await updatestatus({ id: re.id }, handleSuccess);
          console.log(res);
        } catch (error) {
          message.error('操作失败，请重试');
          console.error(error);
        }
      },
      onCancel: () => {
        message.info('操作已取消');
      },
    });
  }


  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_core:warehouse_out_apply:delete',
      },
      {
        label: '仓库拒出',
        onClick: warehouserefused.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
  :deep(.ant-table-wrapper) {
    overflow: auto; // 确保表格容器允许滚动
  }
</style>
