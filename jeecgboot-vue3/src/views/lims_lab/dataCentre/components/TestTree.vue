<template>
  <div class="bg-white m-4 mr-0 overflow-auto" style="height: calc(100vh - 120px)">
    <a-spin :spinning="loading">
      <template v-if="userIdentity === '2'">
        <BasicTree
          v-if="!treeReloading"
          :treeData="treeData"
          :selectedKeys="selectedKeys"
          :expandedKeys="expandedKeys"
          :autoExpandParent="autoExpandParent"
          :toolbar="customToolbarItems"
          :search="true"
          :searchInputProps="{ placeholder: '扫描样品/标本标签条码' }"
          :checkable="false"
          :showLine="true"
          :multiple="true"
          :beforeRightClick="handleBeforeRightClick"
          @search="onSearch"
          @select="onSelect"
          @expand="handleExpand"
        >
          <template #switcherIcon="{ node, expanded }">
            <component :is="node.switcherIcon" v-if="node.switcherIcon" :style="{ color: node.status === 'failed' ? 'red' : 'inherit' }" />
            <template v-else-if="node.children && node.children.length > 0">
              <FolderOpenOutlined v-if="expanded" />
              <FolderOutlined v-else />
            </template>
            <CloseCircleOutlined v-else />
          </template>
        </BasicTree>
      </template>
    </a-spin>
  </div>
  <DocEditor @register="registerModalDocEditor" />
  <ReworkApplyModal @register="registerReworkModal" @success="handleReworkSuccess" />
  <SysMethodAnalyteDrawer @register="registerDrawer" />
  <SysStandardLimitModal @register="registerLimitModel" />
</template>

<script lang="ts" setup>
  import { h, nextTick, ref } from 'vue';
  import {
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    FolderOpenOutlined,
    FolderOutlined,
    HourglassOutlined,
  } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getTests, generateTestRecord, getOOEditorConfig, queryByIdUrl, addDuplicate } from '../dataCentre.api';
  import DocEditor from '@/views/dcs/components/DocEditor.vue';
  import { useModal } from '@/components/Modal';
  import ReworkApplyModal from './ReworkApplyModal.vue';
  import { BasicTree } from '@/components/Tree';
  import type { ToolbarItem } from '@/components/Tree/src/types/tree';
  import { Modal, InputNumber } from 'ant-design-vue';
  import { useDrawer } from '@/components/Drawer';
  import SysMethodAnalyteDrawer from '@/views/lims_core/components/SysMethodAnalyteDrawer.vue';
  import { defHttp } from '@/utils/http/axios';
  import SysStandardLimitModal from '@/views/lims_core/components/SysStandardLimitModal.vue';
  import {sysEvaluationlist} from "@/views/lims_core/SysStandard.api";

  const [registerDrawer, { openDrawer }] = useDrawer();
  const emit = defineEmits(['select']);
  const { createMessage } = useMessage();
  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();
  const [registerReworkModal, { openModal: openReworkModal }] = useModal();
  const [registerLimitModel, { openModal: openLimitModel }] = useModal();

  let loading = ref<boolean>(false);
  // 树列表数据
  let treeData = ref<any[]>([]);
  // 当前展开的项
  let expandedKeys = ref<any[]>([]);
  // 当前选中的项
  let selectedKeys = ref<any[]>([]);
  // 是否自动展开父级
  let autoExpandParent = ref<boolean>(true);
  // 用户身份
  let userIdentity = ref<string>('2');
  // 树组件重新加载
  let treeReloading = ref<boolean>(false);
  const parentMap = new Map();
  // 加载Test信息
  async function loadTestTreeData(loadBy: string) {
    //console.log('parentMap', parentMap);
    const existsInChildren = Array.from(parentMap.values()).some((parentNode) => parentNode.children.some((child) => child.id === loadBy));
    if (!existsInChildren) {
      loading.value = true;

      // 根据loadBy的格式决定调用getTests的第一个参数
      let searchType = 0; // 默认为0
      // 判断是否为纯数字且长度为19位
      if (/^\d{19}$/.test(loadBy)) {
        searchType = 0;
      }
      // 判断是否以GBT开头
      else if (loadBy.startsWith('GBT')) {
        searchType = 2;
      }
      // 判断是否以3位字母开头，后跟9位数字
      else if (/^[A-Za-z]{3}\d{9}$/.test(loadBy)) {
        searchType = 1;
      } else if (/^IT\d{9}$/.test(loadBy)) {
        // 调试样品号
        searchType = 1;
      }
      getTests(searchType, loadBy)
        .then((res) => {
          console.log('res====', res);
          res.forEach((item) => {
            console.log('item', item);
            // 创建父节点
            if (!parentMap.has(item.method)) {
              const parentNode = {
                id: item.method, // 使用 method 作为父节点的 id
                key: item.method,
                title: item.method, // 显示文本
                children: [], // 初始化子节点数组
                switcherIcon: () => h(FolderOutlined),
              };
              parentMap.set(item.method, parentNode);
              treeData.value.push(parentNode); // 添加父节点到 treeData
            }
            // 创建子节点
            const childNode = {
              id: item.id, // 使用 id 作为子节点的 id
              key: item.id,
              title: item.sampleNo, // 显示文本
              status: item.status,
              methodId: item.methodId,
              switcherIcon: () => {
                switch (item.status) {
                  case '1':
                    return h(ClockCircleOutlined);
                  case '2':
                    return h(CheckCircleOutlined);
                  default:
                    return h(HourglassOutlined);
                }
              },
            };
            // 将子节点添加到对应的父节点
            parentMap.get(item.method).children.push(childNode);
            autoExpandParentNode(childNode);
          });
          console.log('parentMap', parentMap);
        })
        .finally(() => (loading.value = false));
    }
  }

  // 自动展开父节点，只展开一级
  function autoExpandParentNode(curNode) {
    let keys: Array<any> = [];
    treeData.value.forEach((item, index) => {
      if (item.children && item.children.length > 0) {
        keys.push(item.key);
      }
      if (item.key === curNode.key) {
        // 默认选中第一个
        setSelectedKey([item.id], item);
      }
    });
    if (keys.length > 0) {
      reloadTree();
      expandedKeys.value = keys;
    }
  }

  // 重新加载树组件，防止无法默认展开数据
  async function reloadTree() {
    await nextTick();
    treeReloading.value = true;
    await nextTick();
    treeReloading.value = false;
  }

  /**
   * 设置当前选中的行
   */
  function setSelectedKey(selKeys, data?: object) {
    //console.log('setSelectedKey', selKeys);
    selectedKeys.value = selKeys;
    if (data) {
      emit('select', data);
    }
  }

  // 搜索事件
  function onSearch(loadBy: string) {
    if (loadBy) {
      // 自动选中搜索框内容
      const searchInput = document.querySelector('.ant-input-search input') as HTMLInputElement;
      if (searchInput) {
        searchInput.select();
      }
      loadTestTreeData(loadBy);
    }
  }

  // 树选择事件
  function onSelect(selKeys, event) {
    // 处理叶子节点选择
    if (!event.node.children) {
      // 检查是否按下了 Ctrl 键 (Windows) 或 Command 键 (Mac)
      const isMultipleSelect = event.nativeEvent && (event.nativeEvent.ctrlKey || event.nativeEvent.metaKey);

      if (isMultipleSelect) {
        // 多选模式：保留已选中的节点，添加或移除当前节点
        const nodeKey = event.node.key;
        const isAlreadySelected = selectedKeys.value.includes(nodeKey);

        if (isAlreadySelected) {
          // 如果已经选中，则取消选中
          const newSelKeys = selectedKeys.value.filter((key) => key !== nodeKey);
          setSelectedKey(
            newSelKeys,
            event.selectedNodes.filter((node) => node.key !== nodeKey)
          );
        } else {
          // 如果未选中，则添加到选中数组
          setSelectedKey([...selectedKeys.value, nodeKey], event.selectedNodes);
        }
      } else {
        // 单选模式：只选中当前点击的节点
        const nodeKey = event.node.key;
        setSelectedKey([nodeKey], [event.node]);
      }
    } else {
      // 非叶子节点，传递空数组，表示清空数据
      setSelectedKey([], []);
    }
  }

  /**
   * 预览
   */
  async function previewFile(testId: string) {
    console.log('testId', testId);
    const ooConfig = await getOOEditorConfig(testId, 'test');
    openDocEditorModal(true, ooConfig);
  }

  // 树展开事件，每次只能展开一个方法
  const handleExpand = (keys: string[]) => {
    // 简化展开逻辑
    expandedKeys.value = keys;
    autoExpandParent.value = false;
  };

  const onContextMenuClick = async (treeKey, menuKey) => {
    console.log(treeKey, menuKey);
    let parentMethod = '';
    const parentNode = treeData.value.find((parent) => parent.children.some((child) => child.key === treeKey));
    if (parentNode) {
      parentMethod = parentNode.title;
    }
    if (menuKey === '1') {
      console.log('selectedKeys', selectedKeys.value);
      const recordUrls: [] = await generateTestRecord({ testIds: selectedKeys.value.join(',') });
      for (const record of recordUrls) {
        await previewFile(selectedKeys.value[recordUrls.indexOf(record)]);
      }
    } else if (menuKey === '2') {
      //发起复测申请
      console.log('selectedKeys', selectedKeys);
      if (selectedKeys.value.length === 0) {
        createMessage.warning('请先选择需要申请复测的检测项目');
        return;
      }

      // 打开复测申请弹窗
      openReworkModal(true, {
        testIds: selectedKeys.value.join(','),
        title: '申请复测',
      });
    } else if (menuKey === '3') {
      let params = { testId: treeKey };
      const data = await queryByIdUrl(params);
      const methodId = data.methodId;
      const record = {
        id: methodId,
        name: parentMethod,
      };
      openDrawer(true, {
        record,
        isUpdate: true,
      });
    } else if (menuKey === '4') {
      let inputNumber = ref<number | null>(null);
      Modal.confirm({
        title: '添加平行样',
        content: h('div', [
          h('p', '请输入平行样数量：'),
          h(InputNumber, {
            value: inputNumber.value,
            onChange: (value: number | null) => {
              inputNumber.value = value;
            },
            min: 1,
            style: { width: '100%' },
          }),
        ]),
        okText: '确认',
        cancelText: '取消',
        async onOk() {
          if (inputNumber.value === null || inputNumber.value <= 0) {
            createMessage.error('请输入有效的数量');
            return;
          }
          try {
            let params = { testId: treeKey, count: inputNumber.value };
            await addDuplicate(params);
            const nodeKey = treeKey;
            const parentNode = treeData.value.find((parent) => parent.children.some((child) => child.key === nodeKey));
            const node = parentNode?.children.find((child) => child.key === nodeKey);
            if (node) {
              selectedKeys.value = []; // 先清空
              await nextTick(); // 等待 DOM 更新
              selectedKeys.value = [nodeKey]; // 重新赋值
              setSelectedKey([nodeKey], [node]);
            } else {
              createMessage.warning('节点未找到，可能需要手动选择');
            }
          } catch (error) {
            createMessage.error('添加平行样失败');
          }
        },
        onCancel() {
          console.log('取消添加平行样');
        },
      });
    } else if (menuKey === '5') {
      const childNode = treeData.value
        .flatMap((parent) => parent.children)
        .find((child) => child.key === treeKey);
      if (childNode && childNode.methodId) {
        const methodId = childNode.methodId;
        const res = await sysEvaluationlist({ id: methodId });
        const record = res[0];
        openLimitModel(true, {
          record,
          isUpdate: true,
          showFooter: true,
        });
      } else {
        createMessage.error('无法获取 methodId');
      }
    }
  };

  // 处理复测申请成功事件
  const handleReworkSuccess = () => {
    createMessage.success('复测申请已提交');
    // 可以在这里添加其他逻辑，如刷新树等
  };

  // 添加自定义工具栏项
  const customToolbarItems: ToolbarItem[] = [
    {
      label: '我的任务',
      value: 'myTasks',
      onClick: () => {
        console.log('我的任务');
        // 清空现有树数据
        treeData.value = [];
        parentMap.clear();
        // 调用 getTests(3, '') 获取我的任务
        loading.value = true;
        getTests(3, '')
          .then((res) => {
            console.log('我的任务数据:', res);
            res.forEach((item) => {
              // 创建父节点
              if (!parentMap.has(item.method)) {
                const parentNode = {
                  id: item.method,
                  key: item.method,
                  title: item.method,
                  children: [],
                  switcherIcon: () => h(FolderOutlined),
                };
                parentMap.set(item.method, parentNode);
                treeData.value.push(parentNode);
              }
              // 创建子节点
              const childNode = {
                id: item.id,
                key: item.id,
                title: item.sampleNo,
                status: item.status,
                methodId: item.methodId,
                switcherIcon: () => {
                  switch (item.status) {
                    case '1':
                      return h(ClockCircleOutlined);
                    case '2':
                      return h(CheckCircleOutlined);
                    default:
                      return h(HourglassOutlined);
                  }
                },
              };
              // 将子节点添加到对应的父节点
              parentMap.get(item.method).children.push(childNode);
            });
            // 展开所有父节点
            expandedKeys.value = treeData.value.map((item) => item.key);
          })
          .finally(() => (loading.value = false));
      },
    },
    {
      label: '我指派的任务',
      value: 'assignedTasks',
      onClick: () => {
        console.log('我指派的任务');
        // 清空现有树数据
        treeData.value = [];
        parentMap.clear();
        // 调用 getTests(4, '') 获取我指派的任务
        loading.value = true;
        getTests(4, '')
          .then((res) => {
            console.log('我指派的任务数据:', res);
            res.forEach((item) => {
              // 创建父节点
              if (!parentMap.has(item.method)) {
                const parentNode = {
                  id: item.method,
                  key: item.method,
                  title: item.method,
                  children: [],
                  switcherIcon: () => h(FolderOutlined),
                };
                parentMap.set(item.method, parentNode);
                treeData.value.push(parentNode);
              }
              // 创建子节点
              const childNode = {
                id: item.id,
                key: item.id,
                title: item.sampleNo,
                status: item.status,
                methodId: item.methodId,
                switcherIcon: () => {
                  switch (item.status) {
                    case '1':
                      return h(ClockCircleOutlined);
                    case '2':
                      return h(CheckCircleOutlined);
                    default:
                      return h(HourglassOutlined);
                  }
                },
              };
              // 将子节点添加到对应的父节点
              parentMap.get(item.method).children.push(childNode);
            });
            // 展开所有父节点
            expandedKeys.value = treeData.value.map((item) => item.key);
          })
          .finally(() => (loading.value = false));
      },
    },
    {
      label: '我复核的任务',
      value: 'checkerTasks',
      onClick: () => {
        console.log('我复核的任务');
        // 清空现有树数据
        treeData.value = [];
        parentMap.clear();
        // 调用 getTests(4, '') 获取我指派的任务
        loading.value = true;
        getTests(5, '')
          .then((res) => {
            console.log('我复核的任务数据:', res);
            res.forEach((item) => {
              // 创建父节点
              if (!parentMap.has(item.method)) {
                const parentNode = {
                  id: item.method,
                  key: item.method,
                  title: item.method,
                  children: [],
                  switcherIcon: () => h(FolderOutlined),
                };
                parentMap.set(item.method, parentNode);
                treeData.value.push(parentNode);
              }
              // 创建子节点
              const childNode = {
                id: item.id,
                key: item.id,
                title: item.sampleNo,
                status: item.status,
                methodId: item.methodId,
                switcherIcon: () => {
                  switch (item.status) {
                    case '1':
                      return h(ClockCircleOutlined);
                    case '2':
                      return h(CheckCircleOutlined);
                    default:
                      return h(HourglassOutlined);
                  }
                },
              };
              // 将子节点添加到对应的父节点
              parentMap.get(item.method).children.push(childNode);
            });
            // 展开所有父节点
            expandedKeys.value = treeData.value.map((item) => item.key);
          })
          .finally(() => (loading.value = false));
      },
    },
  ];

  // 添加右键菜单处理函数
  function handleBeforeRightClick(node, event) {
    console.log('node', node);
    console.log('event', event);
    // 只有叶子节点显示右键菜单
    if (!node.children || node.children.length === 0) {
      // 设置当前选中节点，但不触发select事件
      selectedKeys.value = [node.key];
      // 不再调用setSelectedKey，避免触发emit
      return [
        {
          label: '生成电子记录',
          icon: 'copy',
          handler: () => {
            onContextMenuClick(node.key, '1');
          },
        },
        {
          label: '发起复测申请',
          icon: 'interaction',
          handler: () => {
            onContextMenuClick(node.key, '2');
          },
        },
        {
          label: '检测指标设置',
          icon: 'interaction',
          handler: () => {
            onContextMenuClick(node.key, '3');
          },
        },
        {
          label: '添加平行样',
          icon: 'interaction',
          handler: () => {
            onContextMenuClick(node.key, '4');
          },
        },
        {
          label: '评价标准',
          icon: 'interaction',
          handler: () => {
            onContextMenuClick(node.key, '5');
          },
        },
      ];
    }
    // 非叶子节点不显示右键菜单
    return [];
  }

  function updateNodeStatuses(testIds: string[], statuses: string[]) {
    treeData.value.forEach((parentNode) => {
      parentNode.children.forEach((childNode) => {
        const index = testIds.indexOf(childNode.id);
        if (index !== -1) {
          childNode.status = statuses[index];
          childNode.switcherIcon = () => {
            switch (childNode.status) {
              case '1':
                return h(ClockCircleOutlined);
              case '2':
                return h(CheckCircleOutlined);
              default:
                return h(HourglassOutlined);
            }
          };
        }
      });
    });
    reloadTree();
  }
  defineExpose({
    updateNodeStatuses,
  });
</script>
<style lang="less" scoped>
  /*升级antd3后，查询框与树贴的太近，样式优化*/
  :deep(.jeecg-tree-header) {
    margin-bottom: 6px;
  }
  /* 自定义图标样式 */
  :deep(.ant-tree-switcher) {
    margin-right: 4px;
  }

  /* 强制启用横向滚动 */
  :deep(.scrollbar__wrap) {
    overflow-x: auto !important; /* 覆盖默认的hidden设置 */
  }

  :deep(.ant-tree) {
    min-width: 460px; /* 增加最小宽度 */
    width: max-content; /* 确保树的宽度能够容纳所有内容 */
  }

  /* 确保节点内容完整显示 */
  :deep(.ant-tree-node-content-wrapper) {
    white-space: normal !important;
    height: auto !important;
    padding: 4px 0;
  }

  :deep(.ant-tree-title) {
    display: inline-block;
    width: auto;
    word-break: break-all;
    padding-right: 20px; /* 添加右侧内边距，确保文本不会紧贴边缘 */
  }

  /* 确保树视图内容不被截断 */
  :deep(.scrollbar__view) {
    width: max-content !important;
    min-width: 100% !important;
  }

  /* 增加树节点的容器宽度 */
  :deep(.ant-tree-list) {
    width: max-content !important;
    min-width: 460px !important; /* 与树组件保持一致 */
  }

  /* 确保树节点内容有足够的空间 */
  :deep(.ant-tree-list-holder-inner) {
    min-width: 460px !important;
    width: max-content !important;
  }
</style>
