import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '类型编码',
    align: 'center',
    dataIndex: 'typeCode',
  },
  {
    title: '类型名称',
    align: 'left',
    dataIndex: 'typeName',
  },
  {
    title: '审批流',
    align: 'center',
    dataIndex: 'workflowId_dictText',
  },
  {
    title: '当前文件数',
    align: 'center',
    dataIndex: 'currentSn',
  },
  {
    title: '编号规则',
    align: 'center',
    dataIndex: 'docNoRule'
   },
   {
    title: '复审频次',
    align: 'center',
    dataIndex: 'reviewFrequencyId_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '类型编码',
    field: 'typeCode',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '类型名称',
    field: 'typeName',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '类型编码',
    field: 'typeCode',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入类型编码!' }];
    },
  },
  {
    label: '类型名称',
    field: 'typeName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入类型名称!' }];
    },
  },
  {
    label: '审批流',
    field: 'workflowId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'qm_template',
    },
  },
  {
    label: '当前文件数',
    field: 'currentSn',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入当前流文件数!' }];
    },
  },
  {
    label: '编号规则',
    field: 'docNoRule',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入编号规则!'},
          ];
     },
  },  
  {
    label: '复审频次',
    field: 'reviewFrequencyId',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"review_frequency"
     },
  },

  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
    // TODO 主键隐藏字段，目前写死为ID
    {
      label: '',
      field: 'parentId',
      component: 'Input',
      show: false,
    },
];

// 高级查询数据
export const superQuerySchema = {
  typeCode: { title: '类型编码', order: 0, view: 'text', type: 'string' },
  typeName: { title: '类型名称', order: 1, view: 'text', type: 'string' },
  workflowId: { title: '审批流', order: 2, view: 'sel_search', type: 'string', dictCode: 'qm_template' },
  currentSn: { title: '当前文件数', order: 3, view: 'number', type: 'number' },
  docNoRule: {title: '编号规则',order: 4,view: 'text', type: 'string',},
  reviewFrequencyId: {title: '复审频次',order: 16,view: 'list', type: 'string',dictCode: 'review_frequency',},
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
