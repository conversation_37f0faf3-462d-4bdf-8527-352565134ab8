import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编号',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'typeId_dictText',
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierId_dictText',
  },
  {
    title: '型号规格',
    align: 'center',
    dataIndex: 'model',
  },
  {
    title: '购入时间',
    align: 'center',
    dataIndex: 'purchaseDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '最新校准时间',
    align: 'center',
    dataIndex: 'lastCalibrationDate',
  },
  {
    title: '校准周期（天）',
    align: 'center',
    dataIndex: 'calibrationCycle',
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'code',
    component: 'Input', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '类型',
    field: 'typeId',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择类型',
      dictCode: 'sys_instrument_type_selector,abbr_name,id',
      multi: true,
    },
    //colProps: {span: 6},
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择供应商',
      dictCode: 'sys_supplier_selector,abbr_name,id',
      multi: true,
    },
    //colProps: {span: 6},
  },
  {
    label: '购入时间',
    field: 'purchaseDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
  {
    label: '最新校准时间',
    field: 'lastCalibrationDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'code',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入编号!' }];
    },
  },
  {
    label: '类型',
    field: 'typeId',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择类型',
      dictCode: 'sys_instrument_type_selector,abbr_name,id,,',
      multi: true,
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入类型!' }];
    },
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择供应商',
      dictCode: 'sys_supplier_selector,abbr_name,id,,',
      multi: true,
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入供应商!' }];
    },
  },
  {
    label: '型号规格',
    field: 'model',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入型号规格!' }];
    },
  },
  {
    label: '购入时间',
    field: 'purchaseDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入购入时间!' }];
    },
  },
  {
    label: '最新校准时间',
    field: 'lastCalibrationDate',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    label: '校准周期（天）',
    field: 'calibrationCycle',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入校准周期（天）!' }];
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: { title: '编号', order: 0, view: 'text', type: 'string' },
  typeId: { title: '类型', order: 1, view: 'popup_dict', type: 'string' },
  supplierId: { title: '供应商', order: 2, view: 'popup_dict', type: 'string' },
  model: { title: '型号规格', order: 3, view: 'text', type: 'string' },
  purchaseDate: { title: '购入时间', order: 4, view: 'date', type: 'string' },
  lastCalibrationDate: { title: '最新校准时间', order: 5, view: 'datetime', type: 'string' },
  calibrationCycle: { title: '校准周期（天）', order: 6, view: 'number', type: 'number' },
  remark: { title: '备注', order: 7, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
