import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { useUserStore } from '@/store/modules/user';
//列表数据
const userStore = useUserStore();
const allDictItems = userStore.getAllDictItems;
export const columns: BasicColumn[] = [
  {
    fixed: 'left',
    width: 80,
    title: '测试状态',
    align: 'center',
    resizable: true,
    dataIndex: 'testControlStatus_dictText',
    customRender: ({ text }) => {
      const color = {
        正常: 'success',
        取消: 'error',
        部分取消: 'warning',
        补样中: 'orange',
      };

      return (
        <a-tag color={color[text]} style={{ fontSize: '12px' }}>
          {text}
        </a-tag>
      );
    },
  },
  {
    title: '任务id',
    align: 'center',
    dataIndex: 'id',
    ifShow: false,
  },
  {
    title: '研发项目编码',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'rdId_dictText',
    sorter: true,
    fixed: 'left',
  },
  {
    title: '研发项目名称',
    align: 'center',
    dataIndex: 'rdName',
    sorter: true,
  },
  {
    title: '样品编号',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'sampleNo',
    sorter: true,
    fixed: 'left',
  },
  {
    title: '样品名称',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'sampleName',
    sorter: true,
    fixed: 'left',
  },
  {
    title: '部门',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'departName',
    sorter: true,
  },
  {
    title: '检测能力名称',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: '方法',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'standardMethodName',
    sorter: true,
  },
  {
    title: '加急类别',
    align: 'center',
    dataIndex: 'serviceType',
    resizable: true,
    width: 180,
  },
  {
    title: '是否补发项目',
    align: 'center',
    dataIndex: 'isSupplementary',
    resizable: true,
    width: 180,
  },
  {
    title: '检验人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'assignee_dictText',
    sorter: true,
  },
  {
    title: '合作人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'cooper_dictText',
  },
  {
    title: '复核人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'checker_dictText',
    sorter: true,
  },

  {
    title: '历史检验人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'historyOperator',
    sorter: true,
  },

  {
    title: '受理时间',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'receiveDate',
    sorter: true,
  },

  {
    title: '超期时间',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'deadLineDate',
    sorter: true,
  },

  {
    title: '营销产品',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'productId_dictText',
    sorter: true,
  },
  {
    title: '任务状态',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'status_dictText',
    sorter: true,
  },
  {
    title: '当前节点',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'curStep',
    customRender: (opt) => {
      if (allDictItems['flow_step']) {
        const re = allDictItems['flow_step'].find((item) => {
          if (item.value === opt.text) {
            return item;
          }
        });

        return re === undefined ? opt.text : re.text;
      }
      return opt.text;
    },
    sorter: true,
  },

  {
    title: '复测序号',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'retestNo',
    sorter: true,
  },
  {
    title: '稳定性',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'repeatTypeId_dictText',
    sorter: true,
  },
  {
    title: '重复名称',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'repeatName',
    customRender: (opt) => {
      console.log(opt.record.repeatType);
      console.log(allDictItems);
      if (allDictItems[opt.record.repeatType]) {
        console.log(1111);
        const re = allDictItems[opt.record.repeatType].find((item) => {
          if (item.value === opt.record.repeatName) {
            return item;
          }
        });
        return re.text;
      }
      return opt.record.repeatName;
    },
    sorter: true,
  },
  {
    title: '下次稳定性日期',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'nextTestDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
    sorter: true,
  },
  {
    title: '指派人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'assigner_dictText',
    sorter: true,
  },
  {
    title: '指派时间',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'assignTime',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
    sorter: true,
  },
  {
    title: '合同编号',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'contractNo',
    sorter: true,
  },
  {
    title: '分包方',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'subtractorId_dictText',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '任务状态',
    field: 'status',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'test_task_status',
    },
    colProps: { span: 3 },
    defaultValue: '未指派',
  },
  {
    label: '部门',
    field: 'deptId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sys_depart where is_lab = 1,depart_name,id',
    },
    colProps: { span: 4 },
    defaultValue: userStore.getLoginInfo.departs?.map((item) => item.id).join(','),
  },
  {
    label: '检测能力',
    field: 'name',
    component: 'JInput',
    colProps: { span: 3 },
  },
  {
    label: '项目编号',
    field: 'rdId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rd_project,rd_no,id',
    },
    colProps: { span: 3 },
  },
  {
    label: '合同编号',
    field: 'contractNo',
    component: 'JInput',
    colProps: { span: 3 },
  },
  {
    label: '样品编号',
    field: 'sampleNo',
    component: 'JInput',
    colProps: { span: 3 },
  },

  {
    label: '样品名称',
    field: 'sampleName',
    component: 'JInput',
    colProps: { span: 3 },
  },
  {
    label: '方法',
    field: 'methodId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sys_method,name,id',
    },
    colProps: { span: 3 },
  },
  {
    label: '被指派人',
    field: 'assignee',
    component: 'JSelectUser',
    componentProps: {},
    colProps: { span: 4 },
  },
  {
    label: '当前节点',
    field: 'curStep',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sys_workflow_step,name,name',
    },
    defaultValue: 'PM确认',
    colProps: { span: 4 },
  },
  {
    label: '客户',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_customer,name,id',
    },
    colProps: { span: 4 },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '样品',
    field: 'sampleId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sample,name,id',
    },
  },
  {
    label: '营销产品',
    field: 'productId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_product,name,id',
    },
  },
  {
    label: '方法',
    field: 'methodId',
    component: 'JDictSelectTag',
    componentProps: ({ formModel }) => {},
  },
  {
    label: '复测序号',
    field: 'retestNo',
    component: 'Input',
  },
  {
    label: '稳定性',
    field: 'repeatTypeId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_method_repeat_type,cycle_name,id',
    },
  },
  {
    label: '下次稳定性日期',
    field: 'nextTestDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '指派人',
    field: 'assigner',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '指派时间',
    field: 'assignTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '被指派人',
    field: 'assignee',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '当前节点',
    field: 'curStep',
    component: 'Input',
  },
  {
    label: '任务状态',
    field: 'status',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表
export const TColumns: BasicColumn[] = [
  {
    fixed: 'left',
    width: 80,
    title: '测试状态',
    align: 'center',
    resizable: true,
    dataIndex: 'testControlStatus_dictText',
    customRender: ({ text }) => {
      const color = {
        正常: 'success',
        取消: 'error',
        部分取消: 'warning',
        补样中: 'orange',
      };

      return (
        <a-tag color={color[text]} style={{ fontSize: '12px' }}>
          {text}
        </a-tag>
      );
    },
  },
  {
    title: '任务id',
    align: 'center',
    dataIndex: 'id',
    ifShow: false,
  },
  {
    title: '研发项目编码',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'rdId_dictText',
    sorter: true,
    fixed: 'left',
  },
  {
    title: '研发项目名称',
    align: 'center',
    dataIndex: 'rdName',
    resizable: true,
    width: 100,
    sorter: true,
  },
  {
    title: '样品编号',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'sampleNo',
    sorter: true,
    fixed: 'left',
  },
  {
    title: '样品名称',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'sampleName',
    sorter: true,
    fixed: 'left',
  },
  {
    title: '部门',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'departName',
    sorter: true,
  },
  {
    title: '检测能力名称',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: '方法',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'standardMethodName',
    sorter: true,
  },
  {
    title: '加急类别',
    align: 'center',
    dataIndex: 'serviceType',
    resizable: true,
    width: 180,
  },
  {
    title: '是否补发项目',
    align: 'center',
    dataIndex: 'isSupplementary',
    resizable: true,
    width: 180,
  },
  {
    title: '检验人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'assignee_dictText',
    sorter: true,
  },
  {
    title: '合作人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'cooper_dictText',
  },
  {
    title: '复核人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'checker_dictText',
    sorter: true,
  },

  {
    title: '历史检验人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'historyOperator',
    sorter: true,
  },

  {
    title: '受理时间',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'receiveDate',
    sorter: true,
  },

  {
    title: '超期时间',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'deadLineDate',
    sorter: true,
  },

  {
    title: '营销产品',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'productId_dictText',
    sorter: true,
  },
  {
    title: '任务状态',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'status_dictText',
    sorter: true,
  },
  {
    title: '当前节点',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'curStep',
    customRender: (opt) => {
      if (allDictItems['flow_step']) {
        const re = allDictItems['flow_step'].find((item) => {
          if (item.value === opt.text) {
            return item;
          }
        });

        return re === undefined ? opt.text : re.text;
      }
      return opt.text;
    },
    sorter: true,
  },

  {
    title: '复测序号',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'retestNo',
    sorter: true,
  },
  {
    title: '稳定性',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'repeatTypeId_dictText',
    sorter: true,
  },
  {
    title: '重复名称',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'repeatName',
    customRender: (opt) => {
      console.log(opt.record.repeatType);
      console.log(allDictItems);
      if (allDictItems[opt.record.repeatType]) {
        console.log(1111);
        const re = allDictItems[opt.record.repeatType].find((item) => {
          if (item.value === opt.record.repeatName) {
            return item;
          }
        });
        return re.text;
      }
      return opt.record.repeatName;
    },
    sorter: true,
  },
  {
    title: '下次稳定性日期',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'nextTestDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
    sorter: true,
  },
  {
    title: '指派人',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'assigner_dictText',
    sorter: true,
  },
  {
    title: '指派时间',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'assignTime',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
    sorter: true,
  },
  {
    title: '合同编号',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'contractNo',
    sorter: true,
  },
  {
    title: '分包方',
    align: 'center',
    resizable: true,
    width: 100,
    dataIndex: 'subtractorId_dictText',
  },
];
// 高级查询数据
export const superQuerySchema = {
  sampleId: { title: '样品', order: 0, view: 'list', type: 'string', dictTable: 'sample', dictCode: 'id', dictText: 'name' },
  productId: { title: '营销产品', order: 1, view: 'list', type: 'string', dictTable: 'sys_product', dictCode: 'id', dictText: 'name' },
  methodId: { title: '方法', order: 2, view: 'list', type: 'string', dictTable: 'sys_method', dictCode: 'id', dictText: 'name' },
  retestNo: { title: '复测序号', order: 3, view: 'text', type: 'string' },
  repeatTypeId: { title: '稳定性', order: 4, view: 'list', type: 'string', dictCode: '' },
  repeatTypeNextId: {
    title: '下次稳定性',
    order: 5,
    view: 'list',
    type: 'string',
    dictTable: 'sys_method_repeat_type',
    dictCode: 'id',
    dictText: 'cycle_name',
  },
  nextTestDate: { title: '下次稳定性日期', order: 6, view: 'date', type: 'string' },
  standardPrice: { title: '标准价格', order: 7, view: 'number', type: 'number' },
  applyPrice: { title: '申请价格', order: 8, view: 'number', type: 'number' },
  pmPrice: { title: 'PM价格', order: 9, view: 'number', type: 'number' },
  assigner: { title: '指派人', order: 10, view: 'sel_user', type: 'string' },
  assignTime: { title: '指派时间', order: 11, view: 'date', type: 'string' },
  assignee: { title: '被指派人', order: 12, view: 'sel_user', type: 'string' },
  curStep: { title: '当前节点', order: 13, view: 'text', type: 'string' },
  status: { title: '任务状态', order: 14, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
