import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import {useUserStore} from "@/store/modules/user";
const userStore = useUserStore();
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '物品编号',
    align:"center",
    dataIndex: 'articleNo'
   },
   {
    title: '物品类型',
    align:"center",
    dataIndex: 'articleTypeId_dictText'
   },
   {
    title: '数量',
    align:"center",
    dataIndex: 'amount'
   },
   {
    title: '单位',
    align:"center",
    dataIndex: 'unitId_dictText'
   },
   {
    title: '操作原因',
    align:"center",
    dataIndex: 'operationReasonId_dictText'
   },
   {
    title: '处理状态',
    align:"center",
    dataIndex: 'status_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "物品编号",
      field: 'articleNo',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "物品类型",
      field: 'articleTypeId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"warehouse_goods_type"
      },
      //colProps: {span: 6},
 	},
	{
      label: "操作原因",
      field: 'operationReasonId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"warehouse_operation_reason"
      },
      //colProps: {span: 6},
 	},
	{
      label: "处理状态",
      field: 'status',
      component: 'JSearchSelect',
      componentProps:{
         dict:"warehouse_out_apply_state"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '物品编号',
    field: 'articleNo',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入物品编号!'},
          ];
     },
  },
  {
    label: '物品类型',
    field: 'articleTypeId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"warehouse_goods_type"
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入物品类型!'},
          ];
     },
  },
  {
    label: '使用数量(批量时请以,隔开)',
    field: 'amount',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数量,批量的时候请使用逗号隔开!'},
          ];
     },
  },
  {
    label: '操作原因',
    field: 'operationReasonId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"warehouse_operation_reason"
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入操作原因!'},
          ];
     },
  },
  {
    label: '开启人',
    field: 'openerId',
    component: 'Input',
    defaultValue: userStore.getUserInfo.username,
    show:false,
  },
  {
    label: '开启日期',
    field: 'openDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    ifShow: ({ model }) => !model.openDate,
  },
  {
    label: '确认效期',
    field: 'confirmValidDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    ifShow: ({ model }) => !model.confirmValidDate,
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  articleNo: {title: '物品编号',order: 0,view: 'text', type: 'string',},
  articleTypeId: {title: '物品类型',order: 1,view: 'sel_search', type: 'string',dictCode: 'warehouse_goods_type',},
  amount: {title: '数量',order: 2,view: 'text', type: 'string',},
  unitId: {title: '单位',order: 3,view: 'sel_search', type: 'string',dictTable: "sys_unit", dictCode: 'id', dictText: 'unit_name',},
  operationReasonId: {title: '操作原因',order: 4,view: 'sel_search', type: 'string',dictCode: 'warehouse_operation_reason',},
  status: {title: '处理状态',order: 5,view: 'sel_search', type: 'string',dictCode: 'warehouse_out_apply_state',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
