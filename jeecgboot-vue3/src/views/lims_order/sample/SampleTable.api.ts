import { defHttp } from '/src/utils/http/axios';
import { useMessage } from '/src/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_order/sample/list',
  listVo = '/lims_order/sample/listVo',
  save = '/lims_order/sample/add',
  addProduct = '/lims_order/sample/addProduct',
  addCapability = '/lims_order/sample/addCapability',
  edit = '/lims_order/sample/edit',
  editLevel = '/lims_order/sample/editLevel',
  deleteOne = '/lims_order/sample/deleteLevel',
  deleteBatch = '/lims_order/sample/deleteBatch',
  importExcel = '/lims_order/sample/importExcel',
  exportXls = '/lims_order/sample/exportXls',
  listDataLog = '/lims_order/sample/listDataLog',
  receive = '/lims_order/sample/receive',
  cancelSample = '/lims_order/sample/cancelSample',
  resumSample = '/lims_order/sample/resumSample',
  copyOldSample = '/lims_order/sample/copyOldSample',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 列表Vo接口
 * @param params
 */
export const listVo = (params) => defHttp.get({ url: Api.listVo, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};
/**
 * 查询数据日志
 */
export const listDataLog = (params) => defHttp.get({ url: Api.listDataLog, params });
/**
 * 添加产品
 * @param params
 */
export const addProduct = (params, id) => {
  return defHttp.post({ url: Api.addProduct + '/' + id, params });
};
/**
 * 编辑产品
 */
export const editLevel = (params) => {
  return defHttp.post({ url: Api.editLevel, params });
};

/**
 * 接收
 */
export const receive = (params, handleSuccess) => {
  return defHttp.get({ url: Api.receive, params }).then(() => {
    handleSuccess();
  });
};

/**
 * 添加能力
 * @param params
 */
export const addCapability = (params, id) => {
  return defHttp.post({ url: Api.addCapability + '/' + id, params });
};

/**
 * 取消样品
 * @param params
 */
export const cancelSample = (params) => {
  return defHttp.get({ url: Api.cancelSample, params });
};

/**
 * 恢复样品
 * @param params
 */
export const resumSample = (params) => {
  return defHttp.get({ url: Api.resumSample, params });
};

/**
 * 复制旧样品
 * @param params
 */
export const copyOldSample = (params) => {
  return defHttp.get({ url: Api.copyOldSample, params });
};
