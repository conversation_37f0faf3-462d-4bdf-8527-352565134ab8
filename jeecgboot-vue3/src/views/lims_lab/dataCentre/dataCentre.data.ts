import { BasicColumn, FormSchema } from '/@/components/Table';

// 检测结果信息 columns
export const testResultInfoColumns: BasicColumn[] = [
  {
    title: '已提交',
    dataIndex: 'isSubmitted',
    width: 30,
    defaultHidden: true,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '样品编号',
    dataIndex: 'sampleNo',
    width: 150,
    ifShow: false,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '检测指标',
    dataIndex: 'analyte',
    width: 120,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
    slots: { customRender: 'lockIconColumn' },
  },
  {
    title: '实验序号',
    dataIndex: 'trialNo',
    width: 60,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
  },
  {
    title: '结果类型',
    dataIndex: 'resultType',
    width: 80,
    resizable: true,
    ellipsis: true,
    defaultHidden: true,
  },
  {
    title: '标识量',
    dataIndex: 'labelResult',
    width: 80,
    resizable: true,
    ellipsis: true,
    slots: { customRender: 'labelResult' },
    defaultHidden: true,
  },
  {
    title: '检测结果',
    dataIndex: 'rawResult',
    width: 80,
    resizable: true,
    ellipsis: true,
    slots: { customRender: 'rawResult' },
  },
  {
    title: '计量单位',
    dataIndex: 'rawUnit',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '检出限',
    dataIndex: 'lod',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '定量限',
    dataIndex: 'loq',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '平行结果',
    dataIndex: 'dupResult',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '精密度类型',
    dataIndex: 'precisionTypeId',
    width: 80,
    resizable: true,
    ellipsis: true,
    ifShow: false,
  },
  {
    title: '精密度要求',
    dataIndex: 'precisionReq',
    width: 120,
    resizable: true,
    ellipsis: true,
    ifShow: false,
  },
  {
    title: '精密度',
    dataIndex: 'resultPrecision',
    width: 80,
    resizable: true,
    ellipsis: true,
    ifShow: false,
  },
  {
    title: '修约算法',
    dataIndex: 'roundingAlgorithmId',
    width: 80,
    ifShow: false,
  },
  {
    title: '修约方式',
    dataIndex: 'roundingWayId',
    width: 80,
    ifShow: false,
  },
  {
    title: '修约规则',
    dataIndex: 'roundingPrecision',
    width: 80,
    resizable: true,
    ellipsis: true,
    ifShow: false,
  },
  {
    title: '报告结果',
    dataIndex: 'repResult',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '报告单位',
    dataIndex: 'repUnit',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '评价要求',
    dataIndex: 'limit',
    width: 100,
    resizable: true,
    ellipsis: true,
    fixed: 'left',
    slots: { customRender: 'limit' },
  },
  {
    title: '？在报告上',
    dataIndex: 'reportable',
    width: 100,
    resizable: true,
    ellipsis: true,
    fixed: 'right',
    slots: { customRender: 'reportable' },
  },
  {
    title: '结论',
    dataIndex: 'conclusion',
    width: 120,
    resizable: true,
    ellipsis: true,
    fixed: 'right',
    slots: { customRender: 'conclusion' },
  },
  {
    title: '计算公式',
    dataIndex: 'calcExpr',
    width: 80,
    ifShow: false,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '检测人',
    dataIndex: 'tester',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '检测时间',
    dataIndex: 'testerTime',
    width: 100,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '审核人',
    dataIndex: 'checker',
    width: 80,
    resizable: true,
    ellipsis: true,
  },
  {
    title: '审核时间',
    dataIndex: 'checkerTime',
    width: 100,
    resizable: true,
    ellipsis: true,
  },
  {
    title: 'id',
    dataIndex: 'id',
    width: 10,
    ellipsis: true,
    auth: 'super',
    defaultHidden: true,
  },
];

export const reworkApplySchema: FormSchema[] = [
  {
    field: 'reworkReason',
    label: '复测原因',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入复测原因',
      rows: 4,
      maxlength: 200,
      showCount: true,
    },
    rules: [{ required: true, message: '请输入复测原因!' }],
    colProps: { span: 24 },
  },
  {
    field: 'reworkBy',
    label: '复测人',
    component: 'JSelectUser',
    componentProps: {
      labelKey: 'realname',
      rowKey: 'id',
      placeholder: '请选择复测人',
      multiple: false,
      isRadioSelection: true,
    },
    rules: [{ required: true, message: '请选择复测人!' }],
    colProps: { span: 24 },
  },
];
