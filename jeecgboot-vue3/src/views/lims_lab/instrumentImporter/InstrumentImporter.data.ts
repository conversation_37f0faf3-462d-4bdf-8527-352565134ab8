import { JVxeColumn, JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
import { FormSchema } from '@/components/Form';


//列表数据
export const columns: JVxeColumn[] = [
  {
    title: '序号',
    key: 'sortNum',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '试样Id',
    key: 'testId',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '试样编号',
    key: 'sampleNo',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '检测内容',
    key: 'capability',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '检测方法',
    key: 'method',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '试样量',
    key: 'amount',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '定容体积',
    key: 'volume',
    type: JVxeTypes.inputNumber,
    width: 'auto',
    align: 'center',
  },
  {
    title: '稀释倍数',
    key: 'df',
    type: JVxeTypes.inputNumber,
    width: 'auto',
    align: 'center',
  },
  {
    title: '仪器编号',
    key: 'instrumentNo',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
  {
    title: '序列编号',
    key: 'sequenceNo',
    type: JVxeTypes.normal,
    width: 'auto',
    align: 'center',
  },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '仪器',
    field: 'instrumentId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'instrument,code,id',
    },
    colProps: { span: 4 },
  },
  {
    label: '任务条码',
    field: 'taskId',
    component: 'Input',
    colProps: { span: 6 },
  },
];
