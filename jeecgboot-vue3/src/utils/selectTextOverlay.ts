/**
 * 文本选择覆盖层解决方案
 * 通过创建透明的文本覆盖层来实现精确的字符级文本选择
 */

/**
 * 初始化文本选择覆盖层
 */
export function initTextSelectionOverlay() {
  console.log('初始化文本选择覆盖层');
  
  // 添加样式
  addOverlayStyles();
  
  // 监听DOM变化
  observeSelectElements();
  
  // 初始处理现有元素
  processExistingElements();
}

/**
 * 添加覆盖层样式
 */
function addOverlayStyles() {
  const style = document.createElement('style');
  style.id = 'text-selection-overlay-styles';
  style.textContent = `
    /* 隐藏原始文本，但保持布局 */
    .ant-select-selection-item.has-overlay {
      color: transparent !important;
      position: relative;
    }
    
    /* 文本覆盖层样式 */
    .text-selection-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 25px; /* 为下拉箭头留出空间 */
      bottom: 0;
      color: inherit;
      font: inherit;
      line-height: inherit;
      padding: inherit;
      margin: 0;
      border: none;
      background: transparent;
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      cursor: text !important;
      pointer-events: auto !important;
      z-index: 5; /* 降低层级，避免完全覆盖 */
      white-space: nowrap;
      overflow: hidden;
      display: flex;
      align-items: center;
    }

    /* 当鼠标悬停在下拉箭头区域时，降低覆盖层优先级 */
    .ant-select:hover .text-selection-overlay {
      pointer-events: none;
    }

    /* 当用户开始选择文本时，提高覆盖层优先级 */
    .text-selection-overlay.selecting {
      pointer-events: auto !important;
      z-index: 15;
    }
    
    /* 确保覆盖层文本可选择 */
    .text-selection-overlay::selection {
      background: #1890ff;
      color: white;
    }
    
    .text-selection-overlay::-moz-selection {
      background: #1890ff;
      color: white;
    }
  `;
  
  document.head.appendChild(style);
}

/**
 * 监听DOM变化
 */
function observeSelectElements() {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          processSelectElements(element);
        }
      });
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

/**
 * 处理现有元素
 */
function processExistingElements() {
  processSelectElements(document.body);
}

/**
 * 处理选择器元素
 */
function processSelectElements(container: HTMLElement) {
  const selectionItems = container.querySelectorAll('.ant-select-selection-item');
  
  selectionItems.forEach(item => {
    const element = item as HTMLElement;
    if (!element.classList.contains('has-overlay')) {
      createTextOverlay(element);
    }
  });
}

/**
 * 为元素创建文本覆盖层
 */
function createTextOverlay(element: HTMLElement) {
  // 获取原始文本
  const originalText = element.textContent || element.innerText || '';
  
  if (!originalText.trim()) {
    return;
  }
  
  // 标记元素已处理
  element.classList.add('has-overlay');
  
  // 创建覆盖层
  const overlay = document.createElement('div');
  overlay.className = 'text-selection-overlay';
  overlay.textContent = originalText;
  
  // 复制样式
  const computedStyle = window.getComputedStyle(element);
  overlay.style.fontSize = computedStyle.fontSize;
  overlay.style.fontFamily = computedStyle.fontFamily;
  overlay.style.fontWeight = computedStyle.fontWeight;
  overlay.style.color = computedStyle.color;
  overlay.style.lineHeight = computedStyle.lineHeight;
  overlay.style.textAlign = computedStyle.textAlign;
  overlay.style.paddingLeft = computedStyle.paddingLeft;
  overlay.style.paddingRight = computedStyle.paddingRight;
  overlay.style.paddingTop = computedStyle.paddingTop;
  overlay.style.paddingBottom = computedStyle.paddingBottom;
  
  // 添加事件处理
  addOverlayEventHandlers(overlay, element);
  
  // 插入覆盖层
  element.style.position = 'relative';
  element.appendChild(overlay);
  
  console.log('为元素创建了文本覆盖层:', originalText);
}

/**
 * 为覆盖层添加事件处理
 */
function addOverlayEventHandlers(overlay: HTMLElement, originalElement: HTMLElement) {
  let isTextSelecting = false;
  let mouseDownTime = 0;
  let mouseDownX = 0;
  let mouseDownY = 0;

  // 鼠标按下事件
  overlay.addEventListener('mousedown', (e) => {
    mouseDownTime = Date.now();
    mouseDownX = e.clientX;
    mouseDownY = e.clientY;
    isTextSelecting = false;

    // 添加选择状态类
    overlay.classList.add('selecting');

    // 不阻止事件，让文本选择正常进行
  });

  // 鼠标移动事件
  document.addEventListener('mousemove', (e) => {
    if (mouseDownTime > 0) {
      const deltaX = Math.abs(e.clientX - mouseDownX);
      const deltaY = Math.abs(e.clientY - mouseDownY);
      const deltaTime = Date.now() - mouseDownTime;

      // 如果鼠标移动距离超过阈值或按住时间超过阈值，认为是在选择文本
      if (deltaX > 3 || deltaY > 3 || deltaTime > 150) {
        isTextSelecting = true;
        overlay.classList.add('selecting');
      }
    }
  });

  // 鼠标释放事件
  document.addEventListener('mouseup', (e) => {
    if (mouseDownTime > 0) {
      const selection = window.getSelection();
      const hasSelection = selection && selection.toString().length > 0;

      if (hasSelection || isTextSelecting) {
        // 有选中文本或正在选择文本，阻止下拉框打开
        if (e.target === overlay || overlay.contains(e.target as Node)) {
          e.preventDefault();
          e.stopPropagation();
        }
      }

      // 延迟移除选择状态，给文本选择一些时间
      setTimeout(() => {
        overlay.classList.remove('selecting');
        mouseDownTime = 0;
        isTextSelecting = false;
      }, 100);
    }
  });

  // 点击事件
  overlay.addEventListener('click', (e) => {
    const selection = window.getSelection();
    const hasSelection = selection && selection.toString().length > 0;

    if (hasSelection || isTextSelecting) {
      // 有选中文本，阻止下拉框打开
      e.preventDefault();
      e.stopPropagation();
    } else {
      // 没有选中文本，允许下拉框打开
      // 将点击事件传递给原始元素
      const selectElement = originalElement.closest('.ant-select') as HTMLElement;
      if (selectElement) {
        // 临时隐藏覆盖层，让点击事件穿透到下拉框
        overlay.style.pointerEvents = 'none';
        setTimeout(() => {
          overlay.style.pointerEvents = 'auto';
        }, 100);

        // 触发原始元素的点击事件
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          clientX: e.clientX,
          clientY: e.clientY
        });
        selectElement.dispatchEvent(clickEvent);
      }
    }
  });

  // 双击复制
  overlay.addEventListener('dblclick', (e) => {
    e.preventDefault();
    e.stopPropagation();

    const text = overlay.textContent || '';
    if (text) {
      copyText(text);
      showMessage('已复制: ' + text);
    }
  });

  // 右键菜单
  overlay.addEventListener('contextmenu', (e) => {
    // 允许默认的右键菜单
    e.stopPropagation();
  });

  // 键盘事件
  overlay.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'a') {
      // Ctrl+A 全选
      e.preventDefault();
      selectAllText(overlay);
    } else if (e.ctrlKey && e.key === 'c') {
      // Ctrl+C 复制
      const selection = window.getSelection();
      if (selection && selection.toString().length > 0) {
        copyText(selection.toString());
        showMessage('已复制选中文本');
      }
    }
  });
}

/**
 * 全选文本
 */
function selectAllText(element: HTMLElement) {
  const range = document.createRange();
  const selection = window.getSelection();
  
  if (selection) {
    selection.removeAllRanges();
    range.selectNodeContents(element);
    selection.addRange(range);
  }
}

/**
 * 复制文本
 */
async function copyText(text: string) {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text);
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
  }
}

/**
 * 显示消息
 */
function showMessage(message: string) {
  const div = document.createElement('div');
  div.textContent = message;
  div.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  `;
  
  document.body.appendChild(div);
  setTimeout(() => {
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  }, 2000);
}
