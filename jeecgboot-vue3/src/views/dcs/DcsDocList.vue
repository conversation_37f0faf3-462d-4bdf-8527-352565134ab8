<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'dcs:dcs_doc:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" v-auth="'dcs:dcs_doc:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button type="primary" v-auth="'dcs:dcs_doc:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
              <a-menu-item key="2" @click="batchHandleInvokeWorkflow">
                <Icon icon="ant-design:export-outlined" />
                发审
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'dcs:dcs_doc:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <DcsDocModal @register="registerModalDcsDoc" @success="handleSuccess" />
    <DocEditor @register="registerModalDocEditor" />
    <DcsDocOperationLogDrawer @register="registerDrawer" />
  </div>
</template>

<script lang="tsx" name="dcs-dcsDoc" setup>
  import { ref, reactive, h, effect, onMounted } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import DcsDocModal from './components/DcsDocModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './DcsDoc.data';
  import {
    list,
    deleteOne,
    batchDelete,
    getImportUrl,
    getExportUrl,
    invokeOneWorkflow,
    batchInvokeWorkflow,
    review,
    abandon,
    publish,
    getOnlyOfficeEditorConfig,
  } from './DcsDoc.api';
  //import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import DocEditor from '@/views/dcs/components/DocEditor.vue';
  import DcsDocOperationLogDrawer from '@/views/dcs/components/DcsDocOperationLogDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { Modal, Input } from 'ant-design-vue';
  import { queryAllDepartAsDict } from '@/api/common/api';
  const queryParam = reactive<any>({});
  const description = ref('');
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModalDcsDoc, { openModal: openDcsDocModal }] = useModal();
  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: 'dcs_doc',
      api: list,
      columns,
      canResize: false,
      clickToRowSelect: true,
      isTreeTable: true,
      rowClassName: (record) => {
        return record.effectiveStatus === 'abandoned' ? 'abandoned-row' : '';
      },
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [
          ['effectiveDate', ['effectiveDate_begin', 'effectiveDate_end'], 'YYYY-MM-DD'],
          ['abandonDate', ['abandonDate_begin', 'abandonDate_end'], 'YYYY-MM-DD'],
        ],
      },
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: 'dcs_doc',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  const [registerDrawer, { openDrawer }] = useDrawer();

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openDcsDocModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openDcsDocModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openDcsDocModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 发审事件
   */
  async function handleInvokeWorkflow(record) {
    description.value = '';
    console.log(description.value);
    invokeOneWorkflow(record.id, 1, description.value, handleSuccess);
  }

  /**
   * 复审事件
   */
  function handleReview(record) {
    review(record.id, handleSuccess);
  }

  /**
   * 修订事件
   */
  async function handleRevise(record) {
    description.value = '';
    Modal.confirm({
      title: '修订确认',
      content: () => {
        return (
          <div>
            <div style="margin-bottom: 8px;">请输入相关修订备注(可为空)</div>
            <Input.TextArea v-model:value={description.value} placeholder="请输入修订备注" rows={4}></Input.TextArea>
          </div>
        );
      },
      onOk: () => {
        console.log(description.value);
        invokeOneWorkflow(record.id, 2, description.value, handleSuccess);
      },
    });
  }

  /**
   * 批量发审事件
   */
  async function batchHandleInvokeWorkflow() {
    await batchInvokeWorkflow({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    let actions = [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'dcs:dcs_doc:edit',
      },
    ];

    if (record.status === 2 && record.revisedId === null) {
      actions.splice(1, 0, {
        label: '发布',
        onClick: handlePublish.bind(null, record),
        auth: 'dcs:dcs_doc:publish',
      });
    }
    if (record.isChild) {
      actions = [
        {
          label: '阅读',
          onClick: openEditor.bind(null, record),
          auth: 'dcs:dcs_doc:open',
        },
      ];
    }

    return actions;
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    let dropDownActions = [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'dcs:dcs_doc:delete',
      },
      {
        label: '审计',
        onClick: openAuditDrawer.bind(null, record),
        auth: 'dcs:dcs_doc:audit',
      },
    ];
    if (record.status === null) {
      dropDownActions.push({
        label: '发审',
        onClick: handleInvokeWorkflow.bind(null, record),
        auth: 'dcs:dcs_doc:invokeWorkflow',
      });
    }
    if (record.status === 0 && record.revisedId === null) {
      dropDownActions.push({
        label: '复审',
        onClick: handleReview.bind(null, record),
        auth: 'dcs:dcs_doc:review',
      });
      dropDownActions.push({
        label: '修订',
        onClick: handleRevise.bind(null, record),
        auth: 'dcs:dcs_doc:revise',
      });
    }

    if (record.status === 0 && record.revisedId === null) {
      dropDownActions.push({
        label: '作废',
        onClick: handleAbandon.bind(null, record),
        auth: 'dcs:dcs_doc:abandon',
      });
    }
    if (record.permission) {
      const perms = record.permission.split(',');
      if (perms.includes('read')) {
        dropDownActions.push({
          label: '阅读',
          onClick: openEditor.bind(null, record),
          auth: 'dcs:dcs_doc:open',
        });
      }
    }
    if (record.isChild) {
      dropDownActions = [];
    }
    return dropDownActions;
  }

  const openEditor = async (record: Recordable) => {
    const ooConfig = await getOnlyOfficeEditorConfig(record.id, record.permission);
    openDocEditorModal(true, ooConfig);
  };

  const handleAbandon = async (record: Recordable) => {
    await abandon(record.id, handleSuccess);
  };

  const handlePublish = async (record: Recordable) => {
    await publish(record.id, handleSuccess);
  };

  const openAuditDrawer = async (record: Recordable) => {
    openDrawer(true, record.id);
  };

  /**
   * 初始化部门字典配置
   */
  function initDeprtDictConfig() {
    queryAllDepartAsDict().then((res) => {
      if (res) {
        const allDictData = userStore.getAllDictItems;
        if (!allDictData['sys_depart']) {
          userStore.setAllDictItems({ ...allDictData, sys_depart: res });
        }
      }
    });
  }
  initDeprtDictConfig();

  onMounted(() => {
    let { setFieldsValue } = getForm();
    setFieldsValue({ effectiveStatus: 'effective' });
  });
</script>

<style lang="less" scoped>
  :deep(.abandoned-row) {
    color: red !important; // 添加 !important 确保样式优先级
  }

  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
