<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <BasicForm @register="registerForm" name="DcsDocForm" @change="onFieldChange" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, watch } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../DcsDoc.data';
  import { saveOrUpdate } from '../DcsDoc.api';
  import { initDictOptions } from '@/utils/dict';
  import { useUserStore } from '/@/store/modules/user';
  import { UserInfo } from '/#/store';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  //表单配置
  const [registerForm, { setProps, resetFields, getFieldsValue, setFieldsValue, updateSchema, validate, scrollToField }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    const dictData = await initDictOptions('system_doc_type,type_name,id');
    console.log('====ddd==', dictData);
    let docType = null;
    if (data.isUpdate) {
      docType = dictData.find((x) => x.value === data.record.typeId);
      await updateSchema({
        field: 'url', // `url` 对应 JUpload 的字段名
        componentProps: {
          bizPath: 'dcs/' + docType.text, // 根据 typeId 动态设置路径
        },
      });
    } else {
      const typeIdSchema = {
        field: 'typeId',
        componentProps: ({ formModel }) => {
          return {
            dict: 'system_doc_type,type_name,id',
            pidField: 'parent_id',
            hasChildField: 'has_child',
            pidValue: '0',
            treeDefaultExpandAll: true,
            onChange: async (e: ChangeEvent) => {
              if (e) {
                docType = dictData.find((x) => x.value === e);
                if (docType) {
                  await updateSchema({
                    field: 'url', // `url` 对应 JUpload 的字段名
                    componentProps: {
                      bizPath: 'dcs/' + docType.text, // 根据 typeId 动态设置路径
                    },
                  });
                }
              }
            },
          };
        },
      };
      await updateSchema(typeIdSchema);
    }
    if (!!data?.isUpdate) {
      const userInfo: UserInfo = useUserStore().getUserInfo;
      console.log('data===>', data);
      const userId = userInfo?.id;
      if (userId != data.record.createBy) {
        // 只有创建人才可以修改url内容
        if (data.record.processInstanceId) {
          //已经启动了审批流程
          await updateSchema({
            field: 'url',
            componentProps: {
              showUploadList: false,
            },
          });
        }
      }
    }
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  const onFieldChange = (event) => {
    console.log('event', event);
  };
  // watch(
  //   () => {
  //     let formValues = getFieldsValue() || {}; // 确保 getFieldsValue() 始终返回一个对象
  //     console.log('formValues',formValues);
  //     return formValues.typeId; // 访问 typeId 时确保它有值
  //   },
  //   async (typeId) => {
  //     if (typeId !== undefined && typeId !== null) {
  //       const dictData = await initDictOptions('system_doc_type,type_name,id');
  //       console.log('======', dictData);
  //       let docType = dictData.find((x) => x.value === typeId);
  //       if (docType) {
  //         updateSchema({
  //           field: 'url', // `url` 对应 JUpload 的字段名
  //           componentProps: {
  //             bizPath: 'dcs/' + docType.text, // 根据 typeId 动态设置路径
  //           },
  //         });
  //       }
  //     }
  //   },
  //   { immediate: true }
  // );
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
