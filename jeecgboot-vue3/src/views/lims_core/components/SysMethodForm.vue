<template>
  <div>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <!--主表区域 -->
      <a-tab-pane tab="方法" :key="refKeys[0]" :forceRender="true" :style="tabsStyle">
        <BasicForm @register="registerForm" ref="formRef" />
      </a-tab-pane>
      <!--子表单区域 -->
      <a-tab-pane tab="试剂耗材" key="sysMethodConsumptive" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodConsumptive"
          v-if="sysMethodConsumptiveTable.show"
          :loading="sysMethodConsumptiveTable.loading"
          :columns="sysMethodConsumptiveTable.columns"
          :dataSource="sysMethodConsumptiveTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
      <a-tab-pane tab="标品" key="sysMethodStdMaterial" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodStdMaterial"
          v-if="sysMethodStdMaterialTable.show"
          :loading="sysMethodStdMaterialTable.loading"
          :columns="sysMethodStdMaterialTable.columns"
          :dataSource="sysMethodStdMaterialTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
      <a-tab-pane tab="设备类别" key="sysMethodInstrumentType" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodInstrumentType"
          v-if="sysMethodInstrumentTypeTable.show"
          :loading="sysMethodInstrumentTypeTable.loading"
          :columns="sysMethodInstrumentTypeTable.columns"
          :dataSource="sysMethodInstrumentTypeTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
      <a-tab-pane tab="实验过程参数" key="sysMethodTestingPara" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodTestingPara"
          v-if="sysMethodTestingParaTable.show"
          :loading="sysMethodTestingParaTable.loading"
          :columns="sysMethodTestingParaTable.columns"
          :dataSource="sysMethodTestingParaTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
      <a-tab-pane tab="重复性" key="sysMethodRepeatType" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodRepeatType"
          v-if="sysMethodRepeatTypeTable.show"
          :loading="sysMethodRepeatTypeTable.loading"
          :columns="sysMethodRepeatTypeTable.columns"
          :dataSource="sysMethodRepeatTypeTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
      <a-tab-pane tab="检测流程" key="sysMethodWorkflow" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodWorkflow"
          v-if="sysMethodWorkflowTable.show"
          :loading="sysMethodWorkflowTable.loading"
          :columns="sysMethodWorkflowTable.columns"
          :dataSource="sysMethodWorkflowTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
      <a-tab-pane tab="检测指标" key="sysMethodAnalyte" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysMethodAnalyte"
          v-if="sysMethodAnalyteTable.show"
          :loading="sysMethodAnalyteTable.loading"
          :columns="sysMethodAnalyteTable.columns"
          :dataSource="sysMethodAnalyteTable.dataSource"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
        />
      </a-tab-pane>
    </a-tabs>

    <div style="width: 100%; text-align: center; margin-top: 10px" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defHttp } from '/@/utils/http/axios';
  import { ref, computed, unref, reactive, onMounted, defineProps } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import {
    formSchema,
    sysMethodConsumptiveColumns,
    sysMethodStdMaterialColumns,
    sysMethodInstrumentTypeColumns,
    sysMethodTestingParaColumns,
    sysMethodRepeatTypeColumns,
    sysMethodWorkflowColumns,
    sysMethodAnalyteColumns,
  } from '../SysMethod.data';
  import {
    saveOrUpdate,
    sysMethodConsumptiveList,
    sysMethodStdMaterialList,
    sysMethodInstrumentTypeList,
    sysMethodTestingParaList,
    sysMethodRepeatTypeList,
    sysMethodWorkflowList,
    sysMethodAnalyteList,
  } from '../SysMethod.api';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  const refKeys = ref([
    'sysMethod',
    'sysMethodConsumptive',
    'sysMethodStdMaterial',
    'sysMethodInstrumentType',
    'sysMethodTestingPara',
    'sysMethodRepeatType',
    'sysMethodWorkflow',
    'sysMethodAnalyte',
  ]);
  const activeKey = ref('sysMethod');
  const sysMethodConsumptive = ref();
  const sysMethodStdMaterial = ref();
  const sysMethodInstrumentType = ref();
  const sysMethodTestingPara = ref();
  const sysMethodRepeatType = ref();
  const sysMethodWorkflow = ref();
  const sysMethodAnalyte = ref();
  const tableRefs = {
    sysMethodConsumptive,
    sysMethodStdMaterial,
    sysMethodInstrumentType,
    sysMethodTestingPara,
    sysMethodRepeatType,
    sysMethodWorkflow,
    sysMethodAnalyte,
  };
  const sysMethodConsumptiveTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodConsumptiveColumns,
    show: false,
  });
  const sysMethodStdMaterialTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodStdMaterialColumns,
    show: false,
  });
  const sysMethodInstrumentTypeTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodInstrumentTypeColumns,
    show: false,
  });
  const sysMethodTestingParaTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodTestingParaColumns,
    show: false,
  });
  const sysMethodRepeatTypeTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodRepeatTypeColumns,
    show: false,
  });
  const sysMethodWorkflowTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodWorkflowColumns,
    show: false,
  });
  const sysMethodAnalyteTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodAnalyteColumns,
    show: false,
  });

  const props = defineProps({
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });
  const formDisabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      }
    }
    return true;
  });
  // 是否显示提交按钮
  const showFlowSubmitButton = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return true;
      }
    }
    return false;
  });

  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  onMounted(() => {
    initFormData();
  });
  //渲染流程表单数据
  const queryByIdUrl = '/lims_core/sysMethod/queryById';
  async function initFormData() {
    if (props.formBpm === true) {
      await reset();
      let params = { id: props.formData.dataId };
      const data = await defHttp.get({ url: queryByIdUrl, params });
      //表单赋值
      await setFieldsValue({
        ...data,
      });
      requestSubTableData(sysMethodConsumptiveList, { id: data.id }, sysMethodConsumptiveTable, () => {
        sysMethodConsumptiveTable.show = true;
      });
      requestSubTableData(sysMethodStdMaterialList, { id: data.id }, sysMethodStdMaterialTable, () => {
        sysMethodStdMaterialTable.show = true;
      });
      requestSubTableData(sysMethodInstrumentTypeList, { id: data.id }, sysMethodInstrumentTypeTable, () => {
        sysMethodInstrumentTypeTable.show = true;
      });
      requestSubTableData(sysMethodTestingParaList, { id: data.id }, sysMethodTestingParaTable, () => {
        sysMethodTestingParaTable.show = true;
      });
      requestSubTableData(sysMethodRepeatTypeList, { id: data.id }, sysMethodRepeatTypeTable, () => {
        sysMethodRepeatTypeTable.show = true;
      });
      requestSubTableData(sysMethodWorkflowList, { id: data.id }, sysMethodWorkflowTable, () => {
        sysMethodWorkflowTable.show = true;
      });
      requestSubTableData(sysMethodAnalyteList, { id: data.id }, sysMethodAnalyteTable, () => {
        sysMethodAnalyteTable.show = true;
      });
      // 隐藏底部时禁用整个表单
      setProps({ disabled: formDisabled.value });
    }
  }

  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );
  // 弹窗tabs滚动区域的高度
  const tabsStyle = computed(() => {
    let height: Nullable<string> = null;
    let minHeight = '100px';
    // 弹窗wrapper
    let overflow = 'auto';
    return { height, minHeight, overflow };
  });

  async function reset() {
    await resetFields();
    activeKey.value = 'sysMethod';
    sysMethodConsumptiveTable.dataSource = [];
    sysMethodStdMaterialTable.dataSource = [];
    sysMethodInstrumentTypeTable.dataSource = [];
    sysMethodTestingParaTable.dataSource = [];
    sysMethodRepeatTypeTable.dataSource = [];
    sysMethodWorkflowTable.dataSource = [];
    sysMethodAnalyteTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);
    return {
      ...main, // 展开
      sysMethodConsumptiveList: allValues.tablesValue[0].tableData,
      sysMethodStdMaterialList: allValues.tablesValue[1].tableData,
      sysMethodInstrumentTypeList: allValues.tablesValue[2].tableData,
      sysMethodTestingParaList: allValues.tablesValue[3].tableData,
      sysMethodRepeatTypeList: allValues.tablesValue[4].tableData,
      sysMethodWorkflowList: allValues.tablesValue[5].tableData,
      sysMethodAnalyteList: allValues.tablesValue[6].tableData,
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    //提交表单
    await saveOrUpdate(values, true);
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>

<style lang="less">
  // Online表单Tab风格专属样式
  .j-cgform-tab-modal {
    .ant-modal-header {
      padding-top: 8px;
      padding-bottom: 8px;
      border-bottom: none !important;
    }

    .ant-modal .ant-modal-body > .scrollbar,
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-tabs-top-bar {
      width: calc(100% - 55px);
      position: relative;
      left: -14px;
    }

    .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
      overflow: hidden auto;
    }
  }
</style>
