import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_order/quotation/list',
  apply = '/lims_order/quotation/apply',
  saveVo = '/lims_order/quotation/addVo',
  save = '/lims_order/quotation/add',
  edit = '/lims_order/quotation/edit',
  deleteOne = '/lims_order/quotation/delete',
  deleteBatch = '/lims_order/quotation/deleteBatch',
  importExcel = '/lims_order/quotation/importExcel',
  exportXls = '/lims_order/quotation/exportXls',
  unlock = '/lims_order/quotation/unlock',
  listDataLog = '/lims_order/quotation/listDataLog',
  geneOneQuotion = '/oo/generateQuotation',
  batchGeneQuotion = '/oo/batchGenerateQuotations',
  getOOEditorConfig = '/oo/editor-config',
  handOverToReceive = '/lims_order/quotation/handOverToReceive',
  preCheck = '/lims_order/quotation/preCheck',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
};
/**
 * 保存或者更新
 * @param params
 */
export const saveVo = (params, isUpdate) => {
  return defHttp.post({ url: Api.edit, params });
};

/**
 * 发起审批
 */
export const apply = (params, handleSuccess) => {
  return defHttp.get({ url: Api.apply, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 解锁
 */
export const unlock = (params, handleSuccess) => {
  return defHttp.get({ url: Api.unlock, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 查询数据日志列表
 */
export const listDataLog = (params) => defHttp.get({ url: Api.listDataLog, params });

/**
 * 批量生成报价单
 * @param params
 */
export const batchGeneQuotion = (params) => {
  return defHttp.post({ url: Api.batchGeneQuotion, params }, { joinParamsToUrl: true });
};

/**
 * 生成报价单
 * @param params
 */
export const geneOneQuotion = (params) => {
  return defHttp.post({ url: Api.geneOneQuotion, params }, { joinParamsToUrl: true });
};

export const getOOEditorConfig = async (Id: String, biz: String) => {
  return await defHttp.get({ url: Api.getOOEditorConfig, params: { biz: biz, bizId: Id, permission: null } });
};


/**
 * 流转到检品接收
 * @param params
 */
export const handOverToReceive = (params ,handleSuccess) => {
  return defHttp.get({ url: Api.handOverToReceive, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 转合同检查
 * @param params
 */
export const preCheck = (params) => defHttp.get({ url: Api.preCheck, params });
