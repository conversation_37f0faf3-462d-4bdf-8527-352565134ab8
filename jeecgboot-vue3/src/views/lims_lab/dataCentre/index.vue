<template>
  <div class="data-centre-container" style="height: 100%; width: 100%; display: flex">
    <!-- 左侧树形菜单 -->
    <div style="width: 280px; height: 100%; padding: 16px 8px 16px 16px">
      <a-card :bordered="false" style="height: 100%">
        <TestTree ref="testTreeRef" @select="onTreeSelect" />
      </a-card>
    </div>

    <!-- 右侧内容区域 -->
    <div style="flex: 1; height: 100%; padding: 16px 16px 16px 8px; overflow: hidden">
      <a-card :bordered="false" style="height: 100%; width: 100%">
        <a-tabs v-model:activeKey="activeTabKey" style="height: 100%">
          <a-tab-pane tab="检测结果" key="test-result">
            <TestResultInfoTab :curTestIds="curTestsData" ref="testResultInfoTabRef" @save="onSave" @submit="onSubmit" />
          </a-tab-pane>
          <a-tab-pane tab="线性" key="linearity">
            <LinearityInfoTab :curTestIds="curTestsData" :qc-types="'SST,STD'" :isActive="activeTabKey === 'linearity'" />
          </a-tab-pane>
          <a-tab-pane tab="检测限、定量限" key="lodloq">
            <LinearityInfoTab :curTestIds="curTestsData" :qc-types="'LOD,LOQ'" :isActive="activeTabKey === 'lodloq'" />
          </a-tab-pane>
          <a-tab-pane tab="准确度" key="accuracy">
            <LinearityInfoTab :curTestIds="curTestsData" :qc-types="'ACC'" :isActive="activeTabKey === 'accuracy'" />
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts" setup name="test-result">
  import { provide, ref } from 'vue';
  import { useDesign } from '/@/hooks/web/useDesign';

  import TestTree from './components/TestTree.vue';
  import TestResultInfoTab from './components/TestResultInfoTab.vue';
  import LinearityInfoTab from '@/views/lims_lab/dataCentre/components/LinearityInfoTab.vue';

  const { prefixCls } = useDesign('test-result');
  provide('prefixCls', prefixCls);

  // 当前选中的部门信息
  let curTestsData = ref('');
  // 引用TestResultInfoTab组件
  const testResultInfoTabRef = ref();
  const testTreeRef = ref();
  // 当前激活的标签页
  const activeTabKey = ref('test-result');

  // 左侧树选择后触发
  async function onTreeSelect(data) {
    console.log('data', data);

    // 检查是否有未保存的数据
    if (testResultInfoTabRef.value) {
      const canContinue = await testResultInfoTabRef.value.checkUnsavedData();
      if (!canContinue) {
        return; // 用户取消了操作
      }
    }

    // 更新当前选中的测试ID
    if (data && data.length > 0) {
      // 只有当有数据时才设置ID
      curTestsData.value = data.map((item) => item.id).join(',');
    } else {
      // 没有数据时清空
      curTestsData.value = '';
    }
  }

  function onSave({ testIds, statuses }) {
    if (testTreeRef.value && testTreeRef.value.updateNodeStatuses) {
      testTreeRef.value.updateNodeStatuses(testIds, statuses);
    }
  }

  function onSubmit({ testIds, statuses }) {
    if (testTreeRef.value && testTreeRef.value.updateNodeStatuses) {
      testTreeRef.value.updateNodeStatuses(testIds, statuses);
    }
  }
</script>

<style lang="less">
  @import './index.less';
</style>
