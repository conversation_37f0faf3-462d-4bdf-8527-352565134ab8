<template>
  <div class="document-container">
    <div class="document-viewer">
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载文档...</div>
      </div>
      <div class="editor-container">
        <DocumentEditor
          v-if="documentServerUrl && documentUrl"
          :id="editorId"
          ref="docEditorRef"
          :documentServerUrl="documentServerUrl"
          :config="editorConfig"
          :events_onDocumentReady="onDocumentReady"
          :events_onError="onError"
        />
      </div>
      <!-- 根据传入的按钮配置动态渲染浮动按钮 -->
      <template v-if="floatButtons && floatButtons.length > 0">
        <FloatButton
          v-for="(btn, index) in floatButtons"
          :key="index"
          :tooltip="btn.tooltip"
          type="primary"
          class="doc-float-button"
          :style="{
            right: '24px',
            bottom: `${24 + index * 60}px`,
          }"
          @click="handleButtonClick(btn)"
        >
          <template #icon>
            <component :is="getIconComponent(btn.icon)" />
          </template>
        </FloatButton>
      </template>
      <div v-if="error" class="error-message">
        <h3>加载文档时出错</h3>
        <p>{{ error }}</p>
      </div>
    </div>
  </div>
  <MailModal @register="registerMailModal" />
</template>

<script setup lang="ts">
  import { ref, onMounted, watch } from 'vue';
  import { DocumentEditor } from '@onlyoffice/document-editor-vue';
  import { FloatButton } from 'ant-design-vue';
  import * as Icons from '@ant-design/icons-vue';
  import MailModal from '@/views/dcs/components/mail/MailModal.vue';
  import { useModal } from '@/components/Modal';
  import { sign } from '/@/views/lims_lab/reportCentre/Report.api';
  // 定义按钮配置类型
  interface FloatButtonConfig {
    tooltip: string;
    icon: string | any;
    type?: string; // 使用字符串类型，兼容 ant-design-vue 的 FloatButton 组件
    action: string;
  }

  // 获取图标组件
  const getIconComponent = (icon: string | any) => {
    // 如果已经是组件对象，直接返回
    if (typeof icon !== 'string') {
      return icon;
    }

    // 如果是字符串，从Icons中获取对应的组件
    return Icons[icon];
  };

  // 定义属性
  const props = defineProps({
    // reportId
    documentId: {
      type: String,
    },
    // 文档服务器URL
    documentServerUrl: {
      type: String,
    },
    // 文档URL
    documentUrl: {
      type: String,
      required: true,
    },
    // 文档标题
    documentTitle: {
      type: String,
      default: '',
    },
    // 文档类型
    documentType: {
      type: String,
      default: 'word', // 可选值: word, cell, slide
    },
    // 是否只读模式
    readOnly: {
      type: Boolean,
      default: true,
    },
    // 编辑器ID
    editorId: {
      type: String,
      default: () => `editor-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    },
    // 显示器ID
    screenId: {
      type: String,
      default: 'primary',
    },
    // 编辑器配置 - 从父组件传入，通过后端接口获取
    editorConfig: {
      type: Object,
      required: true,
    },
    floatButtons: {
      type: Array as () => FloatButtonConfig[],
      default: () => [],
    },
  });
  // 定义事件
  const emit = defineEmits(['register', 'buttonClick']);
  const docEditorRef = ref();
  // 状态变量
  const loading = ref(true);
  const error = ref('');
  const documentLoaded = ref(false);

  const [registerMailModal, { openModal: openMailModal }] = useModal();

  // 直接使用父组件传入的编辑器配置
  console.log('使用父组件传入的编辑器配置:', props.editorConfig);

  // 事件处理函数
  const onDocumentReady = () => {
    console.log('文档已准备就绪:', props.documentUrl);
    loading.value = false;
    documentLoaded.value = true;
    // 获取iframe 设置height=100%
    const iframe = document.querySelector(`iframe`);
    if (iframe) {
      iframe.height = '100%';
      console.log('已设置iframe高度为100%');
    } else {
      console.warn('未找到iframe元素，无法设置高度');
    }
  };

  const onError = (event: any) => {
    console.error('文档加载错误:', event);
    error.value = `加载文档时出错: ${event.message || '未知错误'}`;
    loading.value = false;
  };

  // 生命周期钩子
  onMounted(() => {
    console.log('DocumentViewer 已挂载, 文档URL:', props.documentUrl);
    console.log('编辑器配置:', props.editorConfig);
    console.log('显示器ID:', props.screenId);
    console.log(props.floatButtons);
  });

  // 监听文档URL变化
watch(
  () => props.documentUrl,
  (newUrl, oldUrl) => {
    console.log('watch 触发 - 旧URL:', oldUrl);
    console.log('watch 触发 - 新URL:', newUrl);
    if (newUrl) {
      loading.value = true;
      error.value = '';
      documentLoaded.value = false;
      console.log('文档URL已更改:', newUrl);
    }
  },
  { immediate: true } // 添加 immediate 选项确保首次加载也会触发
);
  // 处理按钮点击事件
  const handleButtonClick = (btn: FloatButtonConfig) => {
    // 获取编辑器实例
    const editorInstance = docEditorRef.value;

    // 提取当前key的数字部分
    let allKeys: string[] = [];
    const key = props.editorConfig?.document?.key;
    // 只有当key存在时才进行处理
    if (key) {
      const match = key.match(/^report(\d+)_/);
      if (match) allKeys = [match[1]];
    }
    // 触发外部事件
    emit('buttonClick', {
      action: btn.action,
      editorInstance,
      allKeys,
    });

    // 如果是邮件按钮，保留原有逻辑
    if (btn.action === 'email') {
      handleEmail();
    }
  };
  // 处理导出按钮点击事件
  const handleEmail = () => {
    // 获取编辑器实例
    const editorInstance = docEditorRef.value;
    if (editorInstance) {
      // 确保editorConfig中存在editorConfig.plugins.options.all
      const pluginsOptions = props.editorConfig?.editorConfig?.plugins?.options?.all;
      openMailModal(true, pluginsOptions);
      console.log('发送邮件');
    }
  };
</script>

<style scoped>
  .document-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: white;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .editor-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .document-viewer {
    flex: 1;
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .document-editor {
    flex: 1;
    width: 100%;
    height: 100%;
    min-height: 400px;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #42b983;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
  }

  .loading-text {
    font-size: 16px;
    color: #333;
  }

  .error-message {
    padding: 20px;
    background-color: #fff0f0;
    border: 1px solid #ffcaca;
    border-radius: 4px;
    margin: 20px;
    color: #d83030;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
</style>
