import { defHttp } from '/@/utils/http/axios';

enum Api {
  getSystemDocTypeDeptPermission = '/dcs/systemDocTypeDeptPermission/get',
  saveSystemDocTypeDeptPermission = '/dcs/systemDocTypeDeptPermission/save',
}

/**
 * 获取审批流id
 * @param id 类型id
 */
export const getSystemDocTypeDeptPermission = (typeId) => defHttp.get({ url: Api.getSystemDocTypeDeptPermission, params: { typeId } });


/**
 * 保存或者更新
 * @param params
 */
export const saveSystemDocTypeDeptPermission = (params) => {
  return defHttp.post({ url: Api.saveSystemDocTypeDeptPermission, params });
};
