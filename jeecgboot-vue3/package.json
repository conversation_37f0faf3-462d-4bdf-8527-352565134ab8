{"name": "jeecgboot-vue3", "version": "3.7.1", "author": {"name": "广州国标检验检测有限公司", "url": "https://gitlab.guobiaotest.com:9080/lims/iTest/-/tree/main/jeecgboot-vue3?ref_type=heads"}, "scripts": {"pinstall": "pnpm install", "clean:cache": "rimraf node_modules/.cache/ && rimraf node_modules/.vite", "dev": "vite", "build": "cross-env NODE_ENV=production NODE_OPTIONS=--max-old-space-size=8192 vite build && esno ./build/script/postBuild.ts", "build:deploy": "npm run build && node ./scripts/deploy-fix.js", "build:report": "pnpm clean:cache && cross-env REPORT=true npm run build", "preview": "npm run build && vite preview", "reinstall": "rimraf pnpm-lock.yaml && rimraf yarn.lock && rimraf package.lock.json && rimraf node_modules && npm run install", "clean:lib": "rimraf node_modules", "gen:icon": "esno ./build/generate/icon/index.ts", "batch:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "upgrade:log": "conventional-changelog -p angular -i CHANGELOG.md -s", "husky:install": "husky install"}, "dependencies": {"@ant-design/colors": "^7.0.2", "@ant-design/icons-vue": "^7.0.1", "@iconify/iconify": "^3.1.1", "@jeecg/online": "3.7.1-<PERSON>", "@onlyoffice/document-editor-vue": "^1.5.0", "@tinymce/tinymce-vue": "4.0.7", "@traptitech/markdown-it-katex": "^3.6.0", "@vant/area-data": "^1.5.1", "@vue/shared": "^3.4.19", "@vueuse/core": "^10.8.0", "@vxe-ui/plugin-render-antd": "^4.0.13", "@zxcvbn-ts/core": "^3.0.4", "ant-design-vue": "^4.1.2", "axios": "^1.6.7", "china-area-data": "^5.0.1", "clipboard": "^2.0.11", "codemirror": "^5.65.3", "cron-parser": "^4.9.0", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dom-align": "^1.12.4", "echarts": "^5.4.3", "emoji-mart-vue-fast": "^15.0.1", "enquire.js": "^2.1.6", "event-source-polyfill": "^1.0.31", "highlight.js": "^11.11.1", "intro.js": "^7.2.0", "lodash-es": "^4.17.21", "lodash.get": "^4.4.2", "markdown-it": "^14.0.0", "markdown-it-link-attributes": "^4.0.1", "mathjs": "^14.4.0", "md5": "^2.3.0", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "pinia": "2.1.7", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "resize-observer-polyfill": "^1.5.1", "showdown": "^2.1.0", "sortablejs": "^1.15.2", "tinymce": "6.6.2", "vditor": "^3.9.9", "vue": "^3.4.19", "vue-cropper": "^0.6.4", "vue-cropperjs": "^5.0.0", "vue-i18n": "^9.9.1", "vue-infinite-scroll": "^2.0.2", "vue-plugin-hiprint": "0.0.58-fix", "vue-print-nb-jeecg": "^1.0.12", "vue-router": "^4.3.0", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.6.19", "vxe-table": "^4.13.37", "xe-utils": "3.7.5", "xss": "^1.0.14"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.2", "@iconify/json": "^2.2.185", "@purge-icons/generated": "^0.10.0", "@rys-fe/vite-plugin-theme": "^0.8.6", "@types/codemirror": "^5.60.15", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/intro.js": "^5.1.5", "@types/jest": "^29.5.12", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^20.11.19", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.11", "@types/showdown": "^2.0.6", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "@vitejs/plugin-vue": "^4.3.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.4.4", "@vue/test-utils": "^2.4.4", "autoprefixer": "^10.4.17", "commitizen": "^4.3.0", "conventional-changelog-cli": "^4.1.0", "cross-env": "^7.0.3", "cz-git": "^1.8.0", "czg": "^1.8.0", "dotenv": "^16.3.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "esno": "^4.0.0", "fs-extra": "^11.2.0", "http-server": "^14.1.1", "husky": "^8.0.3", "inquirer": "^9.2.15", "is-ci": "^3.0.1", "jest": "^29.7.0", "less": "^4.2.0", "lint-staged": "15.2.2", "npm-run-all": "^4.1.5", "picocolors": "^1.0.0", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "postcss-less": "^6.0.0", "prettier": "^3.2.5", "pretty-quick": "^4.0.0", "rimraf": "^5.0.5", "rollup": "^4.12.0", "rollup-plugin-visualizer": "^5.12.0", "sharp": "^0.34.1", "stylelint": "^16.2.1", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recommended": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^4.9.5", "unocss": "^0.58.5", "vite": "^5.2.11", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mkcert": "^1.17.3", "vite-plugin-mock": "^2.9.6", "vite-plugin-optimize-persist": "^0.1.2", "vite-plugin-package-config": "^0.1.1", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend-plus": "^0.1.0", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^1.8.27", "workbox-window": "^7.3.0"}, "repository": {"type": "git", "url": "git+https://gitlab.guobiaotest.com:9080/lims/iTest.git"}, "license": "MIT", "bugs": {"url": "https://gitlab.guobiaotest.com:9080/lims/iTest/-/issues"}, "homepage": "https://lims.guobiaotest.com:3380/", "engines": {"node": "^18 || >=20"}, "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab"}