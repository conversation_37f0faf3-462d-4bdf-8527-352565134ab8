<template>
  <BasicModal
    ref="modalRef"
    destroyOnClose
    wrapClassName="j-cgform-tab-modal"
    v-bind="$attrs"
    @register="registerCopyModal"
    :width="896"
    :maskClosable="false"
    @ok="handleSubmit"
  >
    <!-- 标题区域 -->
    <template #title>
      <div class="titleArea">
        <div class="title">{{ title }}</div>
        <div class="right">
          <a-radio-group v-model:value="activeKey">
            <template v-for="(item, index) in tabNav" :key="index">
              <a-radio-button :value="item.tableName">{{ item.tableTxt }}</a-radio-button>
            </template>
          </a-radio-group>
        </div>
      </div>
    </template>
    <!--表单区域 -->
    <div class="contentArea">
      <!--主表区域 -->
      <BasicForm @register="registerForm" ref="formRef" v-show="activeKey == refKeys[0]" name="SysStandardForm" />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, onMounted, onUnmounted } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../SysStandardCopy.data';
  import { clonestandard } from '../SysStandard.api';
  import { initDictOptions } from '@/utils/dict';
  import { useSuperSubscript } from '@/hooks/setting';

  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const modalRef = ref();
  const refKeys = ref(['sysStandard', 'sysStandardEvaluationLimt']);
  const tabNav = ref<any>([
    { tableName: 'sysStandard', tableTxt: '标准' },
  ]);
  const activeKey = ref('sysStandard');
  const sysStandardEvaluationLimt = ref();
  const tableRefs = { sysStandardEvaluationLimt };
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  //表单赋值
  const [registerCopyModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });

  const { toSuperscript, toSubscript } = useSuperSubscript();

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));
  //重置
  async function reset() {
    await resetFields();
    activeKey.value = 'sysStandard';
  }
  function getSelectedText(inputElement) {
    if (!inputElement) return '';

    const start = inputElement.selectionStart;
    const end = inputElement.selectionEnd;

    if (start !== end) {
      return inputElement.value.substring(start, end);
    }

    return '';
  }

  function replaceSelectedTextWithSuperscript(inputElement, selectedText) {
    if (!inputElement || !selectedText) return;

    const start = inputElement.selectionStart;
    const end = inputElement.selectionEnd;
    const value = inputElement.value;

    // 使用你已有的 toSuperscript 函数
    const superscriptText = toSuperscript(selectedText);

    // 替换文字
    const newValue = value.substring(0, start) + superscriptText + value.substring(end);

    // 更新输入框值
    inputElement.value = newValue;

    // 触发输入事件以更新数据
    inputElement.dispatchEvent(new Event('input', { bubbles: true }));

    // 设置新的光标位置
    const newCursorPos = start + superscriptText.length;
    inputElement.setSelectionRange(newCursorPos, newCursorPos);
  }

  function toSuperSub(row, column: VxeTableDefines.ColumnInfo<any>) {
    const $table = sysStandardEvaluationLimt.value.getXTable();
    if ($table) {
      const cellElement = $table.getCellElement(row, column);
      console.log('cellElement', cellElement);
      if (cellElement) {
        const inputElement = cellElement.querySelector('input') || cellElement.querySelector('textarea');
        if (inputElement) {
          // 获取选中文字
          const selectedText = getSelectedText(inputElement);
          if (selectedText) {
            // 替换为上标
            replaceSelectedTextWithSuperscript(inputElement, selectedText);
            // 更新行数据
            row[column.field] = inputElement.value;
          }
        }
      }
    }
  }
  //表单提交事件
  async function handleSubmit(values) {
    try {
      // 先同步数据确保最新状态
      let values = await validate();
      console.log('values', values);
      setModalProps({ confirmLoading: true });
      clonestandard(values,isUpdate.value)
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  // 添加监听窗口变化的代码
  onMounted(() => {
    // 监听窗口大小变化
    window.addEventListener('resize', handleWindowResize);

    if (modalRef.value) {
      // 监听模态窗口状态变化
      const modalDom = modalRef.value.$el;
      if (modalDom) {
        // 使用 MutationObserver 监听DOM变化
        const observer = new MutationObserver(() => {
          // 模态窗口状态发生变化，刷新表格
          handleTableRefresh();
        });

        // 开始监听
        observer.observe(modalDom, {
          attributes: true,
          attributeFilter: ['class', 'style'],
        });
      }
    }
  });

  // 在组件卸载时移除事件监听器
  onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('resize', handleWindowResize);
  });

  // 处理窗口大小变化
  function handleWindowResize() {
    // 延迟执行以避免频繁触发
    if (activeKey.value === 'sysStandardEvaluationLimt') {
      handleTableRefresh();
    }
  }
  // 刷新表格
  function handleTableRefresh() {
    const $table = sysStandardEvaluationLimt.value?.getXTable();
    if ($table) {
      // 强制刷新表格布局
      $table.refreshColumn();
      $table.recalculate(true);
    }
  }
  const unitOptions = ref([]);
  async function loadUnitOptions() {
    const res = await initDictOptions('sys_unit,unit_name,id');
    unitOptions.value = res;
  }

  onMounted(async () => {
    await loadUnitOptions();
    window.addEventListener('resize', handleWindowResize);
  });
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }

  .table-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .titleArea {
    display: flex;
    align-content: center;
    padding-right: 70px;
    .title {
      margin-right: 16px;
      line-height: 32px;
    }
    .right {
      overflow-x: auto;
      overflow-y: hidden;
      flex: 1;
      white-space: nowrap;
      .ant-radio-group {
        font-weight: normal;
      }
    }
  }

  html[data-theme='light'] {
    .right {
      .ant-radio-group {
        :deep(.ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked)) {
          color: #555;
        }
      }
    }
  }
</style>

<style lang="less">
  // Online表单Tab风格专属样式
  .j-cgform-tab-modal {
    .contentArea {
      //padding: 20px 1.5% 0;
    }

    //.ant-modal-header {
    //  padding-top: 8px;
    //  padding-bottom: 8px;
    //  border-bottom: none !important;
    //}

    .ant-modal .ant-modal-body > .scrollbar,
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-tabs-top-bar {
      width: calc(100% - 55px);
      position: relative;
      left: -14px;
    }

    .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
      overflow: hidden auto;
    }
  }
</style>
