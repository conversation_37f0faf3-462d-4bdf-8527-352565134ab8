import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '测试状态',
    align: 'center',
    dataIndex: 'testControlStatus_dictText',
    width: 80,
  },

  {
    title: '研发项目编码',
    align: 'center',
    dataIndex: 'rdId_dictText',
    width: 150,
    resizable: true,
  },
  {
    title: '检品编号',
    align: 'center',
    dataIndex: 'sampleNo',
    width: 120,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '合同单号',
    align: 'center',
    dataIndex: 'orderId_dictText',
    width: 150,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '检品名称',
    align: 'left',
    dataIndex: 'name',
    width: 180,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '检品流转状态',
    align: 'center',
    dataIndex: 'sampleFlowStatus_dictText',
    resizable: true,
    width: 80,
  },
  {
    title: '批号',
    align: 'center',
    dataIndex: 'lotNo',
    resizable: true,
    width: 180,
  },
  {
    title: '生产厂家',
    align: 'center',
    dataIndex: 'manufacturer',
    resizable: true,
    width: 180,
  },
  {
    title: '接收日期',
    align: 'center',
    dataIndex: 'receiveDate',
    resizable: true,
    width: 180,
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '产品列表',
    align: 'center',
    dataIndex: 'productIds_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '业务类型',
    align: 'center',
    dataIndex: 'bizTypeId_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '客户名称',
    align: 'center',
    dataIndex: 'customerId_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '客户联系人',
    align: 'center',
    dataIndex: 'customerContactId_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '加急类别',
    align: 'center',
    dataIndex: 'serviceType',
    resizable: true,
    width: 180,
  },
  {
    title: '是否补发项目',
    align: 'center',
    dataIndex: 'isSupplementary',
    resizable: true,
    width: 180,
  },
  {
    title: '剂型',
    align: 'center',
    dataIndex: 'dosageForm_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '产地',
    align: 'center',
    dataIndex: 'origin',
    resizable: true,
    width: 180,
  },
  {
    title: '检品数量',
    align: 'center',
    dataIndex: 'receiveCount',
    resizable: true,
    width: 180,
  },
  {
    title: '数量单位',
    align: 'center',
    dataIndex: 'receiveCountUnit_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '留样数量',
    align: 'center',
    dataIndex: 'retainAmount',
    resizable: true,
    width: 180,
  },
  {
    title: '包装规格',
    align: 'center',
    dataIndex: 'packMedicineType',
    resizable: true,
    width: 180,
  },
  {
    title: '生产日期',
    align: 'center',
    dataIndex: 'productDate',
    resizable: true,
    width: 180,
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '注册编号',
    align: 'center',
    dataIndex: 'certificateNo',
    resizable: true,
    width: 180,
  },
  {
    title: '批准文号',
    align: 'center',
    dataIndex: 'authorizeNo',
    resizable: true,
    width: 180,
  },
  {
    title: '仓库库位',
    align: 'center',
    dataIndex: 'warehouseLocation',
    resizable: true,
    width: 180,
  },
  {
    title: '领用备注',
    align: 'center',
    dataIndex: 'remark',
    resizable: true,
    width: 180,
  },
  {
    title: '成分',
    align: 'center',
    dataIndex: 'composition',
    resizable: true,
    width: 180,
  },
  {
    title: '性状',
    align: 'center',
    dataIndex: 'properties',
    resizable: true,
    width: 180,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'specification',
    resizable: true,
    width: 180,
  },
  {
    title: '贮藏',
    align: 'center',
    dataIndex: 'storage',
    resizable: true,
    width: 180,
  },
  {
    title: '有效期',
    align: 'center',
    dataIndex: 'expiry',
    resizable: true,
    width: 180,
  },
  {
    title: '每日最大摄入量',
    align: 'center',
    dataIndex: 'maxDailyIntake',
    resizable: true,
    width: 180,
  },
  {
    title: '治疗周期',
    align: 'center',
    dataIndex: 'treatmentCycle',
    resizable: true,
    width: 180,
  },
  {
    title: '品种类别',
    align: 'center',
    dataIndex: 'category_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: 'pH值',
    align: 'center',
    dataIndex: 'phValue',
    resizable: true,
    width: 180,
  },
  {
    title: '给药途径',
    align: 'center',
    dataIndex: 'routeOfAdministration_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '剧烈生产条件',
    align: 'center',
    dataIndex: 'severeCondition_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '药包材类型',
    align: 'center',
    dataIndex: 'packagingMaterialType_dictText',
    resizable: true,
    width: 180,
  },
  {
    title: '是否特殊管理样品',
    align: 'center',
    dataIndex: 'isSpecialManage_dictText',
    resizable: true,
    width: 180,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '检品流转状态',
    field: 'sampleFlowStatus',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sample_flow_status',
    },
    defaultValue: '未入库',
  },
  {
    label: '测试状态',
    field: 'testControlStatus',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'test_control_status',
    },
    defaultValue: '正常,部分取消,补样中',
  },
  {
    label: '检品名称',
    field: 'name',
    component: 'JInput',
    //colProps: {span: 6},
  },
  {
    label: '研发项目编码',
    field: 'rdId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rd_project,rd_no,id',
    },
  },
  {
    label: '报价单号',
    field: 'quotationId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'quotation,quotation_no,id',
    },
    //colProps: {span: 6},
  },
  {
    label: '合同单号',
    field: 'orderId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'biz_order,contract_no,id',
    },
    //colProps: {span: 6},
  },
  {
    label: '编号',
    field: 'sampleNo',
    component: 'Input',
    //colProps: {span: 6},
  },

  {
    label: '产品列表',
    field: 'productIds',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sys_product,name,id',
    },
    //colProps: {span: 6},
  },
  {
    label: '业务类型',
    field: 'bizTypeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'biz_type,name,id',
      getPopupContainer: () => document.body,
    },
  },
  {
    label: '客户名称',
    field: 'customerId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sys_customer,name,id',
    },

    //colProps: {span: 6},
  },
  {
    label: '受理人',
    field: 'acceptor',
    component: 'JSelectUser',
    componentProps: {},
    colProps: {
      span: 8,
    },
  },
  {
    label: '接收日期',
    field: 'receiveDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: {
      span: 8,
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '业务信息',
    field: 'BizInfo',
    component: 'Divider',
    colProps: {
      span: 24,
    },
  },
  {
    label: '报价单号',
    field: 'quotationId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'quotation,quotation_no,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入报价单号!' }];
    },
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
  },
  {
    label: '合同单号',
    field: 'orderId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'biz_order,contract_no,id',
    },
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
  },
  {
    label: '编号',
    field: 'sampleNo',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入编号!' }];
    },
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
  },
  {
    label: '研发项目编码',
    field: 'rdId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rd_project,rd_no,id',
    },
    dynamicDisabled: false,
    colProps: {
      span: 8,
    },
  },
  {
    label: '客户名称',
    field: 'customerId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_customer,name,id',
    },
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
    needTran: true,
    sourceTable: 'sys_customer',
    sourceField: 'name',
    //colProps: {span: 6},
  },
  {
    label: '客户地址',
    field: 'detailedAddress',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
    needTran: true,
    sourceTable: 'sys_customer',
    linkField: 'customerId',
  },
  {
    label: '客户联系人名称',
    field: 'customerContactId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_customer_contact,name,id',
    },
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
    needTran: true,
    sourceTable: 'sys_customer_contact',
    sourceField: 'name',
    //colProps: {span: 6},
  },
  {
    label: '客户电话',
    field: 'phone',
    component: 'Input',
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
  },
  {
    label: '业务类型',
    field: 'bizTypeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'biz_type,name,id',
      getPopupContainer: () => document.body,
    },
    dynamicDisabled: true,
    colProps: {
      span: 8,
    },
  },
  {
    label: '检品信息',
    field: 'sampleInfo',
    component: 'Divider',
    colProps: {
      span: 24,
    },
  },
  {
    label: '检品名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    colProps: {
      span: 8,
    },
    needTran: true,
  },
  {
    label: '是否特殊管理样品',
    field: 'isSpecialManage',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '检品数量',
    field: 'receiveCount',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '留样数量',
    field: 'retainAmount',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '数量单位',
    field: 'receiveCountUnit',
    component: 'JDictSelectTag',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    componentProps: {
      dictCode: 'sys_unit,unit_name,id',
    },
    colProps: {
      span: 8,
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    needTran: true,
    sourceTable: 'sys_unit',
    sourceField: 'unit_name',
  },
  {
    label: '批号',
    field: 'lotNo',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    colProps: {
      span: 8,
    },
    needTran: true,
  },
  {
    label: '规格',
    field: 'specification',
    component: 'Input',
    colProps: {
      span: 8,
    },
    needTran: true,
  },
  {
    label: '生产厂家',
    field: 'manufacturer',
    component: 'Input',
    colProps: {
      span: 8,
    },
    needTran: true,
  },
  {
    label: '生产单位地址',
    field: 'manufacturerAddr',
    component: 'Input',
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
    colProps: {
      span: 24,
    },
    needTran: true,
  },
  {
    label: '生产日期',
    field: 'productDate',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '包装材料',
    field: 'packMaterial',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'pack_material',
    },
    colProps: {
      span: 8,
    },
    needTran: true,
  },
  {
    label: '批准文号',
    field: 'authorizeNo',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '注册编号',
    field: 'certificateNo',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },

  {
    label: '剂型',
    field: 'dosageForm',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'dosage_form',
    },
    colProps: {
      span: 8,
    },
  },

  {
    label: '制剂规格',
    field: 'dosageSpec',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '特殊检品',
    field: 'specialSample',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },

  {
    label: '包装规格',
    field: 'packMedicineType',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '检品类别',
    field: 'sampleType',
    component: 'JCategorySelect',
    componentProps: {
      pcode: 'C10', //TODO back和事件未添加，暂时有问题
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '储存条件',
    field: 'storage',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填项!' }];
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '检品状态',
    field: 'properties',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '有效期',
    field: 'expiry',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '商标',
    field: 'trademark',
    component: 'Input',
    colProps: {
      span: 8,
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '安全声明',
    field: 'safeDeclare',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'safeDeclare',
      type: 'radio',
    },
    defaultValue: '1',
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '安全声明描述',
    field: 'safeDeclareDesc',
    component: 'Input',

    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '仓库库位',
    field: 'warehouseLocation',
    component: 'Input',
    ifShow: false,
  },
  {
    label: '检验信息',
    field: 'TestInfo',
    component: 'Divider',
    colProps: {
      span: 24,
    },
  },
  {
    label: '检测标准',
    field: 'methodInfo',
    component: 'Input',
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '判定依据',
    field: 'standardInfo',
    component: 'Input',
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '委托日期',
    field: 'entrustDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '接收日期',
    field: 'receiveDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '要求完成时间',
    field: 'testEndDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: {
      span: 8,
    },
    dynamicDisabled: true,
  },
  {
    label: '受理人',
    field: 'acceptor',
    component: 'JSelectUser',
    componentProps: {},
    colProps: {
      span: 8,
    },
  },
  {
    label: '认证图章',
    field: 'sealType',
    component: 'JCheckbox',
    componentProps: {
      dictCode: 'seal_type',
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '报告版本',
    field: 'reportVersion',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'report_version',
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '检测项目',
    field: 'testChoice',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'test_choice',
    },
    colProps: {
      span: 8,
    },
  },
  {
    label: '项目内容',
    field: 'testContent',
    component: 'Input',
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '测余样品处理',
    field: 'sampleReturn',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sample_return',
      type: 'radio',
    },
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '备注',
    field: 'testRemark',
    component: 'InputTextArea',
    colProps: {
      span: 24,
    },
    itemProps: {
      labelCol: { sm: { span: 2 } },
      wrapperCol: { sm: { span: 22 } },
    },
  },
  {
    label: '父级节点',
    field: 'pid',
    component: 'JTreeSelect',
    componentProps: {
      dict: 'sample,name,id',
      pidField: 'pid',
      pidValue: '0',
      hasChildField: 'has_child',
    },
    show: false,
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '商机',
    field: 'opportunityId',
    component: 'JDictSelectTag',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  quotationId: { title: '报价单号', order: 0, view: 'list', type: 'string', dictTable: 'quotation', dictCode: 'id', dictText: 'quotation_no' },
  orderId: { title: '合同单号', order: 1, view: 'list', type: 'string', dictTable: 'biz_order', dictCode: 'id', dictText: 'contract_no' },
  sampleNo: { title: '编号', order: 2, view: 'text', type: 'string' },
  name: { title: '产品名称', order: 3, view: 'text', type: 'string' },
  lotNo: { title: '批号', order: 4, view: 'text', type: 'string' },
  manufacturerId: { title: '生产厂家', order: 5, view: 'sel_search', type: 'string', dictCode: '' },
  standardPrice: { title: '标准价格', order: 6, view: 'number', type: 'number' },
  pmPrice: { title: 'PM价格', order: 7, view: 'number', type: 'number' },
  applyPrice: { title: '申请单价', order: 8, view: 'number', type: 'number' },
  standardLeadTime: { title: '标准工期', order: 9, view: 'number', type: 'number' },
  pmLeadTime: { title: 'PM工期 ', order: 10, view: 'number', type: 'number' },
  discount: { title: '折扣', order: 11, view: 'number', type: 'number' },
  receiveDate: { title: '接收日期', order: 12, view: 'date', type: 'string' },
  productIds: { title: '产品列表', order: 13, view: 'list', type: 'string', dictTable: 'sys_product', dictCode: 'id', dictText: 'name' },
  bizTypeId: { title: '业务类型', order: 14, view: 'cat_tree', type: 'string', pcode: 'C09' },
  customerId: { title: '客户名称', order: 15, view: 'list', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  customerContactId: {
    title: '客户联系人',
    order: 16,
    view: 'list',
    type: 'string',
    dictTable: 'sys_customer_contact',
    dictCode: 'id',
    dictText: 'name',
  },
  warehouseLocation: { title: '仓库库位', order: 17, view: 'text', type: 'string' },
  remark: { title: '领用备注', order: 18, view: 'text', type: 'string' },
  composition: { title: '成分', order: 19, view: 'text', type: 'string' },
  properties: { title: '性状', order: 20, view: 'text', type: 'string' },
  specification: { title: '规格', order: 21, view: 'text', type: 'string' },
  storage: { title: '贮藏', order: 22, view: 'text', type: 'string' },
  expiry: { title: '有效期', order: 23, view: 'text', type: 'string' },
  maxDailyIntake: { title: '每日最大摄入量', order: 24, view: 'text', type: 'string' },
  treatmentCycle: { title: '治疗周期', order: 25, view: 'text', type: 'string' },
  category: { title: '品种类别', order: 26, view: 'list', type: 'string', dictCode: 'drug_category' },
  phValue: { title: 'pH值', order: 27, view: 'text', type: 'string' },
  routeOfAdministration: { title: '给药途径', order: 28, view: 'list', type: 'string', dictCode: 'route_of_administration' },
  severeCondition: { title: '剧烈生产条件', order: 29, view: 'list', type: 'string', dictCode: 'severe_condition' },
  packagingMaterialType: { title: '药包材类型', order: 30, view: 'list', type: 'string', dictCode: 'packaging_material_type' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
