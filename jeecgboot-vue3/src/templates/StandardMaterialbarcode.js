export default {
  panels: [
    {
      index: 0,
      name: 1,
      height: 40,
      width: 60,
      paperHeader: 49.5,
      paperFooter: 105,
      printElements: [
        {
          options: {
            left: 0,
            top: 0,
            height: 102,
            width: 168,
            right: 168,
            bottom: 90,
            vCenter: 84,
            hCenter: 45,
          },
          printElementType: {
            title: '矩形',
            type: 'rect',
          },
        },
        {
          options: {
            left: 0,
            top: 12,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 24,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 36,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 48,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 60,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 72,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 32,
            top: 0,
            height: 72,
            width: 9,
          },
          printElementType: {
            title: '竖线',
            type: 'vline',
          },
        },
        {
          options: {
            left: 80,
            top: 24.3,
            height: 48,
            width: 9,
            right: 92.25,
            bottom: 85.5,
            vCenter: 87.75,
            hCenter: 57,
          },
          printElementType: {
            title: '竖线',
            type: 'vline',
          },
        },
        {
          options: {
            left: 110,
            top: 24.3,
            height: 48,
            width: 9,
            right: 135.75,
            bottom: 67.5,
            vCenter: 131.25,
            hCenter: 45.75,
          },
          printElementType: {
            title: '竖线',
            type: 'vline',
          },
        },
        {
          options: {
            left: 1,
            top: 0,
            height: 9.75,
            width: 33,
            title: '标准物质名称',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 0,
            width: 120,
            testData: '名称',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'name',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 12,
            top: 13,
            height: 9.75,
            width: 33,
            title: '编号',
            right: 124.5,
            bottom: 21.75,
            vCenter: 64.5,
            hCenter: 16.875,
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 13,
            width: 120,
            testData: '编号',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'code',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 12,
            top: 27,
            height: 9.75,
            width: 33,
            title: '批号',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 27,
            width: 50,
            testData: '批号',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'lotNo',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 39,
            height: 9.75,
            width: 33,
            title: '开启日期',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 39,
            width: 50,
            testData: '开启日期',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'purchseDate',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 9,
            top: 51,
            height: 9.75,
            width: 33,
            title: '开启人',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 51,
            width: 50,
            testData: '开启人',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: '',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        // {
        //   options: {
        //     fontSize: 5,
        //     left: 6,
        //     top: 63,
        //     height: 9.75,
        //     width: 33,
        //     title: '开启日期',
        //   },
        //   printElementType: {
        //     type: 'text',
        //   },
        // },
        // {
        //   options: {
        //     left: 33,
        //     top: 63,
        //     width: 33,
        //     testData: '开启日期',
        //     fontSize: 5,
        //     right: 148.5,
        //     bottom: 40.5,
        //     vCenter: 88.5,
        //     hCenter: 25.5,
        //     field: 'purchseDate',
        //     coordinateSync: false,
        //     widthHeightSync: false,
        //     textAlign: 'center',
        //     textContentVerticalAlign: 'middle',
        //     qrCodeLevel: 0,
        //   },
        //   printElementType: {
        //     type: 'text',
        //   },
        // },
        {
          options: {
            fontSize: 5,
            left: 85,
            top: 27,
            height: 9.75,
            width: 33,
            title: '含量/纯度',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 112,
            top: 27,
            width: 33,
            testData: '纯度',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'purity',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 85,
            top: 39,
            height: 9.75,
            width: 33,
            title: '有效期至',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 112,
            top: 39,
            width: 50,
            testData: '有效期至',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'vaildDate',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 85,
            top: 51,
            height: 9.75,
            width: 33,
            title: '保存条件',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 112,
            top: 51,
            width: 50,
            testData: '保存条件',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'storageCondition',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 85,
            top: 63,
            height: 9.75,
            width: 33,
            title: '确认效期',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 112,
            top: 63,
            width: 50,
            testData: '确认效期',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: '',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 26,
            top: 75,
            height: 26,
            width: 120,
            field: 'barcode',
            testData: 'XS888888888',
            fontSize: 12,
            lineHeight: 18,
            textAlign: 'left',
            textType: 'barcode',
            title: '条形码',
            qid: 'barcode',
            hideTitle: true,
          },
          printElementType: {
            type: 'text',
          },
        },
      ],
      paperNumberContinue: false,
      paperNumberLeft: -10,
      paperNumberTop: -10,
      watermarkOptions: {},
      panelLayoutOptions: {},
    },
  ],
};
