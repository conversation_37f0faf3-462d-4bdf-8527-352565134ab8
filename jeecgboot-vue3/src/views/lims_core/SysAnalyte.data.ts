import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编号',
    align: 'center',
    dataIndex: 'code',
    resizable: true,
  },
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
    resizable: true,
  },
  {
    title: 'CAS编号',
    align: 'center',
    dataIndex: 'casNo',
    resizable: true,
  },
  {
    title: 'ICH类别',
    align: 'center',
    dataIndex: 'ichTypeId',
    resizable: true,
    customRender: ({ text }) => {
      return render.renderCategoryTree(text, 'C08');
    },
  },
  {
    title: '药典第一组别',
    align: 'center',
    dataIndex: 'groupOne',
    resizable: true,
  },
  {
    title: '药典第二组别',
    align: 'center',
    dataIndex: 'groupTwo',
    resizable: true,
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
    resizable: true,
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
    resizable: true,
  },
  {
    title: '更新人',
    align: 'center',
    dataIndex: 'updateBy_dictText',
    resizable: true,
  },
  {
    title: '更新日期',
    align: 'center',
    dataIndex: 'updateTime',
    resizable: true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'code',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '名称',
    field: 'name',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: 'CAS编号',
    field: 'casNo',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'code',
    component: 'JInput',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入编号!' }];
    },
  },
  {
    label: '名称',
    field: 'name',
    component: 'JInput',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
    needTran: true,
  },
  {
    label: 'CAS编号',
    field: 'casNo',
    component: 'JInput',
  },
  {
    label: 'ICH类别',
    field: 'ichTypeId',
    component: 'JCategorySelect',
    componentProps: {
      pcode: 'C08', //TODO back和事件未添加，暂时有问题
    },
  },
  {
    label: '药典第一组别',
    field: 'groupOne',
    component: 'AutoComplete',
  },
  {
    label: '药典第二组别',
    field: 'groupTwo',
    component: 'AutoComplete',
  },
  // TO
  //
  // DO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: { title: '编号', order: 0, view: 'text', type: 'string' },
  name: { title: '名称', order: 1, view: 'text', type: 'string' },
  casNo: { title: 'CAS编号', order: 2, view: 'text', type: 'string' },
  ichTypeId: { title: 'ICH类别', order: 3, view: 'cat_tree', type: 'string', pcode: 'C08' },
  createBy: { title: '创建人', order: 4, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 5, view: 'datetime', type: 'string' },
  updateBy: { title: '更新人', order: 6, view: 'text', type: 'string' },
  updateTime: { title: '更新日期', order: 7, view: 'datetime', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
