/**
 * 简单有效的 a-select 文本选择修复方案
 * 专门针对 JDictSelectTag 等组件
 */

/**
 * 初始化简单的文本选择修复
 */
export function initSimpleSelectTextFix() {
  console.log('初始化简单的 a-select 文本选择修复');
  
  // 添加强制样式
  addForceStyles();
  
  // 添加事件处理
  addEventHandlers();
  
  // 监听DOM变化
  observeDOM();
}

/**
 * 添加强制样式
 */
function addForceStyles() {
  const style = document.createElement('style');
  style.id = 'simple-select-text-fix';
  style.textContent = `
    /* 强制所有文本可选择 */
    .ant-select *,
    .ant-select-selector *,
    .ant-select-selection-item *,
    .ant-select-selection-item-content * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      cursor: text !important;
    }
    
    /* 移除阻止选择的样式 */
    .ant-select-selector {
      user-select: text !important;
      -webkit-user-select: text !important;
      pointer-events: auto !important;
    }
    
    .ant-select-selection-item {
      user-select: text !important;
      -webkit-user-select: text !important;
      pointer-events: auto !important;
    }
  `;
  
  document.head.appendChild(style);
}

/**
 * 添加事件处理
 */
function addEventHandlers() {
  let isSelecting = false;
  let startX = 0;
  let startY = 0;
  
  // 鼠标按下
  document.addEventListener('mousedown', (e) => {
    const target = e.target as HTMLElement;
    if (isInSelectComponent(target)) {
      isSelecting = false;
      startX = e.clientX;
      startY = e.clientY;
    }
  }, true);
  
  // 鼠标移动
  document.addEventListener('mousemove', (e) => {
    if (Math.abs(e.clientX - startX) > 3 || Math.abs(e.clientY - startY) > 3) {
      isSelecting = true;
    }
  }, true);
  
  // 鼠标释放
  document.addEventListener('mouseup', (e) => {
    const target = e.target as HTMLElement;
    if (isInSelectComponent(target) && isSelecting) {
      // 阻止后续的click事件
      setTimeout(() => {
        document.addEventListener('click', preventClick, true);
        setTimeout(() => {
          document.removeEventListener('click', preventClick, true);
        }, 10);
      }, 0);
    }
    isSelecting = false;
  }, true);
  
  // 双击复制
  document.addEventListener('dblclick', (e) => {
    const target = e.target as HTMLElement;
    if (isInSelectComponent(target)) {
      e.preventDefault();
      e.stopPropagation();
      
      const text = getTextContent(target);
      if (text) {
        copyText(text);
        showMessage('已复制: ' + text);
      }
    }
  }, true);
  
  function preventClick(e: Event) {
    const target = e.target as HTMLElement;
    if (isInSelectComponent(target)) {
      e.preventDefault();
      e.stopPropagation();
    }
  }
}

/**
 * 监听DOM变化，为新元素添加修复
 */
function observeDOM() {
  const observer = new MutationObserver(() => {
    fixNewElements();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 初始修复
  fixNewElements();
}

/**
 * 修复新元素
 */
function fixNewElements() {
  const selects = document.querySelectorAll('.ant-select');
  selects.forEach(select => {
    const element = select as HTMLElement;
    if (!element.hasAttribute('data-text-fixed')) {
      element.setAttribute('data-text-fixed', 'true');
      
      // 强制设置样式
      element.style.userSelect = 'text';
      element.style.webkitUserSelect = 'text';
      
      // 为所有子元素设置样式
      const allChildren = element.querySelectorAll('*');
      allChildren.forEach(child => {
        const childElement = child as HTMLElement;
        childElement.style.userSelect = 'text';
        childElement.style.webkitUserSelect = 'text';
        childElement.style.cursor = 'text';
      });
    }
  });
}

/**
 * 检查元素是否在select组件内
 */
function isInSelectComponent(element: HTMLElement): boolean {
  let current = element;
  while (current && current !== document.body) {
    if (current.classList.contains('ant-select') ||
        current.classList.contains('ant-select-selector') ||
        current.classList.contains('ant-select-selection-item')) {
      return true;
    }
    current = current.parentElement as HTMLElement;
  }
  return false;
}

/**
 * 获取文本内容
 */
function getTextContent(element: HTMLElement): string {
  // 尝试从选择项获取文本
  const selectionItem = element.closest('.ant-select-selection-item');
  if (selectionItem) {
    return (selectionItem.textContent || '').trim();
  }
  
  // 尝试从选择器获取文本
  const selector = element.closest('.ant-select-selector');
  if (selector) {
    return (selector.textContent || '').trim();
  }
  
  return (element.textContent || '').trim();
}

/**
 * 复制文本
 */
async function copyText(text: string) {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
  }
}

/**
 * 显示消息
 */
function showMessage(message: string) {
  const div = document.createElement('div');
  div.textContent = message;
  div.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
  `;
  
  document.body.appendChild(div);
  setTimeout(() => {
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  }, 2000);
}
