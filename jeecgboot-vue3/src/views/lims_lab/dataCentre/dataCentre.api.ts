import { defHttp } from '/@/utils/http/axios';




enum Api {
  getTests = '/dataCentre/getTests',
  getTestResults = '/dataCentre/getTestResults',
  getQCTestResults = '/dataCentre/getQCTestResults',
  saveTestResults = '/dataCentre/saveTestResults',
  submitTestResults = '/dataCentre/submitTestResults',
  checkTestResults = '/dataCentre/checkTestResults',
  unlockTestResults = '/dataCentre/unlockTestResults',
  generateTestRecord = 'oo/generateTestRecord',
  getOOEditorConfig = '/oo/editor-config',
  applyRework = '/dataCentre/applyRework',
  getConclusion = '/dataCentre/getConclusion',
  queryByIdUrl = '/dataCentre/getMethodidByTestid',
  addDuplicate = '/dataCentre/addDuplicate',
  changeStandard = '/dataCentre/changeStandard',
}

/**
 * 获取Test列表
 */
export const getTests = (loadByType,loadBy) => defHttp.get({ url: Api.getTests, params: { loadByType:loadByType,loadBy: loadBy } });

/**
 * 查询数据，获取当前选中的Test的所有TestResults
 */
export const getTestResults = (params?) => defHttp.get({ url: Api.getTestResults, params });

export const getQCTestResults = (params?) => defHttp.get({ url: Api.getQCTestResults, params });

export const generateTestRecord = (params?) => defHttp.get({ url: Api.generateTestRecord, params }, { joinParamsToUrl: true });

// 获取OnlyOffice EditorConfig
export const getOOEditorConfig = async (testId: String, biz: String) => {
  return await defHttp.get({ url: Api.getOOEditorConfig, params: { biz: biz, bizId: testId, permission: null } });
};

export const submitTestResult = (params?) => defHttp.post({ url: Api.submitTestResults, params }, { joinParamsToUrl: true });

export const saveTestResult = async (testResults) => {
  return await defHttp.post({ url: Api.saveTestResults, data: testResults });
};

export const checkTestResult = (params?) => defHttp.post({ url: Api.checkTestResults, params }, { joinParamsToUrl: true });

export const unlockTestResult = (params?) => defHttp.post({ url: Api.unlockTestResults, params }, { joinParamsToUrl: true });

/**
 * 申请复测
 * @param params 复测申请参数
 */
export const applyRework = (params) => defHttp.post({ url: Api.applyRework, data: params });

/**
 * 获取结论
 * @param params 结论参数
 */
export const getConclusion = (params) => defHttp.post({ url: Api.getConclusion, params });

/**
 * 根据testid查询方法id
 * @param params
 */
export const queryByIdUrl = (params?) => defHttp.get({ url: Api.queryByIdUrl, params });

/**
 * 添加重复性测试
 * @param params
 */
export const addDuplicate = (params) => defHttp.get({ url: Api.addDuplicate, params });

/**
 * 修改标准
 * @param params
 */
export const changeStandard = (params) => defHttp.get({ url: Api.changeStandard, params });
