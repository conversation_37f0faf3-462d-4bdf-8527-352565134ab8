import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/dcs/training/list',
  save = '/dcs/training/add',
  edit = '/dcs/training/edit',
  deleteOne = '/dcs/training/delete',
  deleteBatch = '/dcs/training/deleteBatch',
  importExcel = '/dcs/training/importExcel',
  exportXls = '/dcs/training/exportXls',
  trainingRecordList = '/dcs/training/queryTrainingRecordByMainId',
  createMeeting = '/dcs/training/createMeeting',
  raiseCheackIn = '/dcs/training/raiseCheackIn',
  checkin = '/dcs/training/checkin',
  confirmBatch = '/dcs/training/confirmBatch',
  activateDoc = '/dcs/training/activateDoc',
  getOOEditorConfig = '/oo/editor-config',
  geneOneTraingRecord = '/oo/generateTrainingRecord',
  batchGeneTraingRecord = '/oo/generateTrainingRecords',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 查询子表数据
 * @param params
 */
export const trainingRecordList = Api.trainingRecordList;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 批量确认
 * @param params
 */
export const confirmBatch = (params, handleSuccess) => {
  return defHttp.post({ url: Api.confirmBatch, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 创建会议
 * @param params
 */
export const createMeeting = (params, handleSuccess) => {
  return defHttp.get({ url: Api.createMeeting, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 发起签到
 * @param params
 */
export const raiseCheackIn = (params, handleSuccess) => {
  return defHttp.get({ url: Api.raiseCheackIn, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 签到
 * @param params
 */
export const checkin = (params, handleSuccess) => {
  return defHttp.get({ url: Api.checkin, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 激活文档
 * @param params
 */
export const activateDoc = (params, handleSuccess) => {
  return defHttp.get({ url: Api.activateDoc, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

// 获取OnlyOffice EditorConfig
export const getOOEditorConfig = async (trainingId: number) => {
  return await defHttp.get({ url: Api.getOOEditorConfig, params: { biz: 'training', bizId: trainingId, permission: null } });
};


/**
 * 生成单个培训记录
 * @param params
 */
export const geneOneTraingRecord = (params) => {
  return defHttp.post({ url: Api.geneOneTraingRecord, params }, { joinParamsToUrl: true });
};

/**
 * 批量生成培训记录
 * @param params
 */
export const batchGeneTraingRecord = (params) => {
  return defHttp.post({ url: Api.batchGeneTraingRecord, params }, { joinParamsToUrl: true });
};
