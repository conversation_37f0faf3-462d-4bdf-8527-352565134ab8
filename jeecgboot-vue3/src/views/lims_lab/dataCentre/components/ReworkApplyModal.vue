<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="550" :okText="okText" @ok="handleSubmit" destroyOnClose>
    <ReworkApplyForm ref="formRef" :testIds="testIds" @success="handleSuccess" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, unref, nextTick } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import ReworkApplyForm from './ReworkApplyForm.vue';
  import { applyRework } from '../dataCentre.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useUserStore } from '/@/store/modules/user';

  const { createMessage } = useMessage();
  const userStore = useUserStore();
  const emit = defineEmits(['success', 'register']);
  const formRef = ref<any>(null);
  const testIds = ref<string>('');
  const title = ref<string>('申请复测');
  const okText = ref<string>('提交');

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 接收传入的参数
    if (data && data.testIds) {
      testIds.value = data.testIds;
    }

    // 自定义标题
    if (data && data.title) {
      title.value = data.title;
    }

    // 重置表单
    await nextTick();
    if (unref(formRef)) {
      unref(formRef).resetFields();

      // 设置复测人为当前用户
      const currentUser = userStore.getUserInfo;
      if (currentUser && currentUser.id) {
        // 单选模式下，直接设置ID值
        unref(formRef).setFieldsValue({
          reworkBy: currentUser.id,
        });
      }
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      const formEl = unref(formRef);
      if (!formEl) return;

      const values = await formEl.validate();
      setModalProps({ confirmLoading: true });

      // 确保testIds已设置
      values.testIds = testIds.value;

      // 调用API提交复测申请
      const result = await applyRework(values);

      createMessage.success('复测申请提交成功');
      closeModal();
      emit('success', result);
    } catch (error: any) {
      createMessage.error(error.message || '提交失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  // 处理成功事件
  function handleSuccess(result: any) {
    emit('success', result);
  }

  // 暴露方法
  defineExpose({
    openModal: (data: any) => {
      setModalProps({ visible: true });
      if (data) {
        if (data.testIds) {
          testIds.value = data.testIds;
        }
        if (data.title) {
          title.value = data.title;
        }
      }
    },
  });
</script>
