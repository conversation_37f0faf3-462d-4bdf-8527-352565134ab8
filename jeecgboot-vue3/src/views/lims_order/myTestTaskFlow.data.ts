import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '任务id',
    align: 'center',
    dataIndex: 'taskId',
  },
  {
    title: '当前节点',
    align: 'center',
    dataIndex: 'stepId',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '测试任务条码',
    field: 'taskId',
    component: 'Input',
  },
  {
    label: '流转节点',
    field: 'flowStep',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'flow_step',
    },
    defaultValue: '结果录入',
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '任务id',
    field: 'taskId',
    component: 'Input',
    show:false,
  },
  {
    label: '流转节点',
    field: 'flowStep',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'flow_step',
    },
    defaultValue: '结果录入',
  },
  {
    label: '状态',
    field: 'status',
    component: 'Input',
    show:false,
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  taskId: { title: '任务id', order: 0, view: 'text', type: 'string' },
  stepId: { title: '流转节点', order: 1, view: 'text', type: 'string' },
  status: { title: '状态', order: 2, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
