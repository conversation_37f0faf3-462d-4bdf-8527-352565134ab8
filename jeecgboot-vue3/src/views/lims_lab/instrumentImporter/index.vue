<template>
  <BasicForm @register="registerForm" :layout="'horizontal'" @submit="handleSubmit" class="ant-advanced-search-form" />
  <JVxeTable
    ref="vxeTable"
    toolbar
    rowSelection
    :toolbarConfig="toolbarConfig"
    dragSort
    sortKey="sortNum"
    :sortBegin="1"
    dragSortFixed="none"
    :columns="columns"
    :column-config="columnConfig"
    :dataSource="dataSource"
    :edit-config="editConfig"
  >
    <template #toolbarPrefix>
      <a-button type="primary" preIcon="ant-design:safety-outlined" @click="handleSequence">上样</a-button>
      <a-button type="primary" preIcon="ant-design:clear-outlined" @click="clearScreen">清除屏幕</a-button>
    </template>
  </JVxeTable>
  <SequenceNoModal @register="registerSequenceNoModal" @success="handleSequenceSuccess" />
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { columns, searchFormSchema } from './InstrumentImporter.data';
  import { getTestsForImporter, exportSequence } from './InstrumentImporter.api';
  import { useForm, BasicForm } from '@/components/Form';
  import { VxeTablePropTypes } from 'vxe-table';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '/@/hooks/web/useMessage';
  import SequenceNoModal from '@/views/lims_lab/instrumentImporter/components/SequenceNoModal.vue';

  const vxeTable = ref();

  // 工具栏的按钮配置
  const toolbarConfig = reactive({
    btn: ['remove'],
    slot: ['prefix', 'suffix'],
  });

  const columnConfig = reactive({
    resizable: true,
    drag: true,
  });

  const editConfig = reactive<VxeTablePropTypes.EditConfig>({
    trigger: 'click',
    mode: 'cell',
    showIcon: true,
    showStatus: true,
  });

  const dataSource = ref<Recordable[]>([]);

  const [registerForm] = useForm({
    //注册表单列
    schemas: searchFormSchema,
    compact: true,
    autoFocusFirstItem: true,
    //回车提交
    autoSubmitOnEnter: true,
    //不显示重置按钮
    showResetButton: false,
    showSubmitButton: false,
  });

  const [registerSequenceNoModal, { openModal: openSequenceNoModal }] = useModal();
  const { createMessage, createConfirm } = useMessage();

  /**
   * 查询事件
   */
  async function handleSubmit(values: any) {
    const res = await getTestsForImporter(values.instrumentId, values.taskId);
    console.log('res', res);
    // 将查询结果追加到dataSource中，但需要排除已存在的testId
    if (res && res.length > 0) {
      // 获取当前dataSource中所有的testId
      const existingTestIds = dataSource.value.map((item) => item.testId);
      // 过滤出不在当前dataSource中的新数据
      const newData = res.filter((item: any) => !existingTestIds.includes(item.testId));
      // 将新数据追加到现有数据中
      dataSource.value = [...dataSource.value, ...newData];
    }
  }

  /**
   * 上样处理函数
   * 打开序列号编辑弹窗
   */
  function handleSequence() {
    if (!dataSource.value || dataSource.value.length === 0) {
      createMessage.warning('请先添加数据再进行上样操作');
      return;
    }

    // 打开序列号编辑弹窗
    openSequenceNoModal(true, dataSource.value);
  }

  /**
   * 序列号设置成功后的回调
   * @param dataWithSequenceNo 带有序列号的数据
   */
  async function handleSequenceSuccess(dataWithSequenceNo: any[]) {
    try {
      // 更新本地数据源
      dataSource.value = dataWithSequenceNo;

      // 获取设置的序列号（所有行都是相同的序列号）
      const sequenceNo = dataWithSequenceNo.length > 0 ? dataWithSequenceNo[0].sequenceNo : '';

      // 确认是否导出序列
      createConfirm({
        iconType: 'warning',
        title: '确认上样',
        content: `序列号已设置为 "${sequenceNo}"，确定要导出当前序列数据吗？`,
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          await exportSequence(dataSource.value);
          createMessage.success('序列导出成功');
        },
      });
    } catch (error: any) {
      createMessage.error('导出序列失败: ' + (error.message || '未知错误'));
    }
  }

  /**
   * 清除屏幕
   */
  function clearScreen() {
    dataSource.value = [];
  }
</script>
<style lang="less" scoped>
  .ant-advanced-search-form {
    //background: rgba(255, 255, 255, 0.04);
    //border: 1px solid #434343;
    padding: 24px;
    border-radius: 2px;
  }
</style>
