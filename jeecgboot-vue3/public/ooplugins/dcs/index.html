<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title></title>
    <title>引用文件</title>
    <style>
      .search-container {
          margin-left: 20px; 
      }
      ul {
          list-style-type: none;
      }
      li {
          margin: 5px 0;
      }
      li a {
          text-decoration: none;
          color: blue;
      }
      li a:hover {
          text-decoration: underline;
      }
      #staticLinkList {
          max-height: 300px;
          overflow-y: auto;
          border: 1px solid #ccc;
          padding: 10px;
          list-style-type: none;
          border: 1px solid transparent;
      }
      #staticLinkList:not(:empty) {
          border-color: #ccc;
      }
      h1 {
          margin-left: 20px;
      }
    </style>
    <!-- 可以引入外部的 js 脚本 -->
    <!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.2/jquery.min.js"></script> -->
    <!-- 这个是插件 js，基本是必须要引入的 -->
    <script src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.js"></script>
    <link rel="stylesheet" href="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.css">
    <script src="dcs.js"></script>
  </head>

  <body>
    <!-- 这里是 html 区域，H5 的标签都可以在这里使用，设置 id 然后到 dcs.js 里面绑定事件 -->
    <!-- 操作 -->
    <h1>请选择要引用的文件</h1>
    <div class="search-container">
        <input type="text" id="nameSearch" placeholder="请输入名称搜索" onkeydown="handleKeyDown(event)">
        <button onclick="searchLinks()">搜索</button>
    </div>
    <ul id="staticLinkList">
        <!-- 链接列表将通过 JS 动态生成 -->
    </ul>
  </body>
</html>