import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
import { getAreaTextByCode } from '@/components/Form/src/utils/Area';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '客户代码',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '销售人员',
    align: 'center',
    dataIndex: 'salerId_dictText',
  },
  {
    title: '当前编号',
    align: 'center',
    dataIndex: 'currentNo',
  },
  {
    title: '成交状态',
    align: 'center',
    dataIndex: 'dealStatus_dictText',
  },
  {
    title: '客户进度',
    align: 'center',
    dataIndex: 'customerProgress_dictText',
  },
  {
    title: '客户编号',
    align: 'center',
    dataIndex: 'customerCode',
  },
  {
    title: '客户来源',
    align: 'center',
    dataIndex: 'customerSource_dictText',
  },
  {
    title: '客户状态',
    align: 'center',
    dataIndex: 'customerStatus_dictText',
  },
  {
    title: '负责人',
    align: 'center',
    dataIndex: 'responsiblePerson_dictText',
  },
  {
    title: '是否归档',
    align: 'center',
    dataIndex: 'isArchived',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '分配状态',
    align: 'center',
    dataIndex: 'allocationStatus_dictText',
  },
  {
    title: '锁定状态',
    align: 'center',
    dataIndex: 'lockStatus',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '业务类型',
    align: 'center',
    dataIndex: 'businessType',
  },
  {
    title: '大客户分类',
    align: 'center',
    dataIndex: 'majorCustomerCategory_dictText',
  },
  {
    title: '客户级别',
    align: 'center',
    dataIndex: 'customerLevel_dictText',
  },
  {
    title: '企业性质',
    align: 'center',
    dataIndex: 'enterpriseNature_dictText',
  },
  {
    title: '客户关系',
    align: 'center',
    dataIndex: 'customerRelationship_dictText',
  },
  {
    title: '产品剂型',
    align: 'center',
    dataIndex: 'productDosageForm_dictText',
  },
  {
    title: '成交金额',
    align: 'center',
    dataIndex: 'transactionAmount',
  },
  {
    title: '潜在业务机会金额',
    align: 'center',
    dataIndex: 'potentialAmount',
  },
  {
    title: '成单几率',
    align: 'center',
    dataIndex: 'successProbability',
  },
  {
    title: '决策者态度',
    align: 'center',
    dataIndex: 'decisionMakerAttitude',
  },
  {
    title: '关键人员态度',
    align: 'center',
    dataIndex: 'keyPersonAttitude',
  },
  {
    title: '价格敏感度',
    align: 'center',
    dataIndex: 'priceSensitivity',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '国家',
    align: 'center',
    dataIndex: 'country',
  },
  {
    title: '省',
    align: 'center',
    dataIndex: 'province',
  },
  {
    title: '市',
    align: 'center',
    dataIndex: 'city',
  },
  {
    title: '区',
    align: 'center',
    dataIndex: 'district',
    customRender: ({ text }) => {
      return getAreaTextByCode(text);
    },
  },
  {
    title: '地址',
    align: 'center',
    dataIndex: 'detailedAddress',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '销售人员',
    field: 'salerId',
    component: 'JSelectUser',
    componentProps: {},
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
    needTran: true,
  },
  {
    label: '客户代码',
    field: 'code',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户代码!' }];
    },
  },
  {
    label: '销售人员',
    field: 'salerId',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '成交状态',
    field: 'dealStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'deal_status',
    },
  },
  {
    label: '客户进度',
    field: 'customerProgress',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_progress',
    },
  },
  {
    label: '客户编号',
    field: 'customerCode',
    component: 'Input',
  },
  {
    label: '客户来源',
    field: 'customerSource',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'opportunity_source',
    },
  },
  {
    label: '客户状态',
    field: 'customerStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_status',
    },
  },
  {
    label: '负责人',
    field: 'responsiblePerson',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '是否归档',
    field: 'isArchived',
    defaultValue: 'N',
    component: 'JSwitch',
    componentProps: {
      options: ['Y', 'N'],
    },
  },
  {
    label: '分配状态',
    field: 'allocationStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'allocation_status',
    },
  },
  {
    label: '锁定状态',
    field: 'lockStatus',
    defaultValue: 'N',
    component: 'JSwitch',
    componentProps: {
      options: ['Y', 'N'],
    },
  },
  {
    label: '业务类型',
    field: 'businessType',
    defaultValue: '预设业务类型',
    component: 'Input',
  },
  {
    label: '大客户分类',
    field: 'majorCustomerCategory',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_type',
    },
  },
  {
    label: '客户级别',
    field: 'customerLevel',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_grade',
    },
  },
  {
    label: '企业性质',
    field: 'enterpriseNature',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'enterprise_nature',
    },
  },
  {
    label: '客户关系',
    field: 'customerRelationship',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_relationship',
    },
  },
  {
    label: '产品剂型',
    field: 'productDosageForm',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'product_dosage_form',
    },
  },
  {
    label: '成交金额',
    field: 'transactionAmount',
    component: 'Input',
  },
  {
    label: '潜在业务机会金额',
    field: 'potentialAmount',
    component: 'Input',
  },
  {
    label: '成单几率',
    field: 'successProbability',
    component: 'Input',
  },
  {
    label: '决策者态度',
    field: 'decisionMakerAttitude',
    component: 'Input',
  },
  {
    label: '关键人员态度',
    field: 'keyPersonAttitude',
    component: 'Input',
  },
  {
    label: '价格敏感度',
    field: 'priceSensitivity',
    component: 'JSwitch',
    componentProps: {
      options: ['Y', 'N'],
    },
  },
  {
    label: '国家',
    field: 'country',
    defaultValue: '中国',
    component: 'Input',
  },
  {
    label: '省市区',
    field: 'district',
    component: 'JAreaSelect',
  },
  {
    label: '地址',
    field: 'detailedAddress',
    component: 'Input',
    needTran: true,
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表列表数据
export const sysCustomerContactColumns: BasicColumn[] = [
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '手机',
    align: 'center',
    dataIndex: 'phone',
  },
  {
    title: '电子邮箱',
    align: 'center',
    dataIndex: 'email',
  },
  {
    title: '传真',
    align: 'center',
    dataIndex: 'fax',
  },
  {
    title: '座机',
    align: 'center',
    dataIndex: 'telephone',
  },
  {
    title: '职位',
    align: 'center',
    dataIndex: 'title',
  },
  {
    title: '性别',
    align: 'center',
    dataIndex: 'sex',
  },
  {
    title: '生日',
    align: 'center',
    dataIndex: 'birthday',
  },
  {
    title: '爱好',
    align: 'center',
    dataIndex: 'hobby',
  },
];
//子表表格配置
export const sysCustomerContactJVxeColumns: JVxeColumn[] = [
  {
    title: '姓名',
    key: 'name',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '手机',
    key: 'phone',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '电子邮箱',
    key: 'email',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '传真',
    key: 'fax',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '座机',
    key: 'telephone',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '职位',
    key: 'title',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '性别',
    key: 'sex',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '生日',
    key: 'birthday',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '爱好',
    key: 'hobby',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  code: { title: '客户代码', order: 1, view: 'text', type: 'string' },
  salerId: { title: '销售人员', order: 2, view: 'sel_user', type: 'string' },
  currentNo: { title: '当前编号', order: 4, view: 'text', type: 'string' },
  dealStatus: { title: '成交状态', order: 5, view: 'list', type: 'string', dictCode: 'deal_status' },
  customerProgress: { title: '客户进度', order: 6, view: 'list', type: 'string', dictCode: 'customer_progress' },
  customerCode: { title: '客户编号', order: 7, view: 'text', type: 'string' },
  customerSource: { title: '客户来源', order: 8, view: 'list', type: 'string', dictCode: 'opportunity_source' },
  customerStatus: { title: '客户状态', order: 9, view: 'list', type: 'string', dictCode: 'customer_status' },
  responsiblePerson: { title: '负责人', order: 10, view: 'sel_user', type: 'string' },
  isArchived: { title: '是否归档', order: 11, view: 'switch', type: 'string' },
  allocationStatus: { title: '分配状态', order: 12, view: 'list', type: 'string', dictCode: 'allocation_status' },
  lockStatus: { title: '锁定状态', order: 13, view: 'switch', type: 'string' },
  businessType: { title: '业务类型', order: 14, view: 'text', type: 'string' },
  majorCustomerCategory: { title: '大客户分类', order: 15, view: 'list', type: 'string', dictCode: 'customer_type' },
  customerLevel: { title: '客户级别', order: 16, view: 'list', type: 'string', dictCode: 'customer_grade' },
  enterpriseNature: { title: '企业性质', order: 17, view: 'list', type: 'string', dictCode: 'enterprise_nature' },
  customerRelationship: { title: '客户关系', order: 18, view: 'list', type: 'string', dictCode: 'customer_relationship' },
  productDosageForm: { title: '产品剂型', order: 19, view: 'list_multi', type: 'string', dictCode: 'product_dosage_form' },
  transactionAmount: { title: '成交金额', order: 20, view: 'text', type: 'string' },
  potentialAmount: { title: '潜在业务机会金额', order: 21, view: 'text', type: 'string' },
  successProbability: { title: '成单几率', order: 22, view: 'text', type: 'string' },
  decisionMakerAttitude: { title: '决策者态度', order: 23, view: 'text', type: 'string' },
  keyPersonAttitude: { title: '关键人员态度', order: 24, view: 'text', type: 'string' },
  priceSensitivity: { title: '价格敏感度', order: 25, view: 'switch', type: 'string' },
  country: { title: '国家', order: 26, view: 'text', type: 'string' },
  province: { title: '省', order: 27, view: 'pca', type: 'string' },
  city: { title: '市', order: 28, view: 'pca', type: 'string' },
  district: { title: '区', order: 29, view: 'pca', type: 'string' },
  detailedAddress: { title: '地址', order: 30, view: 'text', type: 'string' },
  //子表高级查询
  sysCustomerContact: {
    title: '客户联系人',
    view: 'table',
    fields: {
      name: { title: '姓名', order: 0, view: 'text', type: 'string' },
      phone: { title: '手机', order: 1, view: 'text', type: 'string' },
      email: { title: '电子邮箱', order: 2, view: 'text', type: 'string' },
      fax: { title: '传真', order: 3, view: 'text', type: 'string' },
      telephone: { title: '座机', order: 4, view: 'text', type: 'string' },
      title: { title: '职位', order: 5, view: 'text', type: 'string' },
      sex: { title: '性别', order: 6, view: 'text', type: 'string' },
      birthday: { title: '生日', order: 7, view: 'text', type: 'string' },
      hobby: { title: '爱好', order: 8, view: 'text', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
