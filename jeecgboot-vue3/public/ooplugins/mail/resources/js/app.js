(function(window, undefined) {
    'use strict';

    // 全局变量存储编辑器实例
    let editorInstance = null; 

    // 插件初始化
    window.Asc.plugin.init = function(initData) {
        // 确保加载遮罩默认是隐藏的
        document.getElementById('loading-overlay').style.display = 'none';    
        console.log('window.Asc',window.Asc);
        
        // 初始化 wangEditor 编辑器
        try {
            // 创建编辑器容器
            const container = document.getElementById('message-editor').parentNode;
            // 清空原有内容，创建工具栏和编辑器容器
            container.innerHTML = '<div style="border: 1px solid #ddd; border-radius: 4px; overflow: hidden;"><div id="toolbar-container"></div><div id="editor-container" style="height: 250px; border-top: 1px solid #eee;"></div></div>';
            
            // 创建编辑器
            const { createEditor, createToolbar } = window.wangEditor;
            
            // 编辑器配置
            const editorConfig = {
                placeholder: '请输入邮件正文...',
                on<PERSON><PERSON><PERSON>(editor) {
                    // 当编辑器内容变化时触发
                    console.log('editor content', editor.getHtml());
                }
            };
            
            // 创建编辑器实例 - 使用简洁模式
            editorInstance = createEditor({
                selector: '#editor-container',
                html: '',
                config: editorConfig,
                mode: 'simple', // 改为简洁模式
            });
            
            // 创建工具栏 - 使用简洁模式
            const toolbarConfig = {};
            const toolbar = createToolbar({
                editor: editorInstance,
                selector: '#toolbar-container',
                config: toolbarConfig,
                mode: 'simple', // 改为简洁模式
            });
            
            // 创建兼容接口，保持与原有代码的兼容性
            window.richTextEditor = {
                getContent: function() {
                    return editorInstance.getHtml();
                },
                setContent: function(content) {
                    editorInstance.setHtml(content || '');
                }
            };
            
            console.log('wangEditor 初始化成功');
            
            // 如果有初始内容，设置到编辑器中
            if (Asc.plugin.info && Asc.plugin.info.options && Asc.plugin.info.options.body) {
                window.richTextEditor.setContent(Asc.plugin.info.options.body);
            }
        } catch (error) {
            console.error('编辑器初始化失败:', error);
            // 可以考虑提供一个简单的 textarea 作为后备
            createSimpleFallbackEditor();
        }
        
        // 获取当前文档名称
        getCurrentDocumentInfo();
        
        // 从options中获取文件URL和邮件信息
        if (Asc.plugin.info && Asc.plugin.info.options) {
            window.fileUrlFromEditor = Asc.plugin.info.options.url;
            console.log('Asc.plugin.info.options:', Asc.plugin.info.options);
            window.fileNameFromEditor = Asc.plugin.info.options.fileName || '外部文件';           
            // 更新界面显示 (附件部分保持不变)
            if (window.fileUrlFromEditor) {
                // 创建可点击的附件链接
                const attachmentInfo = document.querySelector('.attachment-info');
                attachmentInfo.innerHTML = '';
                
                // 移除附件信息区域的边框和样式
                attachmentInfo.style.border = 'none !important';
                attachmentInfo.style.boxShadow = 'none !important';
                attachmentInfo.style.padding = '0 !important';
                attachmentInfo.style.margin = '0 !important';
                attachmentInfo.style.background = 'transparent !important';
                
                // 创建附件容器
                const attachmentContainer = document.createElement('div');
                attachmentContainer.style.display = 'flex';
                attachmentContainer.style.alignItems = 'center';
                attachmentContainer.style.border = 'none';
                attachmentContainer.style.boxShadow = 'none';
                attachmentContainer.style.background = 'transparent';
                
                // 根据文件类型添加图标
                const fileIcon = document.createElement('span');
                fileIcon.style.marginRight = '8px';
                fileIcon.style.width = '16px';
                fileIcon.style.height = '16px';
                fileIcon.style.display = 'inline-block';
                
                // 获取文件扩展名
                const fileExt = window.fileNameFromEditor.split('.').pop().toLowerCase();
                
                // 设置图标
                switch(fileExt) {
                    case 'pdf':
                        // PDF图标代码保持不变
                        fileIcon.innerHTML = '<svg viewBox="0 0 1024 1024" width="16" height="16"><path d="M852.537678 966.322357c7.542461 0 14.818718-3.016984 20.142807-8.341075s8.341075-12.600347 8.341075-20.142807V262.033969c0-6.743847-2.395841-13.22149-6.743848-18.368111L725.469393 67.793414c-5.412825-6.388908-13.39896-10.115771-21.740034-10.115771h-532.231543c-7.542461 0-14.818718 3.016984-20.142807 8.341075s-8.341075 12.600347-8.341075 20.142807v851.67695c0 7.542461 3.016984 14.818718 8.341075 20.142807s12.600347 8.341075 20.142807 8.341075h681.039862z" fill="#CA223D"></path><path d="M881.02156 265.228423v-3.194454c0-6.743847-2.395841-13.22149-6.743848-18.368111L725.469393 67.793414c-5.412825-6.388908-13.39896-10.115771-21.740034-10.115771h-1.331023v207.55078h178.623224z" opacity=".15"></path><path d="M359.083258 657.52513c-10.293241 0-16.14974-4.614211-19.432929-9.494627-10.027036-15.262392 2.040901-40.640555 14.197574-52.442288 6.300173-6.033969 25.023224-21.562565 65.131369-37.889775 8.07487-19.07799 15.97227-40.55182 23.071057-62.469324 11.801733-36.647487 18.989255-74.271057 22.982323-100.447834-8.696014-16.061005-34.162912-65.042634-34.162912-83.676949 0-10.470711 2.307106-18.989255 7.010052-25.200693 4.880416-6.566378 11.979203-10.204506 20.054073-10.204507 9.228423 0 18.456846 4.702946 25.466898 12.955286 7.098787 8.25234 11.00312 19.255459 11.003119 30.702253 0 17.569497-2.307106 44.012478-6.300173 71.254073 17.392028 31.323397 36.292548 59.984749 52.087348 78.885269 15.617331 18.634315 34.340381 35.493934 55.902946 50.223917 37.97851-3.283189 82.878336-4.348007 99.205546 2.218371 20.852686 8.429809 24.40208 22.272444 20.763952 32.476949-6.388908 17.924437-34.87279 27.951473-58.742461 20.586482-9.760832-3.016984-35.493934-12.067938-66.994801-32.47695-26.176776 2.484575-53.240901 6.388908-78.619064 11.358059-27.507799 5.32409-53.063432 12.422877-76.045754 21.030156-19.521664 44.278683-37.357366 70.10052-53.152166 76.755633-9.583362 4.259272-17.303293 5.856499-23.425997 5.856499z M404.808319 775.25851h17.125823l1.419757 8.252339h0.532409c6.655113-5.679029 14.996187-10.293241 23.514732-10.293241 19.699133 0 31.323397 16.14974 31.323397 40.640555 0 27.330329-16.415945 43.213865-33.896707 43.213865-7.010052 0-13.665165-3.105719-19.787869-8.696014l0.532409 13.132755v24.224611h-20.763951v-110.47487z M492.034662 815.366655c0-26.088042 16.238475-42.060312 33.453033-42.060312 8.962218 0 14.552513 3.460659 20.231542 8.696014l-0.887348-12.511612v-28.128943h20.941421v113.758059h-16.948353l-1.685962-8.252339h-0.443674c-5.856499 5.679029-13.842634 10.293241-22.094974 10.293241-19.787868-0.088735-32.565685-15.706066-32.565685-41.794108z M630.993414 757.600277c-2.75078-0.976083-5.856499-1.685962-8.429809-1.685962-6.300173 0-9.672097 3.726863-9.672097 12.511612v6.832583h14.996187v16.504679h-14.996187v63.356672h-20.941421v-63.267937h-10.648181v-15.528596l10.648181-0.887349v-6.566378c0-16.682149 7.719931-29.459965 27.774003-29.459965 6.122704 0 11.535529 1.419757 15.084922 2.839515l-3.815598 15.351126z" fill="#FFFFFF"></path></svg>';
                        break;
                    case 'doc':
                    case 'docx':
                        fileIcon.innerHTML = '<svg viewBox="0 0 24 24" width="16" height="16"><path fill="#2B579A" d="M21,5v14c0,1.1-0.9,2-2,2H5c-1.1,0-2-0.9-2-2V5c0-1.1,0.9-2,2-2h14C20.1,3,21,3.9,21,5z M12,12.5l-5-3v6l5-3V12.5z M17,9.5l-5,3v-6l5,3V9.5z"/></svg>';
                        break;
                    case 'xls':
                    case 'xlsx':
                        fileIcon.innerHTML = '<svg viewBox="0 0 24 24" width="16" height="16"><path fill="#217346" d="M21,5v14c0,1.1-0.9,2-2,2H5c-1.1,0-2-0.9-2-2V5c0-1.1,0.9-2,2-2h14C20.1,3,21,3.9,21,5z M14,10H8v1h6V10z M14,12H8v1h6V12z M14,14H8v1h6V14z M16,10h2v5h-2V10z"/></svg>';
                        break;
                    case 'ppt':
                    case 'pptx':
                        fileIcon.innerHTML = '<svg viewBox="0 0 24 24" width="16" height="16"><path fill="#D24726" d="M21,5v14c0,1.1-0.9,2-2,2H5c-1.1,0-2-0.9-2-2V5c0-1.1,0.9-2,2-2h14C20.1,3,21,3.9,21,5z M9,11.5c0,0.8,0.7,1.5,1.5,1.5H12v1H9v2h3.5c0.8,0,1.5-0.7,1.5-1.5v-3c0-0.8-0.7-1.5-1.5-1.5H12v-1h3V8h-3.5C9.7,8,9,8.7,9,9.5V11.5z"/></svg>';
                        break;
                    default:
                        fileIcon.innerHTML = '<svg viewBox="0 0 24 24" width="16" height="16"><path fill="#607D8B" d="M14,2H6C4.9,2,4,2.9,4,4v16c0,1.1,0.9,2,2,2h12c1.1,0,2-0.9,2-2V8L14,2z M16,16H8v-2h8V16z M16,12H8v-2h8V12z M13,9V3.5L18.5,9H13z"/></svg>';
                }
                
                // 创建链接
                const attachmentLink = document.createElement('a');
                attachmentLink.href = window.fileUrlFromEditor;
                attachmentLink.textContent = window.fileNameFromEditor;
                attachmentLink.target = '_blank';
                attachmentLink.style.color = '#3d85c6';
                attachmentLink.style.textDecoration = 'underline';
                attachmentLink.title = '点击查看文件';
                
                // 组装附件显示
                attachmentContainer.appendChild(fileIcon);
                attachmentContainer.appendChild(attachmentLink);
                
                // 设置附件信息区域样式
                attachmentInfo.style.border = 'none';
                attachmentInfo.style.boxShadow = 'none';
                attachmentInfo.style.padding = '0';
                attachmentInfo.style.margin = '0';
                attachmentInfo.style.background = 'transparent';
                
                attachmentInfo.appendChild(attachmentContainer);
            }
            
            // 设置收件人和抄送
            if (Asc.plugin.info.options.to) {
                document.getElementById('to').value = Asc.plugin.info.options.to;
            }
            
            if (Asc.plugin.info.options.cc) {
                document.getElementById('cc').value = Asc.plugin.info.options.cc;
            }
            
            // 设置邮件主题
            if (Asc.plugin.info.options.subject) {
                document.getElementById('subject').value = Asc.plugin.info.options.subject;
            }
        }
    };

    window.Asc.plugin.button = function(id) {
        if (id == 0) {
           sendMail();
        } else {
            this.executeCommand("close", "");
        }
    };

    // 获取当前文档信息
    function getCurrentDocumentInfo() {
        window.Asc.plugin.executeMethod("GetFileInfo", [], function(result) {
            if (result) {
                const docName = result.data.title || "未命名文档";
                document.getElementById('current-document-name').textContent = docName;                
                // 默认设置邮件主题为文档名
                document.getElementById('subject').value = docName;
            }
        });
    }

    // 发送邮件
    function sendMail() {
        // 验证表单
        if (!validateForm()) {
            return;
        }
        // 显示加载中
        toggleLoading(true);
        
        // 获取表单数据 (to, cc, bcc 已处理成数组)
        const mailData = getFormData();      
        
        // 获取token
        const token = Asc.plugin.info && Asc.plugin.info.options ? Asc.plugin.info.options.token : null;
        if (token) {
            mailData.token = token; // 保留token，将在sendMailToServer中使用
        }

        // 准备附件URL列表
        mailData.attachments = [];
        if (window.fileUrlFromEditor) {
            mailData.attachments.push(window.fileUrlFromEditor);
        }
        
        // 直接调用发送到服务器的函数
        sendMailToServer(mailData);
    }

    // 发送邮件数据到后端服务器
    function sendMailToServer(mailData) {
        const backendApiUrl = '/jeecg-boot/oo/sendEmail'; // 后端API端点URL
        const token = mailData.token; // 从mailData获取token

        // 准备发送到后端的数据 (符合EmailDTO结构)
        const emailDTO = {
            to: mailData.to,
            cc: mailData.cc,
            bcc: mailData.bcc,
            subject: mailData.subject,
            body: mailData.message, // 使用 getFormData 获取的 HTML 或纯文本内容
            attachments: mailData.attachments // 附件URL列表
        };

        // 构建请求头
        const headers = {
            'Content-Type': 'application/json'
        };
        if (token) {
            headers['X-Access-Token'] = token;
        }

        // 发送POST请求到后端
        fetch(backendApiUrl, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(emailDTO)
        })
        .then(response => {
            if (!response.ok) {
                // 如果HTTP状态码不是2xx，尝试解析错误信息
                return response.json().then(err => {
                    throw new Error(err.message || `HTTP error! status: ${response.status}`);
                }).catch(() => {
                    // 如果解析json失败，抛出通用错误
                    throw new Error(`HTTP error! status: ${response.status}`);
                });
            }
            return response.json(); // 解析成功的JSON响应
        })
        .then(result => {
            // 检查后端返回的Result对象
            if (result && (result.success === true || result.code === 200)) {
                showSuccess("邮件发送成功！");
                resetForm();
            } else {
                // 显示后端返回的错误信息
                showError("邮件发送失败: " + (result.message || "未知错误"));
            }
        })
        .catch(error => {
            // 处理网络错误或上面抛出的错误
            showError("发送邮件请求失败: " + error.message);
        })
        .finally(() => {
            // 无论成功或失败，都隐藏加载指示器
            toggleLoading(false);
        });
    }

    // 移除 sendMailViaSmtp 函数
    // 移除 sendEmailWithSmtpJs 函数

    // 验证表单
    function validateForm() {
        // 验证收件人
        const to = document.getElementById('to').value.trim();
        if (!to) {
            showError("请输入收件人邮箱");
            return false;
        }
        
        // 验证邮箱格式
        const emails = to.split(';');
        for (let email of emails) {
            email = email.trim();
            if (email && !isValidEmail(email)) {
                showError("收件人邮箱格式不正确: " + email);
                return false;
            }
        }
        
        // 验证抄送邮箱格式
        const cc = document.getElementById('cc').value.trim();
        if (cc) {
            const ccEmails = cc.split(';');
            for (let email of ccEmails) {
                email = email.trim();
                if (email && !isValidEmail(email)) {
                    showError("抄送邮箱格式不正确: " + email);
                    return false;
                }
            }
        }
        
        // 验证密送邮箱格式
        const bcc = document.getElementById('bcc').value.trim();
        if (bcc) {
            const bccEmails = bcc.split(';');
            for (let email of bccEmails) {
                email = email.trim();
                if (email && !isValidEmail(email)) {
                    showError("密送邮箱格式不正确: " + email);
                    return false;
                }
            }
        }
        
        // 验证主题
        const subject = document.getElementById('subject').value.trim();
        if (!subject) {
            showError("请输入邮件主题");
            return false;
        }
        
        return true;
    }
    
    // 验证邮箱格式
    function isValidEmail(email) {
        const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        return re.test(String(email).toLowerCase());
    }

    // 显示错误信息
    function showError(message) {
        alert(message);
    }
    
    // 显示成功信息
    function showSuccess(message) {
        alert(message);
    }
    
    // 切换加载状态
    function toggleLoading(show) {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (show) {
            loadingOverlay.style.display = 'flex';
        } else {
            loadingOverlay.style.display = 'none';
        }
    }
    
    // 重置表单
    function resetForm() {
        document.getElementById('to').value = '';
        document.getElementById('cc').value = '';
        document.getElementById('bcc').value = '';
        document.getElementById('subject').value = '';
        if (editorInstance) {
            editorInstance.setHtml('');
        } else if (window.richTextEditor) {
            window.richTextEditor.setContent('');
        }
    }

    // 辅助函数：ArrayBuffer转Base64
    function arrayBufferToBase64(buffer) {
        let binary = '';
        const bytes = new Uint8Array(buffer);
        const len = bytes.byteLength;
        for (let i = 0; i < len; i++) {
            binary += String.fromCharCode(bytes[i]);
        }
        return window.btoa(binary);
    }

    // 获取表单数据
    function getFormData() {
        let message = '';
        let isHtml = true; // wangEditor 默认输出 HTML
        
        // 优先使用 wangEditor 编辑器
        if (editorInstance) {
            message = editorInstance.getHtml();
        } 
        // 其次使用兼容接口
        else if (window.richTextEditor) {
            message = window.richTextEditor.getContent();
        }
        // 最后尝试使用后备编辑器
        else {
            const simpleEditor = document.getElementById('simple-text-editor');
            if (simpleEditor) {
                message = simpleEditor.value;
                isHtml = false; // 后备编辑器是纯文本
            } else {
                console.warn("未找到任何可用的编辑器，邮件正文将为空");
            }
        }
        
        return {
            to: parseEmails('to'),
            cc: parseEmails('cc'),
            bcc: parseEmails('bcc'),
            subject: document.getElementById('subject').value,
            message: message,
            isHtml: isHtml,
            // attachments 数组将在 sendMail 函数中添加
        };
    }

    // 添加解析邮箱地址的函数
    function parseEmails(fieldId) {
        const value = document.getElementById(fieldId).value.trim();
        if (!value) return [];
        
        // 分割邮箱地址（使用分号作为分隔符）
        return value.split(';')
            .map(email => email.trim())
            .filter(email => email.length > 0); // 过滤掉空字符串
    }

    // 可选: 添加一个简单的后备编辑器函数
    function createSimpleFallbackEditor() {
        const container = document.getElementById('message-editor').parentNode;
        if (container) {
            container.innerHTML = '<textarea id="simple-text-editor" style="width: 100%; height: 200px;" placeholder="编辑器加载失败，请输入纯文本..."></textarea>';
            // 更新 editorInstance 为 null
            editorInstance = null;
        } else {
            console.error("找不到编辑器容器，无法创建后备编辑器");
        }
    }

})(window, undefined);