# 是否打开mock
VITE_USE_MOCK = true

# 发布路径
VITE_PUBLIC_PATH = /


# 跨域代理，您可以配置多个 ,请注意，没有换行符
VITE_PROXY = [["/jeecg-boot","http://localhost:48080/jeecg-boot"],["/upload","http://localhost:3300/upload"],["/ooplugins","http://localhost:3381/ooplugins"],["/app","http://*************:9997/app"],["/example/","http://*************:9997/example/"],["/web-apps/","http://*************:9997/web-apps/"],["/8.3.3-0d10b80972d36ff5a943a921d724982a/","http://*************:9997/8.3.3-0d10b80972d36ff5a943a921d724982a/"],["/8.3.3-7f0847e33368e9238b736c1cf90ab8e0/","http://*************:9997/8.3.3-7f0847e33368e9238b736c1cf90ab8e0/"],["/cache/","http://*************:9997/cache/"]]

#后台接口全路径地址(必填)
VITE_GLOB_DOMAIN_URL=http://localhost:48080/jeecg-boot

#后台接口父地址(必填)
VITE_GLOB_API_URL=/jeecg-boot

# 接口前缀
VITE_GLOB_API_URL_PREFIX=

#微前端qiankun应用,命名必须以VITE_APP_SUB_开头,jeecg-app-1为子应用的项目名称,也是子应用的路由父路径
VITE_APP_SUB_jeecg-app-1 = '//localhost:8092'
