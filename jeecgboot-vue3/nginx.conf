# Nginx 配置文件，用于解决 SPA 路由和 MIME 类型问题

server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    index index.html;

    # 设置正确的 MIME 类型
    location ~* \.(js|mjs)$ {
        add_header Content-Type application/javascript;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~* \.css$ {
        add_header Content-Type text/css;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~* \.(woff|woff2|eot|ttf|otf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location ~* \.(png|jpg|jpeg|gif|svg|webp|ico)$ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    # API 请求代理（根据实际情况调整）
    location /api/ {
        proxy_pass http://your-backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /js/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /css/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /img/ {
        expires 1M;
        add_header Cache-Control "public, immutable";
    }

    location /fonts/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA 路由处理 - 所有其他请求都返回 index.html
    location / {
        try_files $uri $uri/ /index.html;
        
        # 防止缓存 index.html
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 安全头部
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options DENY;
    add_header X-XSS-Protection "1; mode=block";

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
}
