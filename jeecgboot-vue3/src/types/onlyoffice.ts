export interface IConfig {
  document: {
    fileType: string;
    key: string;
    title: string;
    url: string;
    options?: {
      cache: boolean;
    };
  };
  editorConfig: {
    lang: string;
    mode: string;
    callbackUrl?: string;
    canHistoryClose?: boolean;
    canHistoryRestore?: boolean;
    canUseHistory?: boolean;
    plugins?: {
      autostart?: string[];
      pluginsData?: string[];
      url?: string;
    };
  };
  height: string;
}

export interface IConnector {
  executeMethod: (method: string, args: any[]) => void;
}

export interface IDocumentEditor {
  createConnector: () => IConnector;
} 