<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <BasicForm @register="registerForm" name="SysTranslationForm">
      <template #tran="{ model, field }">
        <!-- 如果是组件需要进行双向绑定，model当前表单对象，field当前字段名称  -->
        <a-input v-model:value="model[field]" placeholder="请输入翻译" />
        <span class="font-color">原文:{{ ' ' + record[field] }}</span>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../SysTranslation.data';
  import { customSave, list } from '../SysTranslation.api';
  import { initDictOptions } from '@/utils/dict';
  import { saveOrUpdate } from '@/views/lims_core/SysMethod.api';

  import { message } from 'ant-design-vue';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, scrollToField, resetSchema }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  const baseTable = ref('');
  const record = ref({});

  const fields = ref({});
  const formSchemas = ref([]);
  const cacheData = ref([]);
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    fields.value = {};
    formSchemas.value = [];
    console.log(data);

    //处理schema
    let cacheDict = {};
    cacheData.value = {};
    for (const e of data.dataSource) {
      let fs: [] = data.formSchema
        .filter((item) => item.needTran == true)
        .map((item) => ({
          label: item.title,
          key: item.key,
          field: item.key + '_' + e.id,
          component: 'Input',
          dictCode: item.dictCode,
          sourceTable: item.sourceTable || data.baseTable,
          sourceField: item.sourceField || item.key,
          sourceId: item.dictCode ? e[item.key] : e.id,
          slot: 'tran',
        }));
      fs.push({
        field: 'divider-basic'+ e.id,
        component: 'Divider',
        label: '分割一下',
      });
      formSchemas.value.push(...fs);

      for (const item of fs) {
        if (item.dictCode && !cacheDict[item.dictCode]) {
          await initDictOptions(item.dictCode).then((dictOptions) => {
            cacheDict[item.dictCode] = dictOptions;

            dictOptions
              .filter((i) => i.value == e[item.key])
              .forEach((j) => {
                record.value[item.field] = j.label;
              });
          });
        } else if (cacheDict[item.dictCode]) {
          cacheDict[item.dictCode]
            .filter((i) => i.value == e[item.key])
            .forEach((j) => {
              record.value[item.field] = j.label;
            });
        } else {
          record.value[item.field] = e[item.key];
        }
      }
    }
    console.log('record', record.value);
    await resetSchema(formSchemas.value);

    //加载数据
    for (const item1 of formSchemas.value) {
      if (item1.sourceTable && !cacheData.value[item1.sourceTable]) {
        await list({ context: item1.sourceTable }).then((res) => {
          console.log(res);
          cacheData.value[item1.sourceTable] = res.records;
        });
      }
    }

    formSchemas.value.forEach((item) => {
      if (item.sourceTable && cacheData.value[item.sourceTable]) {
        console.log(cacheData.value[item.sourceTable]);
        cacheData.value[item.sourceTable].forEach((i) => {
          if (item.sourceField == i.fieldName && i.sourceId == item.sourceId) {
            fields.value[item.field] = i.translation;
          }
        });
      }
    });
    console.log(fields.value);
    setFieldsValue(fields.value);
    setModalProps({ confirmLoading: false, showCancelBtn: true, showOkBtn: true });
  });

  //设置标题
  const title = '处理翻译';
  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      values.baseTable = baseTable.value;

      setModalProps({ confirmLoading: true });

      for (const item of formSchemas.value) {
        console.log(item.sourceTable, item.sourceId, item.sourceField, values[item.field]);
        if (values[item.field]) {
          let obj = {
            baseTable: item.sourceTable,
            baseId: item.sourceId,
            [item.sourceField]: values[item.field],
          };
          await customSave(obj, isUpdate.value);
        }
      }

      //提交表单
      // await customSave(values, isUpdate.value);

      //关闭弹窗
      closeModal();
      //刷新列表
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
