<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="1200" @ok="handleSubmit">
    <a-row>
      <a-col :span="20">
        <BasicTable @register="register" :row-selection="rowSelection" @edit-end="handleEditEnd" @edit-cancel="handleEditCancel" ref="tableRef" />
      </a-col>
      <a-col :span="4">
        <a-divider />
        <h2>请点击下方选择要指定角色的人员</h2>
        <a-divider />
        <a-steps v-model:current="current" direction="vertical" :items="items" @change="onStepChange" />
      </a-col>
    </a-row>
  </BasicModal>
</template>

<script lang="tsx" name="lims_core-user-lab" setup>
  import { defineComponent, reactive, ref } from 'vue';
  import { BasicColumn, BasicTable, EditRecordRow } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getUserList, queryTreeList } from '/@/api/common/api';
  import { columns, searchFormSchema } from './UserLabSelector.data';
  import { message, Modal } from 'ant-design-vue';
  const emit = defineEmits(['register', 'success']);
  //设置标题
  const title = '实验人员选择器';
  const queryParam = reactive<any>({});
  const current = ref<number>(0);
  const items = ref([]);
  const assignType = ref('');
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    selectedRowKeys.value = [];

    items.value = [
      {
        title: '未选择',
        id: '',
        field: 'assignee',
        subTitle: '检验人',
      },
      {
        title: '未选择',
        id: '',
        field: 'cooper',
        subTitle: '合作人',
      },
      {
        title: '未选择',
        id: '',
        field: 'checker',
        subTitle: '复核人',
      },
    ];

    console.log('123123', data);
    current.value = 0;
    subTitle.value = '正在选择检验人';

    console.log(data.record);
    if (data.record.assignee) {
      items.value[0].id = data.record.assignee;
      items.value[0].title = data.record.assignee_dictText;
    }
    if (data.record.cooper) {
      items.value[1].id = data.record.cooper;
      items.value[1].title = data.record.cooper_dictText;
    }
    if (data.record.checker) {
      items.value[2].id = data.record.checker;
      items.value[2].title = data.record.checker_dictText;
    }
    assignType.value = data.assignType;
    if (data.assignType == 'checker') {
      current.value = 2;
      items.value[0].title='不用填写';
      items.value[1].title='不用填写';
      subTitle.value = '正在选择复核人';
    }
    setProps({
      title: subTitle.value,
    });
    getForm().setFieldsValue({ departId: data.deptId });
  });

  const onStepChange = (value: number) => {
    current.value = value;
    selectedRowKeys.value = [];
    console.log(assignType.value);

    if (assignType.value == 'checker') {
      message.error('当前模式只能选择复核人!');
      current.value = 2;
    }
    subTitle.value = '正在选择' + items.value[current.value].subTitle;
    setProps({
      title: subTitle.value,
    });
  };
  const tableRef = ref();
  const subTitle = ref('请选择实验人员');
  const { tableContext } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'userlabselector',
      },
      title: subTitle.value,
      isTreeTable: true,
      size: 'small',
      canResize: true,
      clickToRowSelect: true,
      rowSelection: {
        type: 'radio',
        checkStrictly: false,
        onChange: (_, selectedRows1) => {
          if (selectedRows1.length == 0) {

            return;
          }
          items.value[current.value].id = selectedRows1[0].username;
          items.value[current.value].title = selectedRows1[0].realname;
        },
      },
      columns: columns,
      api: getUserList,
      rowKey: 'id',
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: false,
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  function handleEditEnd({ record, index, key, value }: Recordable) {
    console.log(record, index, key, value);
    return false;
  }

  function handleEditCancel() {
    console.log('cancel');
  }

  //BasicTable绑定注册
  const [register, { expandAll, collapseAll, getForm,setProps }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  const handleSubmit = async () => {
    Modal.confirm({
      title: '确认提交',
      content: (
        <div>
          <p>您选择的人员信息如下：</p>
          <ul>
            <li>检验人: {items.value[0].title || '未选择'}</li>
            <li>合作人: {items.value[1].title || '未选择'}</li>
            <li>复核人: {items.value[2].title || '未选择'}</li>
          </ul>
        </div>
      ),
      onOk: async () => {
        emit('success', items.value);
        selectedRowKeys.value = [];
        closeModal();
      },
      onCancel: () => {
        return;
      },
    });
  };
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
