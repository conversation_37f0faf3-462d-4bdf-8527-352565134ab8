<template>
  <a-spin :spinning="loading" tip="进度加载中...">
  <a-list :data-source="progress">
    <template #renderItem="{ item }">
      <a-list-item>
        <div style="width: 100%">
          <a-row>
            <a-col :span="6">
              <a-space direction="vertical" align="center">
                <span style="font-weight: bold; color: #e6c07b">{{ item.sampleNo }}</span>
                <span style="color: #0a8fe9">{{ item.method }}</span>
              </a-space>
            </a-col>
            <a-col :span="18"
              ><a-steps style="margin-top: 8px" :current="item.current" :status="item.status" :items="item.items">
                <template #progressDot="{ description, status, index, prefixCls }">
                  <a-popover>
                    <template #content>
                      <span> {{ description }}</span>
                    </template>
                    <span :class="`${prefixCls}-icon-dot`"></span>
                  </a-popover>
                </template> </a-steps
            ></a-col>
          </a-row>
        </div>
      </a-list-item>
    </template>
  </a-list>
  </a-spin>
</template>
<script lang="ts" setup>
  import { listProgress } from '@/views/lims_order/TestTaskFlow.api';
  import { defineProps, ref,onMounted , watch } from 'vue';
  /**
   * props声明
   */
  const props = defineProps({
    sampleId: {
      type: [String, Number],
      default: 'New',
    },
  });

  const progress = ref([]);
  const loading = ref(false);

  onMounted(async () => {
    if (props.sampleId) {
      loading.value = true;
      try {
        const res = await listProgress({ sampleId: props.sampleId });
        progress.value = res;
      } finally {
        loading.value = false;
      }
    }
  });
</script>
