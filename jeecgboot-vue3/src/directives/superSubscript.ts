/**
 * 上下标指令
 * 为输入框添加快捷键和右键菜单支持上下标功能
 */
import type { App, Directive, DirectiveBinding } from 'vue';
import { useSuperSubscript } from '/@/hooks/setting/useSuperSubscript';

interface SuperSubscriptElement extends HTMLElement {
  _superSubscriptHandler?: {
    keydownHandler: (e: KeyboardEvent) => void;
    // contextmenuHandler: (e: MouseEvent) => void;
    cleanup: () => void;
  };
}

const { toSuperscript, toSubscript } = useSuperSubscript();

/**
 * 获取选中的文本和位置信息
 */
function getSelectionInfo(element: HTMLInputElement | HTMLTextAreaElement) {
  const start = element.selectionStart || 0;
  const end = element.selectionEnd || 0;
  const selectedText = element.value.substring(start, end);
  return { start, end, selectedText };
}

/**
 * 替换选中文本为上标
 */
function replaceWithSuperscript(element: HTMLInputElement | HTMLTextAreaElement) {
  const { start, end, selectedText } = getSelectionInfo(element);
  if (selectedText) {
    const superscriptText = toSuperscript(selectedText);
    const newValue = element.value.substring(0, start) + superscriptText + element.value.substring(end);
    element.value = newValue;
    element.focus();
    const newPosition = start + superscriptText.length;
    element.setSelectionRange(newPosition, newPosition);

    // 触发input事件以更新Vue的响应式数据
    element.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

/**
 * 替换选中文本为下标
 */
function replaceWithSubscript(element: HTMLInputElement | HTMLTextAreaElement) {
  const { start, end, selectedText } = getSelectionInfo(element);
  if (selectedText) {
    const subscriptText = toSubscript(selectedText);
    const newValue = element.value.substring(0, start) + subscriptText + element.value.substring(end);
    element.value = newValue;
    element.focus();
    const newPosition = start + subscriptText.length;
    element.setSelectionRange(newPosition, newPosition);

    // 触发input事件以更新Vue的响应式数据
    element.dispatchEvent(new Event('input', { bubbles: true }));
  }
}

/**
 * 创建右键菜单
 */
// function createContextMenu(x: number, y: number, element: HTMLInputElement | HTMLTextAreaElement) {
//   // 移除已存在的菜单
//   const existingMenu = document.querySelector('.super-subscript-menu');
//   if (existingMenu) {
//     existingMenu.remove();
//   }
//
//   const menu = document.createElement('div');
//   menu.className = 'super-subscript-menu';
//   menu.style.cssText = `
//     position: fixed;
//     top: ${y}px;
//     left: ${x}px;
//     background: white;
//     border: 1px solid #d9d9d9;
//     border-radius: 6px;
//     box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
//     z-index: 9999;
//     padding: 4px 0;
//     min-width: 120px;
//     font-size: 14px;
//   `;
//
//   const superscriptItem = document.createElement('div');
//   superscriptItem.textContent = '转为上标';
//   superscriptItem.style.cssText = `
//     padding: 8px 16px;
//     cursor: pointer;
//     transition: background-color 0.2s;
//   `;
//   superscriptItem.addEventListener('mouseenter', () => {
//     superscriptItem.style.backgroundColor = '#f5f5f5';
//   });
//   superscriptItem.addEventListener('mouseleave', () => {
//     superscriptItem.style.backgroundColor = 'transparent';
//   });
//   superscriptItem.addEventListener('click', () => {
//     replaceWithSuperscript(element);
//     menu.remove();
//   });
//
//   const subscriptItem = document.createElement('div');
//   subscriptItem.textContent = '转为下标';
//   subscriptItem.style.cssText = `
//     padding: 8px 16px;
//     cursor: pointer;
//     transition: background-color 0.2s;
//   `;
//   subscriptItem.addEventListener('mouseenter', () => {
//     subscriptItem.style.backgroundColor = '#f5f5f5';
//   });
//   subscriptItem.addEventListener('mouseleave', () => {
//     subscriptItem.style.backgroundColor = 'transparent';
//   });
//   subscriptItem.addEventListener('click', () => {
//     replaceWithSubscript(element);
//     menu.remove();
//   });
//
//   menu.appendChild(superscriptItem);
//   menu.appendChild(subscriptItem);
//   document.body.appendChild(menu);
//
//   // 点击其他地方关闭菜单
//   const closeMenu = (e: MouseEvent) => {
//     if (!menu.contains(e.target as Node)) {
//       menu.remove();
//       document.removeEventListener('click', closeMenu);
//     }
//   };
//   setTimeout(() => {
//     document.addEventListener('click', closeMenu);
//   }, 0);
// }

/**
 * 检查元素是否为输入框
 */
function isInputElement(element: Element): element is HTMLInputElement | HTMLTextAreaElement {
  return element.tagName === 'INPUT' || element.tagName === 'TEXTAREA';
}

/**
 * 查找输入框元素
 */
function findInputElement(element: Element): HTMLInputElement | HTMLTextAreaElement | null {
  // 如果当前元素就是输入框
  if (isInputElement(element)) {
    return element;
  }

  // 查找子元素中的输入框
  const input = element.querySelector('input, textarea');
  if (input && isInputElement(input)) {
    return input;
  }

  return null;
}

const superSubscriptDirective: Directive = {
  mounted(el: SuperSubscriptElement, binding: DirectiveBinding) {
    const inputElement = findInputElement(el);
    if (!inputElement) {
      console.warn('v-super-subscript: 未找到输入框元素');
      return;
    }

    // 键盘事件处理
    const keydownHandler = (e: KeyboardEvent) => {
      // Alt+S: 上标
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        replaceWithSuperscript(inputElement);
      }
      // Alt+X: 下标
      else if (e.altKey && e.key === 'x') {
        e.preventDefault();
        replaceWithSubscript(inputElement);
      }
    };

    // 右键菜单事件处理
    // const contextmenuHandler = (e: MouseEvent) => {
    //   const { selectedText } = getSelectionInfo(inputElement);
    //   if (selectedText) {
    //     e.preventDefault();
    //     createContextMenu(e.clientX, e.clientY, inputElement);
    //   }
    // };

    // 绑定事件
    inputElement.addEventListener('keydown', keydownHandler);
    // inputElement.addEventListener('contextmenu', contextmenuHandler);

    // 保存处理器以便清理
    el._superSubscriptHandler = {
      keydownHandler,
      // contextmenuHandler,
      cleanup: () => {
        inputElement.removeEventListener('keydown', keydownHandler);
        // inputElement.removeEventListener('contextmenu', contextmenuHandler);
      },
    };
  },

  beforeUnmount(el: SuperSubscriptElement) {
    if (el._superSubscriptHandler) {
      el._superSubscriptHandler.cleanup();
      delete el._superSubscriptHandler;
    }
  },
};

export function setupSuperSubscriptDirective(app: App) {
  app.directive('super-subscript', superSubscriptDirective);
}

export default superSubscriptDirective;
