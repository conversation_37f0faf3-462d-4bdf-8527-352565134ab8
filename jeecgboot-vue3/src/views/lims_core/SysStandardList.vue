<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'lims_core:sys_standard:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" v-auth="'lims_core:sys_standard:add'" @click="handleAddYaoDian" preIcon="ant-design:plus-outlined"> 新增药典</a-button>
        <a-button type="primary" v-auth="'lims_core:sys_standard:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls">
          导出</a-button
        >
        <j-upload-button type="primary" v-auth="'lims_core:sys_standard:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_core:sys_standard:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex === 'url'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)"
            >下载</a-button
          >
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <SysStandardModal @register="registerModal" @success="handleSuccess" />
    <YaoDianModal @register="registerYaoDianModal" @success="handleSuccess" />
    <!-- 复制标准 -->
    <SysStandardCopyModal @register="registerCopyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="lims_core-sysStandard" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import SysStandardModal from './components/SysStandardModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './SysStandard.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './SysStandard.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { loadCategoryData, getRpColumnsAndData, getRpColumns } from '/@/api/common/api';
  import { getAuthCache, setAuthCache } from '/@/utils/auth';
  import { DB_DICT_DATA_KEY } from '/@/enums/cacheEnum';
  import YaoDianModal from "@/views/lims_core/YaoDianModal/YaoDianModal.vue";
  import SysStandardCopyModal from "@/views/lims_core/components/SysStandardCopyModal.vue";
  const queryParam = reactive<any>({});
  const userStore = useUserStore();
  const checkedKeys = ref<Array<string | number>>([]);
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerYaoDianModal, { openModal: openYaoDianModal }] = useModal();
  //注册Copymodel
  const [registerCopyModal, { openModal: openCopyuModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '标准',
      api: list,
      columns,
      canResize: false,
      clickToRowSelect: true,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [
          ['publishDate', ['publishDate_begin', 'publishDate_end'], 'YYYY-MM-DD'],
          ['effectiveDate', ['effectiveDate_begin', 'effectiveDate_end'], 'YYYY-MM-DD'],
          ['abandonedDate', ['abandonedDate_begin', 'abandonedDate_end'], 'YYYY-MM-DD'],
          ['createTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD HH:mm:ss'],
        ],
      },
      actionColumn: {
        width: 160,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '标准',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 新增药典事件
   */
  function handleAddYaoDian(){
    openYaoDianModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:sys_standard:edit',
      },
      {
        label: '克隆',
        onClick: handleclonemethods.bind(null, record),
      },
    ];
  }
  function handleclonemethods(record: Recordable) {
    openCopyuModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_core:sys_standard:delete',
      },
    ];
  }

  /**
   * 初始化字典配置
   */
  function initDictConfig() {
    loadCategoryData({ code: 'C05' }).then((res) => {
      if (res) {
        const allDictDate = userStore.getAllDictItems;
        if (!allDictDate['C05']) {
          userStore.setAllDictItems({ ...allDictDate, C05: res });
        }
      }
    });
    getRpColumns('sys_method_anlyate_selector').then((res) => {
      console.log('res', res.cgRpConfigId);
      getRpColumnsAndData(res.cgRpConfigId).then((res) => {
        console.log('res', res.data.records);
        if (res.data.records && res.data.records.length > 0) {
          const allDictDate = userStore.getAllDictItems;

          userStore.setAllDictItems({
            ...allDictDate,
            sys_method_anlyate_selector: res.data.records.map((item) => ({
              value: item.id,
              text: item.method + '#' + item.name,
            })),
          });
        }
      });
    });
  }
  initDictConfig();
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
