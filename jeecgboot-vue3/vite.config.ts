import type { UserConfig, ConfigEnv } from 'vite';
import pkg from './package.json';
import dayjs from 'dayjs';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { generateModifyVars } from './build/generate/generateModifyVars';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

const { dependencies, devDependencies, name, version } = pkg;
const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
};

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_PROXY } = viteEnv;
  const isBuild = command === 'build';

  // 修改代理配置
  const createCustomProxy = () => {
    const proxyList: [string, string][] = (VITE_PROXY || []) as [string, string][];
    const proxy: Record<string, any> = {};

    // 首先处理 OnlyOffice 相关路径
    const onlyOfficeProxy = {
      target: 'http://192.168.1.166:9997',
      changeOrigin: true,
      ws: true,
      secure: false,
      headers: {
        Connection: 'upgrade',
        'X-Forwarded-For': true,
      },
      preserveHeaderKeyCase: true,
      xfwd: true,
    };

    // 为所有 OnlyOffice 路径使用相同的代理配置
    ['/web-apps/', '/8.3.3-506166f1bdb677471023f89cf7b39ec1/', '/8.3.3-7f0847e33368e9238b736c1cf90ab8e0/', '/example/', '/app', '/cache/'].forEach(
      (prefix) => {
        proxy[prefix] = { ...onlyOfficeProxy };
      }
    );

    // 处理插件路径
    proxy['/ooplugins'] = {
      target: 'http://localhost:3380', // 确保指向本地开发服务器
      changeOrigin: true,
      pathRewrite: {
        '^/ooplugins': '/ooplugins', // 保持路径不变
      },
    };

    // 处理插件路径
    proxy['/pdfjs'] = {
      target: 'https://lims.guobiaotest.com:3380', // 确保指向本地开发服务器
      changeOrigin: true,
      pathRewrite: {
        '^/pdfjs': '/pdfjs', // 保持路径不变
      },
    };

    // 处理其他路径
    proxyList.forEach(([prefix, target]) => {
      // 跳过已处理的 OnlyOffice 路径
      if (
        [
          '/web-apps/',
          '/8.3.3-0d10b80972d36ff5a943a921d724982a/',
          '/8.3.3-7f0847e33368e9238b736c1cf90ab8e0/',
          '/example/',
          '/app',
          '/cache/',
        ].includes(prefix)
      ) {
        return;
      }

      // 其他路径使用原有的代理配置
      proxy[prefix] = {
        target: target.endsWith('/') ? target.slice(0, -1) : target,
        changeOrigin: true,
        rewrite: (path: string) => path.replace(new RegExp(`^${prefix}`), ''),
      };
    });
    return proxy;
  };

  return {
    base: VITE_PUBLIC_PATH,
    root,
    resolve: {
      alias: [
        {
          find: 'vue-i18n',
          replacement: 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        {
          find: /\/@\//,
          replacement: pathResolve('src') + '/',
        },
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: /@\//,
          replacement: pathResolve('src') + '/',
        },
        {
          find: /#\//,
          replacement: pathResolve('types') + '/',
        },
      ],
    },
    server: {
      host: true,
      // @ts-ignore
      https: false,
      port: VITE_PORT,
      cors: true,
      proxy: createCustomProxy(),
    },
    build: {
      minify: 'esbuild',
      target: 'es2020',
      cssTarget: 'chrome100',
      outDir: OUTPUT_DIR,
      rollupOptions: {
        treeshake: false,
        output: {
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router'],
            'antd-vue-vendor': ['ant-design-vue', '@ant-design/icons-vue', '@ant-design/colors'],
            'vxe-table-vendor': ['vxe-table', 'xe-utils', '@vxe-ui/plugin-render-antd'],
            'emoji-mart-vue-fast': ['emoji-mart-vue-fast'],
            'china-area-data-vendor': ['china-area-data'],
          },
        },
      },
      reportCompressedSize: false,
      chunkSizeWarningLimit: 2000,
    },
    esbuild: {
      drop: isBuild ? ['console', 'debugger'] : [],
      supported: {
        bigint: true,
      },
    },
    define: {
      __INTLIFY_PROD_DEVTOOLS__: false,
      __APP_INFO__: JSON.stringify(__APP_INFO__),
    },
    css: {
      preprocessorOptions: {
        less: {
          modifyVars: generateModifyVars(),
          javascriptEnabled: true,
        },
      },
    },
    plugins: createVitePlugins(viteEnv, isBuild),
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020',
      },
      exclude: ['@jeecg/online', 'fsevents'],
    },
  };
};
