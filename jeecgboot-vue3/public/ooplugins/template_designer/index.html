<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模板设计器</title>
    <style>
        /* 容器样式 */
        #treeContainer {
            height: 100vh;
            /* 使用视窗高度 */
            overflow-y: auto;
            /* 添加垂直滚动条 */
            padding: 10px;
            /* 美化滚动条 */
            scrollbar-width: thin;
            /* Firefox */
            scrollbar-color: #888 #f1f1f1;
            /* Firefox */
        }

        /* Webkit 浏览器的滚动条样式 */
        #treeContainer::-webkit-scrollbar {
            width: 6px;
            /* 滚动条宽度 */
        }

        #treeContainer::-webkit-scrollbar-track {
            background: #f1f1f1;
            /* 滚动条轨道颜色 */
            border-radius: 3px;
        }

        #treeContainer::-webkit-scrollbar-thumb {
            background: #888;
            /* 滚动条颜色 */
            border-radius: 3px;
        }

        #treeContainer::-webkit-scrollbar-thumb:hover {
            background: #555;
            /* 鼠标悬停时的滚动条颜色 */
        }

        /* 基础样式 */
        .tree-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .tree-node {
            padding: 2px 0;
            list-style: none;
        }

        /* 节点内容样式 */
        .node-content {
            display: flex;
            align-items: center;
            padding: 1px 0;
        }

        .node-text {
            margin-left: 1px;
            user-select: none;
        }

        /* 图标样式 */
        .icon-folder,
        .icon-folder-opened,
        .icon-file {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 14px;
            height: 14px;
            margin-right: 2px;
        }

        .icon-folder::before {
            content: '►';
            color: #666;
        }

        .icon-folder-opened::before {
            content: '▼';
            color: #666;
        }

        .icon-file::before {
            content: '⚏';
            /* 选项1：水平线条，像表单字段 */
            font-size: 14px;
            color: #666;
        }

        /* 子节点容器样式 */
        .children {
            display: none;
            padding-left: 20px;
        }

        .tree-node.expanded>.children {
            display: block;
        }

        /* 拖拽样式 */
        .tree-node.dragging {
            opacity: 0.5;
        }

        /* 添加数据库图标样式 */
        .icon-database {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 14px;
            height: 14px;
            margin-right: 2px;
        }

        .icon-database::before {
            content: '⌸';
            /* 选项1：圆柱体 */
            font-size: 10px;
            color: #666;
        }

        /* 添加表格图标样式 */
        .icon-table {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 14px;
            height: 14px;
            margin-right: 2px;
        }

        .icon-table::before {
            content: '▦';
            /* 或者使用其他表格图标: '🗓️', '📑', '📋' */
            font-size: 12px;
        }

        /* 箭头图标样式 */
        .icon-arrow {
            display: inline-block;
            width: 12px;
            height: 16px;
            line-height: 16px;
            text-align: center;
            cursor: pointer;
            user-select: none;
            margin-right: 1px;
        }

        .icon-arrow-right::before {
            content: '►';
            font-size: 8px;
            color: #666;
        }

        .icon-arrow-down::before {
            content: '▼';
            font-size: 8px;
            color: #666;
        }

        /* 空白占位符样式 */
        .icon-spacer {
            display: inline-block;
            width: 12px;
            height: 16px;
            margin-right: 1px;
        }

        /* 添加动作图标样式 */
        .icon-action {
            display: inline-block;
            width: 14px;
            height: 14px;
            line-height: 14px;
            text-align: center;
            cursor: pointer;
            user-select: none;
        }

        .icon-action::before {
            content: '⚡';
            font-size: 14px;
            color: #666;
        }

        .icon-subreport::before {
            content: '📄';
            /* 选项：文档图标 */
            font-size: 10px;
            color: #FF5733;
        }

        .icon-subtemplate {
            display: inline-flex; /* 使图标和文本在同一行 */
            align-items: center; /* 垂直居中对齐 */
            justify-content: center; /* 水平居中对齐 */
            border: 1px solid #0078D4; /* 边框颜色和样式 */
            border-radius: 4px; /* 圆角边框 */
            padding: 2px; /* 内边距，增加一些空间 */
            height: 10px; /* 设置固定高度以确保居中 */
            width: 10px; /* 设置固定宽度以确保居中 */
        }

        .icon-subtemplate::before {
            content: 'W';  /* 使用 W 字符 */
            font-size: 8px; /* 可以根据需要调整字体大小 */
            color: #0078D4; /* Word 图标的颜色，可以根据需要调整 */
            font-weight: bold; /* 加粗 */
        }
        .tree-node.highlighted {
            background-color: #FFD700 !important; /* 强制高亮背景色 */
            color: black !important; /* 强制选中时的文字颜色 */
        }
    </style>
    <script src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.js"></script>
    <link rel="stylesheet" href="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.css">
    <script src="datasource.js"></script>
</head>

<body>
    <div id="treeContainer"></div>
    <script>
      // 跨iframe消息中转脚本
      window.addEventListener('message', function(event) {
        // 只处理拖拽相关消息
        if (event.data && event.data.type && (event.data.type.startsWith('drag') || event.data.type === 'onPluginEvent')) {
          // 转发到 frameEditor
          var editorFrame = parent.document.querySelector('iframe[name="frameEditor"]');
          if (editorFrame && editorFrame.contentWindow) {
            editorFrame.contentWindow.postMessage(event.data, '*');
          }
        }
      });
    </script>
</body>

</html>