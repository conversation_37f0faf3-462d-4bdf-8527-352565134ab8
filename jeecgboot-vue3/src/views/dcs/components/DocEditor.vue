<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :maskClosable="false" destroyOnClose :title="ooTitle" :width="800" :footer="null" @full-screen="handleFullScreen">
    <div class="editor-container">
      <DocumentEditor
        id="docEditorInstance"
        ref="docEditorRef"
        :documentServerUrl="documentServerUrl"
        :config="config"
        :events_onDocumentReady="onDocumentReady"
        :onLoadComponentError="onLoadComponentError"
      />
      <!-- 根据传入的按钮配置动态渲染浮动按钮 -->
      <template v-if="floatButtons && floatButtons.length > 0">
        <FloatButton
          v-for="(btn, index) in floatButtons"
          :key="index"
          :tooltip="btn.tooltip"
          :type="btn.type || 'primary'"
          class="doc-float-button"
          :style="{
            right: '24px',
            bottom: `${24 + index * 60}px`,
          }"
          @click="handleButtonClick(btn)"
        >
          <template #icon>
            <component :is="getIconComponent(btn.icon)" />
          </template>
        </FloatButton>
      </template>
    </div>
  </BasicModal>
  <MailModal @register="registerMailModal" />
</template>

<script setup lang="ts">
  import { ref, defineProps, defineEmits } from 'vue';
  import { getOnlyOfficeGlobalConfig } from '@/utils/onlyOfficeGlobalConfig';
  import { DocumentEditor } from '@onlyoffice/document-editor-vue';
  import { BasicModal, useModal, useModalInner } from '@/components/Modal';
  import { useGlobSetting } from '@/hooks/setting';
  import { FloatButton } from 'ant-design-vue';
  import * as Icons from '@ant-design/icons-vue';
  import MailModal from './mail/MailModal.vue';

  // 定义按钮配置类型
  interface FloatButtonConfig {
    tooltip: string;
    icon: string | any;
    type?: 'primary' | 'default' | 'dashed' | 'text' | 'link';
    action: string;
  }

  // 获取图标组件
  const getIconComponent = (icon: string | any) => {
    // 如果已经是组件对象，直接返回
    if (typeof icon !== 'string') {
      return icon;
    }

    // 如果是字符串，从Icons中获取对应的组件
    return Icons[icon];
  };

  // 定义组件属性
  const props = defineProps({
    floatButtons: {
      type: Array as () => FloatButtonConfig[],
      default: () => [],
    },
  });

  // 定义事件
  const emit = defineEmits(['register', 'buttonClick']);

  const [registerMailModal, { openModal: openMailModal }] = useModal();

  const docEditorRef = ref();
  const config = ref({});
  const ooTitle = ref('');
  const documentServerUrl = ref(getOnlyOfficeGlobalConfig().onlyOffice.documentServerUrl);
  const { title } = useGlobSetting();

  // 处理按钮点击事件
  const handleButtonClick = (btn: FloatButtonConfig) => {
    // 获取编辑器实例
    const editorInstance = docEditorRef.value;
    // 触发外部事件
    emit('buttonClick', {
      action: btn.action,
      editorInstance,
    });

    // 如果是邮件按钮，保留原有逻辑
    if (btn.action === 'email') {
      handleEmail();
    }
  };

  // 处理导出按钮点击事件
  const handleEmail = () => {
    // 获取编辑器实例
    const editorInstance = docEditorRef.value;
    if (editorInstance) {
      openMailModal(true, config.value?.editorConfig.plugins.options.all);
      console.log('发送邮件');
    }
  };

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    console.log('data', data);
    setModalProps({ confirmLoading: false, height: 600, open: true });
    if (data) {
      ooTitle.value = title;
      data.editorConfig.callbackUrl = getOnlyOfficeGlobalConfig().onlyOffice.editorConfig.callbackUrl;
      // var events = {
      //   onRequestHistory,
      //   onRequestHistoryData,
      //   onRequestRestore,
      //   onRequestHistoryClose
      // }
      // data.events= events;
      data.editorConfig.canHistoryClose = true;
      data.editorConfig.canHistoryRestore = true;
      data.editorConfig.canUseHistory = true;
      var options = {
        cache: false,
      };
      data.document.options = options;
      config.value = data;
      console.log('config.value=》》》》', config.value);
    }
  });
  // 处理 fullscreen 事件
  const handleFullScreen = () => {
    config.value.height = '100%';
  };

  // 定义方法
  const onDocumentReady = () => {
    // 在文档准备就绪后添加拖放事件监听器
    // 正确获取编辑器实例
    const editorInstance = docEditorRef.value;
    // 通过官方API创建连接器
    if (editorInstance) {
      console.log('window', window.DocEditor.instances);
      var connector = window.DocEditor.instances.docEditorInstance.createConnector();
      console.log('connector', connector);
      // 设置连接器的回调函数
      if (connector) {
        window.connector = connector;
        connector.attachEvent('onExternalMouseUp', function () {
          // 当在编辑器外部释放鼠标时触发
          var event = document.createEvent('MouseEvents');
          event.initMouseEvent('mouseup', true, true, window, 1, 0, 0, 0, 0, false, false, false, false, 0, null);
          document.dispatchEvent(event);
        });
      }
      // 推荐将实例挂在Vue实例上（可选）
      // app.config.globalProperties.$docEditor = editorInstance;
    }
    const docEditorIframe = document.querySelector('iframe[name="frameEditor"]') as HTMLIFrameElement;
    if (docEditorIframe) {
      const contentWindow = docEditorIframe.contentWindow;
      const documentBody = contentWindow?.document.getElementById('id_main_view');
      if (documentBody) {
        documentBody.addEventListener('dragover', (event) => {
          event.preventDefault(); // 允许拖放
          event.stopPropagation();
          event.dataTransfer.dropEffect = 'copy';
        });
        documentBody.addEventListener('dragEnd', onDragEnd);
        documentBody.addEventListener('drop', onDrop);
      }
    }
  };

  const onLoadComponentError = (errorCode: number, errorDescription: string) => {
    console.error('Error loading component:', errorDescription);
  };

  const onDragEnd = (event: DragEvent) => {
    console.log('==========onDragEnd=======>', event);
    if (event.dataTransfer.items) {
      // Use DataTransferItemList interface to remove the drag data
      for (var i = 0; i < event.dataTransfer.items.length; i++) {
        event.dataTransfer.items.remove(i);
      }
    } else {
      // Use DataTransfer interface to remove the drag data
      event.dataTransfer.clearData();
    }
  };

  const onRequestHistory = () => {
    //docEditor.refreshHistory()
  };

  const onRequestHistoryData = (event) => {
    const version = event.data;
    // docEditor.setHistoryData(
    // )
  };

  const onRequestRestore = (event) => {
    const fileType = event.data.fileType;
    const url = event.data.url;
    const version = event.data.version;

    // docEditor.refreshHistory({
    //   currentVersion: 2,
    //   history: [
    //     {
    //       created: "2010-07-06 10:13 AM",
    //       key: "af86C7e71Ca8",
    //       user: {
    //         id: "F89d8069ba2b",
    //         name: "Kate Cage",
    //       },
    //       version: 1,
    //     },
    //     {
    //       changes,
    //       created: "2010-07-07 3:46 PM",
    //       key: "Khirz6zTPdfd7",
    //       serverVersion,
    //       user: {
    //         id: "78e1e841",
    //         name: "John Smith",
    //       },
    //       version: 2,
    //     },
    //   ],
    // })
  };

  const onRequestHistoryClose = () => {
    document.location.reload();
  };

  const onDrop = (event: DragEvent) => {
    event.preventDefault(); // 防止默认行为
    event.stopPropagation();
    //debugger;
    const droppedField = event.dataTransfer?.getData('text/plain'); // 获取拖放的数据
    //console.log('Dropped data:', droppedField);
    if (droppedField) {
      const docEditorIframe = document.querySelector('iframe[name="frameEditor"]') as HTMLIFrameElement;
      const pluginIframe = docEditorIframe.contentWindow?.document.querySelector('iframe[name="pluginFrameEditor"]') as HTMLIFrameElement;
      if (pluginIframe && pluginIframe.contentWindow) {
        // 传递 ctrlKey 状态到插件
        pluginIframe.contentWindow.postMessage(
          {
            type: 'customDropEvent',
            data: droppedField,
            ctrlKey: event.ctrlKey, // 添加 Ctrl 键状态
          },
          '*'
        );
      }
    }
  };
</script>

<style lang="less">
  .jeecg-modal-content > .scroll-container {
    padding: 0px;
  }
  body > div:nth-child(11) > div > div.ant-modal-wrap > div > div:nth-child(1) > div > div.ant-modal-footer {
    padding: 0px;
    border-top: 0px solid rgba(5, 5, 5, 0.06);
  }
  body
    > div:nth-child(9)
    > div
    > div.ant-modal-wrap
    > div
    > div:nth-child(1)
    > div
    > div.ant-modal-bodyconst
    pl
    > div
    > div.ant-col.ant-col-24.jeecg-modal-content.css-dev-only-do-not-override-9m98ij
    > div
    > div.scrollbar__wrap.scrollbar__wrap--hidden-default {
    margin-bottom: 0px !important;
  }
  .scroll-container .scrollbar__wrap {
    margin-bottom: 0px !important;
  }
  #docEditorInstance {
    width: 100%;
    height: 100%;
  }

  .editor-container {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .doc-float-button {
    position: absolute !important;
    right: 24px !important;
    /* 移除固定的bottom值，让v-for中的样式生效 */
    /* bottom: 24px !important; */
    z-index: 9999 !important;
  }
</style>
