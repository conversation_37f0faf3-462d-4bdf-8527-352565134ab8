<template>
  <a-layout>
    <a-layout-content style="padding: 20px 20px">
      <a-layout style="padding: 24px 0; background: #fff">
        <a-layout-sider width="200" style="background: #fff">
          <a-menu
            v-model:openKeys="openKeys"
            v-model:selectedKeys="selectedKeys"
            mode="inline"
            :style="{ height: '100%', borderRight: 0 }"
            :items="items"
            @click="handleClick"
          />
        </a-layout-sider>
        <a-layout-content :style="{ padding: '0 24px', minHeight: '280px' }">
          <BasicTable @register="registerTable" :rowClassName="getRowClassName">
            <template #tableTitle>
              <a-button type="primary" @click="handleCreate"> 新增</a-button>
            </template>
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :actions="getTableAction(record)" />
              </template>
            </template>
          </BasicTable>
          <DictItemModal @register="registerModal" @success="reload" :dictId="dictId" />
        </a-layout-content>
      </a-layout>
    </a-layout-content>
  </a-layout>
</template>
<script lang="ts" setup>
  import { h, onMounted, ref, unref } from 'vue';
  import { MailOutlined, CalendarOutlined, AppstoreOutlined, SettingOutlined } from '@ant-design/icons-vue';
  import type { MenuProps } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction } from '/src/components/Table';
  import { deleteItem, itemList, list, listByDictCode } from '@/views/system/dict/dict.api';
  import { dictItemColumns, dictItemSearchFormSchema } from '@/views/system/dict/dict.data';
  import { ColEx } from '@/components/Form/src/types';
  import DictItemModal from '@/views/system/dict/components/DictItemModal.vue';
  import { useModal } from '@/components/Modal';
  import { useDesign } from '@/hooks/web/useDesign';
  const { prefixCls } = useDesign('row-invalid');
  const dictId = ref('');
  // 自适应列配置
  const adaptiveColProps: Partial<ColEx> = {
    xs: 24, // <576px
    sm: 24, // ≥576px
    md: 24, // ≥768px
    lg: 12, // ≥992px
    xl: 12, // ≥1200px
    xxl: 8, // ≥1600px
  };

  const [registerTable, { reload, setProps }] = useTable({
    //需要配置rowKey，否则会有警告
    rowKey: 'dictId',
    api: itemList,
    columns: dictItemColumns,
    formConfig: {
      baseColProps: adaptiveColProps,
      labelAlign: 'right',
      labelCol: {
        offset: 1,
        xs: 24,
        sm: 24,
        md: 24,
        lg: 9,
        xl: 7,
        xxl: 4,
      },
      wrapperCol: {},
      schemas: dictItemSearchFormSchema,
      autoSubmitOnEnter: true,
      actionColOptions: {
        span: 24,
      },
    },
    striped: true,
    useSearchForm: true,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    immediate: false,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
      //slots: { customRender: 'action' },
      fixed: undefined,
    },
  });

  const [registerModal, { openModal }] = useModal();

  const selectedKeys = ref([]);
  const openKeys = ref([]);
  const items = ref([]);
  const handleClick: MenuProps['onClick'] = (menuInfo) => {
    console.log('click ', menuInfo);

    setProps({ searchInfo: { dictId: unref(menuInfo.key) } });
    dictId.value = menuInfo.key;
    reload();
  };

  /**
   * 新增
   */
  function handleCreate() {
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 编辑
   */
  function handleEdit(record) {
    openModal(true, {
      record,
      isUpdate: true,
    });
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
  /**
   * 删除
   */
  async function handleDelete(record) {
    await deleteItem({ id: record.id }, reload);
  }
  function getRowClassName(record) {
    return record.status == 0 ? prefixCls : '';
  }

  onMounted(() => {
    listByDictCode({ dictCode: 'lab_dict' }).then((res) => {
      res.forEach((item) => {
        items.value.push({
          key: item.dictId,
          icon: () => h(AppstoreOutlined),
          label: item.itemText,
          title: item.itemText,
        });
      });

      selectedKeys.value = [res.records[0].id];
      dictId.value = res.records[0].id;
      setProps({ searchInfo: { dictId: unref(dictId) } });
      reload();
    });
  });
</script>
<style scoped>
  #components-layout-demo-top-side .logo {
    float: left;
    width: 120px;
    height: 31px;
    margin: 16px 24px 16px 0;
    background: rgba(255, 255, 255, 0.3);
  }

  .ant-row-rtl #components-layout-demo-top-side .logo {
    float: right;
    margin: 16px 0 16px 24px;
  }

  .site-layout-background {
    background: #fff;
  }
</style>
