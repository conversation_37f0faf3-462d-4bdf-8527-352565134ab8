/* 字体样式 */
@import "./assets/css/iconfont.css";

/* 定义全局样式 */
/* 在 main.js 入库文件引入 */
body {
  margin: 0;
}

/* scrollbar */
::-webkit-scrollbar {
  height: 4px;
  width: 4px;
}
::-webkit-scrollbar-corner {
  height: 4px;
  width: 4px;
}
::-webkit-scrollbar-thumb {
  background: purple;
  border-radius: 2px;
  background-image: -webkit-linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0.2) 75%,
    transparent 75%,
    transparent
  );
}
::-webkit-scrollbar-thumb:hover {
  background: purple;
}

/* flex */
.flex-row {
  display: flex;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.align-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}

.flex-1 {
  flex: 1;
}
.flex-2 {
  flex: 2;
}
.flex-3 {
  flex: 3;
}
.flex-4 {
  flex: 4;
}
.flex-5 {
  flex: 5;
}

.ml-10 {
  margin-left: 10px;
}
.mr-10 {
  margin-right: 10px;
}
.mt-10 {
  margin-top: 10px;
}
.mb-10 {
  margin-bottom: 10px;
}

/* button 样式 为了好看点 */
button {
  padding: 10px;
  min-width: 40px;
  color: white;
  opacity: 0.9;
  cursor: pointer;
  border-width: 0;
  border: 1px solid #d9d9d9;
}
button:hover {
  opacity: 1;
}
button i {
  font-size: 16px !important;
}
.circle,
.circle-4 {
  border-radius: 4px !important;
}
.circle-10 {
  border-radius: 10px !important;
}
/* 按钮颜色 */
.primary {
  background: purple;
}
.info {
  color: #000;
  background: none;
}
.info:hover {
  color: purple;
  border-color: purple;
}
.secondary {
  background: #1976d2;
}
.warning {
  background: #d32f2f;
}

/* modal */
.modal {
  padding: 0;
  margin: 0;
}
.modal .mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  height: 100%;
  background-color: #00000073;
}
.modal .wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  overflow: auto;
  background-color: #00000073;
  outline: 0;
}
.modal .wrap .box {
  position: relative;
  margin: 10% auto;
  width: 40%;
  background: #fff;
  border-radius: 4px;
  z-index: 1001;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}
.modal-box__header {
  padding: 10px 14px;
  border-bottom: 1px solid #e9e9e9;
}
.modal-box__footer {
  text-align: end;
}
.modal-box__footer button {
  min-width: 100px;
}
.modal-box__footer button:not(:last-child) {
  margin-right: 10px;
}
