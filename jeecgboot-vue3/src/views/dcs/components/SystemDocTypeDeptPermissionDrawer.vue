<template>
  <BasicDrawer v-bind="$attrs" title="所拥有的权限" width="40%" @register="registerDrawer" :loading="loading" :showFooter="true" @ok="confirmClick">
    <a-table :dataSource="sysDocTypePermissions" :columns="tableColumns" rowKey="id" bordered style="width: 100%; margin-bottom: 20px">
      <template #permission="{ record }">
        <a-checkbox-group v-model:value="record.permission">
          <a-checkbox v-for="perm in dcs_doc_operation" :key="perm.value" :value="perm.value">
            {{ perm.label }}
          </a-checkbox>
        </a-checkbox-group>
      </template>
    </a-table>
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { ref, reactive } from 'vue';
  import { getDictItemsByCode } from '@/utils/dict';
  import { queryMyCorpIdTree } from '@/views/system/departUser/depart.user.api';
  import { SystemDocTypeDeptPermissionVO } from '@/views/dcs/SystemDocTypeDeptPermission.data';
  import { getSystemDocTypeDeptPermission, saveSystemDocTypeDeptPermission } from '@/views/dcs/SystemDocTypeDeptPermission.api';
  import { handleTree } from '@/utils/helper/treeHelper';
  //
  const loading = ref(false);
  // const message = useMessage();
  //
  // 注册抽屉组件
  const [registerDrawer, { closeDrawer }] = useDrawerInner((data) => {
    console.log('data===', data);
    let typeId: String = data.typeId;
    loadData(typeId);
  });
  const tableColumns = reactive([
    {
      title: '部门',
      dataIndex: 'deptId',
      key: 'deptId',
      customRender: ({ record }) => getDeptName(record),
    },
    {
      title: '权限',
      dataIndex: 'permission',
      key: 'permission',
      slots: { customRender: 'permission' },
    },
  ]);

  let depts = reactive([]);
  let sysDocTypePermissions = ref([]);
  let dcs_doc_operation = reactive([]);
  const getDeptName = (row) => {
    let dept = depts.find((d) => d.id === row.deptId);
    console.log('dept===>', dept);
    return dept ? dept.departName : '';
  };

  const loadData = async (typeId) => {
    loading.value = true;
    let permissions = await getSystemDocTypeDeptPermission(typeId);

    depts = await queryMyCorpIdTree();
    dcs_doc_operation = getDictItemsByCode('dcs_doc_operation');
    const existingDeptIds = new Set(permissions.map((item) => item.deptId));
    if (depts) {
      depts.forEach((dept) => {
        if (!existingDeptIds.has(dept.id)) {
          permissions.push({
            id: null,
            typeId: typeId,
            deptId: dept.id,
            parentId: dept.parentId,
            permission: [],
          });
        } else {
          let perm = permissions.find((d) => d.deptId === dept.id);
          perm.parentId = dept.parentId;
          perm.permission = perm.permission.split(',');
        }
      });
    }
    sysDocTypePermissions.value = handleTree(permissions, 'deptId', 'parentId');
    loading.value = false;
  };

  const filterAndTransformPermissions = (items) => {
    let result = [];
    for (const item of items) {
      if (Array.isArray(item.permission) && item.permission.length > 0) {
        item.permission = item.permission.join(',');
        result.push({ ...item });
      }
      if (Array.isArray(item.children) && item.children.length > 0) {
        const childrenResult = filterAndTransformPermissions(item.children);
        result = result.concat(childrenResult);
      }
    }
    return result;
  };

  const confirmClick = async () => {
    let formData = filterAndTransformPermissions(sysDocTypePermissions.value);
    try {
      loading.value = true;
      const data = formData as unknown as SystemDocTypeDeptPermissionVO;
      await saveSystemDocTypeDeptPermission(data);
      //   message.success(t('common.updateSuccess'));
      //   emit('success');
    } finally {
      loading.value = false;
      closeDrawer();
    }
  };
</script>
