/**
 * 解决 Ant Design Vue a-select 组件文本无法选择和复制的问题
 * 简化版本：主要通过CSS和简单的事件处理来实现
 */

/**
 * 初始化文本选择修复功能
 */
export function initSelectTextFix() {
  console.log('初始化 a-select 文本选择修复功能');

  // 添加全局样式来确保文本可选择
  addGlobalStyles();

  // 添加双击复制功能
  addDoubleClickCopy();

  // 添加右键菜单复制功能
  addContextMenuCopy();
}

/**
 * 添加全局样式
 */
function addGlobalStyles() {
  const styleId = 'select-text-fix-styles';

  // 避免重复添加
  if (document.getElementById(styleId)) {
    return;
  }

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    /* 强制所有 a-select 文本可选择 */
    .ant-select-selector *,
    .ant-select-selection-item *,
    .ant-select-selection-item-content * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }

    /* 添加文本选择提示 */
    .ant-select:not(.ant-select-open) .ant-select-selector:hover::after {
      content: '双击可复制文本';
      position: absolute;
      right: 25px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 12px;
      color: #999;
      pointer-events: none;
      z-index: 10;
    }
  `;

  document.head.appendChild(style);
}

/**
 * 添加双击复制功能
 */
function addDoubleClickCopy() {
  document.addEventListener('dblclick', (e) => {
    const target = e.target as HTMLElement;

    if (isSelectElement(target)) {
      e.preventDefault();
      e.stopPropagation();

      const text = getSelectText(target);
      if (text) {
        copyToClipboard(text);
        showCopyMessage('文本已复制到剪贴板');
      }
    }
  });
}

/**
 * 添加右键菜单复制功能
 */
function addContextMenuCopy() {
  document.addEventListener('contextmenu', (e) => {
    const target = e.target as HTMLElement;

    if (isSelectElement(target)) {
      // 允许默认的右键菜单，这样用户可以使用浏览器的复制功能
      return;
    }
  });
}

/**
 * 检查元素是否是 a-select 的选择器相关元素
 */
function isSelectElement(element: HTMLElement): boolean {
  if (!element) return false;

  // 检查元素本身或其父元素是否包含相关类名
  let current = element;
  let depth = 0;

  while (current && depth < 10) {
    const classList = current.classList;

    if (classList.contains('ant-select-selector') ||
        classList.contains('ant-select-selection-item') ||
        classList.contains('ant-select-selection-item-content')) {
      return true;
    }

    current = current.parentElement as HTMLElement;
    depth++;
  }

  return false;
}

/**
 * 获取选择器中的文本内容
 */
function getSelectText(element: HTMLElement): string {
  // 尝试从不同的元素中获取文本
  let text = '';

  // 首先尝试从选中项获取文本
  const selectionItem = element.closest('.ant-select-selection-item');
  if (selectionItem) {
    text = selectionItem.textContent || selectionItem.innerText || '';
  }

  // 如果没有找到，尝试从选择器获取文本
  if (!text) {
    const selector = element.closest('.ant-select-selector');
    if (selector) {
      text = selector.textContent || selector.innerText || '';
    }
  }

  // 如果还是没有找到，直接从当前元素获取
  if (!text) {
    text = element.textContent || element.innerText || '';
  }

  return text.trim();
}

/**
 * 复制文本到剪贴板
 */
async function copyToClipboard(text: string): Promise<void> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      document.execCommand('copy');
      textArea.remove();
    }
  } catch (err) {
    console.error('复制到剪贴板失败:', err);
  }
}

/**
 * 显示复制成功消息
 */
function showCopyMessage(message: string) {
  // 创建一个临时的提示元素
  const toast = document.createElement('div');
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 9999;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  `;

  document.body.appendChild(toast);

  // 2秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 2000);
}

/**
 * 为特定的 a-select 元素启用文本选择功能
 */
export function enableTextSelection(selector: string | HTMLElement) {
  const elements = typeof selector === 'string'
    ? document.querySelectorAll(selector)
    : [selector];

  elements.forEach(element => {
    if (element) {
      element.classList.add('text-selectable');
    }
  });
}

/**
 * 禁用特定 a-select 元素的文本选择功能
 */
export function disableTextSelection(selector: string | HTMLElement) {
  const elements = typeof selector === 'string'
    ? document.querySelectorAll(selector)
    : [selector];

  elements.forEach(element => {
    if (element) {
      element.classList.remove('text-selectable');
    }
  });
}
