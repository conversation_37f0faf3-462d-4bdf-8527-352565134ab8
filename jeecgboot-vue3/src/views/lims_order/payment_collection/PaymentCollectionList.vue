<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'lims_order:payment_collection:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls">
          导出</a-button
        >
        <j-upload-button type="primary" v-auth="'lims_order:payment_collection:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_order:payment_collection:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"  />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <PaymentCollectionModal @register="registerModal" @success="handleSuccess" />
    <PaymentCollectionAddModal @register="registerAddModal" @success="handleSuccess" />
    <PaymentModal @register="registerPaymentModal" @success="handleSuccess" ref="paymentModal" />
  </div>
</template>

<script lang="tsx" name="lims_order-paymentCollection" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import PaymentCollectionModal from './components/PaymentCollectionModal.vue';
  import PaymentCollectionAddModal from './components/PaymentCollectionAddModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './PaymentCollection.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './PaymentCollection.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import PaymentModal from "@/views/lims_order/payment/components/PaymentModal.vue";
  import PaymentTable from "@/views/lims_order/payment/PaymentTable.vue";
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const paymentModal = ref();
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerAddModal, { openModal:openAddModal }] = useModal();
  const [registerPaymentModal, { openModal: openPaymentModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '回款计划',
      api: list,
      columns,
      expandedRowRender: (record) => {
        console.log('record', record);
        return <PaymentTable paymentCollection={record.record} onSuccess={handleSuccess} />;
      },
      canResize: false,
      clickToRowSelect: true,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,

        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 150,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '回款计划',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openAddModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
    paymentModal.value.doReload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_order:sample:edit',
      },
      {
        label: '添加回款',
        onClick: handlePaymentAdd.bind(null, record),
        auth: 'lims_order:sample:edit',
      },
    ];
  }
  function handlePaymentAdd(record: Recordable) {
    let obj = {
      paymentCollectionId: record.id,
      planAmount: record.planAmount,
      actualAmount: record.actualAmount,
    };
    openPaymentModal(true, {
      record:obj,
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_order:payment_collection:delete',
      },
    ];
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
