<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
      <BasicForm @register="registerForm" ref="formRef" name="SysMethodAnalyteForm" />
      <!-- 子表单区域 -->
      <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
        <a-tab-pane tab="修约要求" key="sysMethodAnalyteRouding" :forceRender="true">
          <JVxeTable
            keep-source
            resizable
            ref="sysMethodAnalyteRouding"
            :loading="sysMethodAnalyteRoudingTable.loading"
            :columns="sysMethodAnalyteRoudingTable.columns"
            :dataSource="sysMethodAnalyteRoudingTable.dataSource"
            :height="340"
            :disabled="formDisabled"
            :rowNumber="true"
            :rowSelection="true"
            :toolbar="true"
            />
        </a-tab-pane>
        <a-tab-pane tab="精密度要求" key="sysMethodAnalytePrecision" :forceRender="true">
          <JVxeTable
            keep-source
            resizable
            ref="sysMethodAnalytePrecision"
            :loading="sysMethodAnalytePrecisionTable.loading"
            :columns="sysMethodAnalytePrecisionTable.columns"
            :dataSource="sysMethodAnalytePrecisionTable.dataSource"
            :height="340"
            :disabled="formDisabled"
            :rowNumber="true"
            :rowSelection="true"
            :toolbar="true"
            />
        </a-tab-pane>
      </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { JVxeTable } from '/@/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts'
    import {sysMethodAnalyteRoudingJVxeColumns,sysMethodAnalytePrecisionJVxeColumns} from '../SysMethodAnalyte.data';
    import {saveOrUpdate,querySysMethodAnalyteRouding,querySysMethodAnalytePrecision} from '../SysMethodAnalyte.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const formDisabled = ref(false);
    const refKeys = ref(['sysMethodAnalyteRouding', 'sysMethodAnalytePrecision', ]);
    const activeKey = ref('sysMethodAnalyteRouding');
    const sysMethodAnalyteRouding = ref();
    const sysMethodAnalytePrecision = ref();
    const tableRefs = {sysMethodAnalyteRouding, sysMethodAnalytePrecision, };
    const sysMethodAnalyteRoudingTable = reactive({
          loading: false,
          dataSource: [],
          columns:sysMethodAnalyteRoudingJVxeColumns
    })
    const sysMethodAnalytePrecisionTable = reactive({
          loading: false,
          dataSource: [],
          columns:sysMethodAnalytePrecisionJVxeColumns
    })
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        formDisabled.value = !data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
             requestSubTableData(querySysMethodAnalyteRouding, {id:data?.record?.id}, sysMethodAnalyteRoudingTable)
             requestSubTableData(querySysMethodAnalytePrecision, {id:data?.record?.id}, sysMethodAnalytePrecisionTable)
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys);

    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

    async function reset(){
      await resetFields();
      activeKey.value = 'sysMethodAnalyteRouding';
      sysMethodAnalyteRoudingTable.dataSource = [];
      sysMethodAnalytePrecisionTable.dataSource = [];
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
           sysMethodAnalyteRoudingList: allValues.tablesValue[0].tableData,
           sysMethodAnalytePrecisionList: allValues.tablesValue[1].tableData,
         }
       }
    //表单提交事件
    async function requestAddOrEdit(values) {
        try {
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
