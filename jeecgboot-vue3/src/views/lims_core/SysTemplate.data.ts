import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '名称',
    align: 'left',
    dataIndex: 'name',
    resizable: true,
    fixed: 'left',
   },
   {
    title: '类别',
    align: 'center',
    dataIndex: 'typeId_dictText'
   },
  {
    title: '语言',
    align: 'center',
    dataIndex: 'reportLanguage_dictText'
  },
   {
    title: '适用的营销产品',
    align: 'center',
    dataIndex: 'packageId_dictText'
   },
   {
    title: '适用的检测能力',
    align: 'center',
    dataIndex: 'capabilityId_dictText'
   },
   {
    title: '电子文件',
    align: 'center',
    dataIndex: 'url',
   },
   {
    title: '循环参数',
    align: 'center',
    dataIndex: 'loopPara'
   },
   {
    title: '可用',
    align: 'center',
    dataIndex: 'isEnabled',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}])
     },
   },
   {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText'
   },
   {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
     {
      label: "名称",
      field: "name",
      component: 'Input', //TODO 范围查询
      //colProps: {span: 6},
	},
	{
      label: "类别",
      field: "typeId",
      component: 'JSearchSelect',
      componentProps:{
         dict:"sys_template_type"
      },
      //colProps: {span: 6},
     },

	{
      label: "创建人",
      field: "createBy",
      component: 'Input',
      //colProps: {span: 6},
     },
     {
      label: "创建日期",
      field: "createTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入名称!'},
          ];
     },
  },
  {
    label: '类别',
    field: 'typeId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"sys_template_type"
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类别!'},
          ];
     },
  },
  {
    label: '语言',
    field: 'reportLanguage',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"report_language"
    },
  },
  {
    label: '适用的营销产品',
    field: 'packageId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"sys_product_package,name,id"
    },
  },
  {
    label: '适用的检测能力',
    field: 'capabilityId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"sys_capability,name,id"
    },
  },
  {
    label: '电子文件',
    field: 'url',
    component: 'JUpload',
    componentProps:{
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入电子文件!'},
          ];
     },
  },
  {
    label: '循环参数',
    field: 'loopPara',
    component: 'Input',
  },
  {
    label: '可用',
    field: 'isEnabled',
    defaultValue: 1,
     component: 'JSwitch',
     componentProps:{
         options:[1,0]
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入可用!'},
          ];
     },
  },
  {
    label: '',
    field: 'parentId',
    component: 'Input',
    show: false,
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '名称',order: 0,view: 'text', type: 'string',},
  typeId: {title: '类别',order: 1,view: 'sel_search', type: 'string',dictCode: 'sys_template_type',},
  packageId: {title: '适用的营销产品',order: 2,view: 'sel_search', type: 'string',dictTable: "sys_product_package", dictCode: 'id', dictText: 'name',},
  capabilityId: {title: '适用的检测能力',order: 3,view: 'sel_search', type: 'string',dictTable: "capability", dictCode: 'id', dictText: 'name',},
  url: {title: '电子文件',order: 4,view: 'file', type: 'string',},
  loopPara: {title: '循环参数',order: 5,view: 'text', type: 'string',},
  isEnabled: {title: '可用',order: 6,view: 'number', type: 'number',},
  createBy: {title: '创建人',order: 7,view: 'text', type: 'string',},
  createTime: {title: '创建日期',order: 8,view: 'datetime', type: 'string',},
};


/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
