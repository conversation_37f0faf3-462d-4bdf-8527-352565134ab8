import 'uno.css';
import '/@/design/index.less';
import 'ant-design-vue/dist/reset.css';
import 'vue-plugin-hiprint/dist/print-lock.css';

// 注册图标
import 'virtual:svg-icons-register';

// 注册 PWA
// import { registerSW } from 'virtual:pwa-register';

import App from './App.vue';
import { createApp } from 'vue';
import { initAppConfigStore } from '/@/logics/initAppConfig';
import { setupErrorHandle } from '/@/logics/error-handle';
import { router, setupRouter } from '/@/router';
import { setupRouterGuard } from '/@/router/guard';
import { setupStore } from '/@/store';
import { setupGlobDirectives } from '/@/directives';
import { setupI18n } from '/@/locales/setupI18n';
import { registerGlobComp } from '/@/components/registerGlobComp';
import { registerThirdComp } from '/@/settings/registerThirdComp';
import { useSso } from '/@/hooks/web/useSso';
// 注册online模块lib
import { registerPackages } from '/@/utils/monorepo/registerPackages';
import Hiprint from 'vue-plugin-hiprint';
// 导入表单项双击复制功能（增强版）
import { initFormItemCopyTooltip } from '/src/utils/formItemCopyHelper';

async function bootstrap() {
  // 创建应用实例
  const app = createApp(App);
  // 【QQYUN-6329】
  window.appRootInstance = app;
  // 多语言配置,异步情况:语言文件可以从服务器端获得
  await setupI18n(app);

  // 配置存储
  setupStore(app);

  // 初始化内部系统配置
  initAppConfigStore();

  // 注册外部模块路由(注册online模块lib)
  registerPackages(app);

  // 注册全局组件
  registerGlobComp(app);

  //CAS单点登录
  await useSso().ssoLogin();

  // 配置路由
  setupRouter(app);

  // 路由保护
  setupRouterGuard(router);

  // 注册全局指令
  setupGlobDirectives(app);

  // 配置全局错误处理
  setupErrorHandle(app);

  // 注册第三方组件
  await registerThirdComp(app);

  // 注册第三方插件
  console.log('注册 Hiprint 插件');
  app.use(Hiprint);

  // 当路由准备好时再执行挂载( https://next.router.vuejs.org/api/#isready)
  await router.isReady();
  // 注册 Service Worker - 添加自动更新逻辑
  // const updateSW = registerSW({
  //   onNeedRefresh() {
  //     console.log('发现新版本，正在自动更新...');
  //     // 自动更新，无需用户交互
  //     updateSW(true);
  //   },
  //   onOfflineReady() {
  //     console.log('应用已准备好离线使用');
  //   },
  // });

  // 挂载应用
  console.log('挂载应用');
  app.mount('#app', true);

  // 临时禁用表单项双击复制功能
  // initFormItemCopyTooltip();
  // console.log('表单项双击复制功能已初始化');

  console.log(' vue3 app 加载完成！');
}

bootstrap();
