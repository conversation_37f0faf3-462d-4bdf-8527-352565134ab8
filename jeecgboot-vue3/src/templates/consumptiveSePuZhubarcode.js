export default {
  panels: [
    {
      index: 0,
      name: 1,
      height: 40,
      width: 60,
      paperHeader: 49.5,
      paperFooter: 105,
      printElements: [
        {
          options: {
            left: 0,
            top: 0,
            height: 72,
            width: 168,
            right: 168,
            bottom: 90,
            vCenter: 84,
            hCenter: 45,
          },
          printElementType: {
            title: '矩形',
            type: 'rect',
          },
        },
        {
          options: {
            left: 0,
            top: 12,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 24,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 36,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 48,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 60,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 32,
            top: 12,
            height:60,
            width: 9,
          },
          printElementType: {
            title: '竖线',
            type: 'vline',
          },
        },
        {
          options: {
            left: 80,
            top: 3,
            height: 9.75,
            width: 167,
            title: '色谱柱标签',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 6,
            top: 15,
            height: 9.75,
            width: 33,
            title: '编号',
            right: 124.5,
            bottom: 21.75,
            vCenter: 64.5,
            hCenter: 16.875,
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 15,
            width: 120,
            testData: '编号',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'code',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 6,
            top: 27,
            height: 9.75,
            width: 33,
            title: '名称',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 27,
            width: 120,
            testData: '名称',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'name',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 39,
            height: 9.75,
            width: 33,
            title: '规格',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 39,
            width: 120,
            testData: '规格',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'spec',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 51,
            height: 9.75,
            width: 33,
            title: '提供方',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 51,
            width: 120,
            testData: '提供方',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'customerId_dictText',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 63,
            height: 9.75,
            width: 33,
            title: '适用样品',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 63,
            width: 120,
            testData: '适用样品',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'sampleId_dictText',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
      ],
      paperNumberContinue: false,
      paperNumberLeft: -10,
      paperNumberTop: -10,
      watermarkOptions: {},
      panelLayoutOptions: {},
    },
  ],
};
