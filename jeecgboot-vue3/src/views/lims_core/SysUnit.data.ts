import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'unitName',
    sorter: true,
  },
  {
    title: '是否包装单位',
    align: 'center',
    dataIndex: 'isPack_dictText',
    sorter: true,
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
    sorter: true,
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
    sorter: true,
  },
  {
    title: '更新人',
    align: 'center',
    dataIndex: 'updateBy_dictText',
    sorter: true,
  },
  {
    title: '更新日期',
    align: 'center',
    dataIndex: 'updateTime',
    sorter: true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'unitName',
    component: 'JInput', //TODO 范围查询
    needTran: true,
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
  {
    label: '是否包装单位',
    field: 'isPack',
    component: 'JCheckbox',
    componentProps: {
      dictCode: 'yn',
    },
    colProps: {
      span: 8,
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'unitName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
    needTran: true,
  },
  {
    label: '是否包装单位',
    field: 'isPack',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
      type: 'radio',
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表列表数据
export const sysUnitConversionColumns: BasicColumn[] = [
  {
    title: '目标单位',
    align: 'center',
    dataIndex: 'targetUnitId',
  },
  {
    title: '转换系数',
    align: 'center',
    dataIndex: 'factor',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy',
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '更新人',
    align: 'center',
    dataIndex: 'updateBy',
  },
  {
    title: '更新日期',
    align: 'center',
    dataIndex: 'updateTime',
  },
];
//子表表格配置
export const sysUnitConversionJVxeColumns: JVxeColumn[] = [
  {
    title: '目标单位',
    key: 'targetUnitId',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '转换系数',
    key: 'factor',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  unitName: { title: '名称', order: 0, view: 'text', type: 'string' },
  createBy: { title: '创建人', order: 1, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 2, view: 'datetime', type: 'string' },
  updateBy: { title: '更新人', order: 3, view: 'text', type: 'string' },
  updateTime: { title: '更新日期', order: 4, view: 'datetime', type: 'string' },
  //子表高级查询
  sysUnitConversion: {
    title: '单位转换',
    view: 'table',
    fields: {
      targetUnitId: { title: '目标单位', order: 0, view: 'text', type: 'string' },
      factor: { title: '转换系数', order: 1, view: 'text', type: 'string' },
      createBy: { title: '创建人', order: 2, view: 'text', type: 'string' },
      createTime: { title: '创建日期', order: 3, view: 'datetime', type: 'string' },
      updateBy: { title: '更新人', order: 4, view: 'text', type: 'string' },
      updateTime: { title: '更新日期', order: 5, view: 'datetime', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
