/**
 * 最终的文本选择解决方案
 * 通过智能事件处理实现文本选择和下拉功能的完美共存
 */

/**
 * 初始化最终的文本选择解决方案
 */
export function initFinalSelectTextFix() {
  console.log('初始化最终的文本选择解决方案');
  
  // 添加强制样式
  addFinalStyles();
  
  // 添加智能事件处理
  addSmartEventHandling();
  
  // 监听DOM变化
  observeChanges();
}

/**
 * 添加最终样式
 */
function addFinalStyles() {
  const style = document.createElement('style');
  style.id = 'final-select-text-fix';
  style.textContent = `
    /* 强制所有文本可选择 */
    .ant-select-selector,
    .ant-select-selector *,
    .ant-select-selection-item,
    .ant-select-selection-item *,
    .ant-select-selection-item-content,
    .ant-select-selection-item-content * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
    }
    
    /* 当正在选择文本时的样式 */
    .ant-select.text-selecting .ant-select-selector {
      cursor: text !important;
    }
    
    .ant-select.text-selecting .ant-select-selection-item {
      cursor: text !important;
      pointer-events: auto !important;
    }
    
    /* 正常状态下允许点击 */
    .ant-select:not(.text-selecting) .ant-select-selector {
      cursor: pointer;
    }
  `;
  
  document.head.appendChild(style);
}

/**
 * 添加智能事件处理
 */
function addSmartEventHandling() {
  let isSelecting = false;
  let selectStartTime = 0;
  let startX = 0;
  let startY = 0;
  let currentSelectElement: HTMLElement | null = null;
  
  // 全局鼠标按下事件
  document.addEventListener('mousedown', (e) => {
    const target = e.target as HTMLElement;
    const selectElement = getSelectElement(target);
    
    if (selectElement) {
      currentSelectElement = selectElement;
      selectStartTime = Date.now();
      startX = e.clientX;
      startY = e.clientY;
      isSelecting = false;
      
      // 清除之前的选择状态
      document.querySelectorAll('.ant-select.text-selecting').forEach(el => {
        el.classList.remove('text-selecting');
      });
    }
  }, true);
  
  // 全局鼠标移动事件
  document.addEventListener('mousemove', (e) => {
    if (currentSelectElement && selectStartTime > 0) {
      const deltaX = Math.abs(e.clientX - startX);
      const deltaY = Math.abs(e.clientY - startY);
      const deltaTime = Date.now() - selectStartTime;
      
      // 如果鼠标移动距离超过阈值或时间超过阈值，认为是在选择文本
      if (deltaX > 2 || deltaY > 2 || deltaTime > 200) {
        isSelecting = true;
        currentSelectElement.classList.add('text-selecting');
      }
    }
  }, true);
  
  // 全局鼠标释放事件
  document.addEventListener('mouseup', (e) => {
    if (currentSelectElement) {
      const selection = window.getSelection();
      const hasSelection = selection && selection.toString().length > 0;
      
      if (hasSelection) {
        isSelecting = true;
        currentSelectElement.classList.add('text-selecting');
      }
      
      // 延迟重置状态
      setTimeout(() => {
        if (currentSelectElement) {
          currentSelectElement.classList.remove('text-selecting');
        }
        currentSelectElement = null;
        selectStartTime = 0;
        isSelecting = false;
      }, hasSelection ? 500 : 100); // 如果有选中文本，延迟更长时间
    }
  }, true);
  
  // 拦截点击事件
  document.addEventListener('click', (e) => {
    const target = e.target as HTMLElement;
    const selectElement = getSelectElement(target);
    
    if (selectElement && isSelecting) {
      // 如果正在选择文本，阻止下拉框打开
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
  }, true);
  
  // 双击复制功能
  document.addEventListener('dblclick', (e) => {
    const target = e.target as HTMLElement;
    const selectElement = getSelectElement(target);
    
    if (selectElement) {
      e.preventDefault();
      e.stopPropagation();
      
      const text = getTextFromElement(target);
      if (text) {
        copyToClipboard(text);
        showNotification('已复制: ' + text);
      }
    }
  }, true);
  
  // 键盘快捷键
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.key === 'a') {
      const activeElement = document.activeElement as HTMLElement;
      const selectElement = getSelectElement(activeElement);
      
      if (selectElement) {
        e.preventDefault();
        selectAllTextInElement(selectElement);
      }
    }
  });
}

/**
 * 获取选择器元素
 */
function getSelectElement(element: HTMLElement): HTMLElement | null {
  let current = element;
  while (current && current !== document.body) {
    if (current.classList.contains('ant-select-selector') ||
        current.classList.contains('ant-select-selection-item')) {
      return current.closest('.ant-select') as HTMLElement;
    }
    current = current.parentElement as HTMLElement;
  }
  return null;
}

/**
 * 从元素获取文本
 */
function getTextFromElement(element: HTMLElement): string {
  const selectionItem = element.closest('.ant-select-selection-item');
  if (selectionItem) {
    return (selectionItem.textContent || '').trim();
  }
  
  const selector = element.closest('.ant-select-selector');
  if (selector) {
    const item = selector.querySelector('.ant-select-selection-item');
    if (item) {
      return (item.textContent || '').trim();
    }
  }
  
  return (element.textContent || '').trim();
}

/**
 * 全选元素中的文本
 */
function selectAllTextInElement(selectElement: HTMLElement) {
  const selectionItem = selectElement.querySelector('.ant-select-selection-item');
  if (selectionItem) {
    const range = document.createRange();
    const selection = window.getSelection();
    
    if (selection) {
      selection.removeAllRanges();
      range.selectNodeContents(selectionItem);
      selection.addRange(range);
    }
  }
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text: string) {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text);
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
  }
}

/**
 * 显示通知
 */
function showNotification(message: string) {
  const notification = document.createElement('div');
  notification.textContent = message;
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  `;
  
  document.body.appendChild(notification);
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 2000);
}

/**
 * 监听DOM变化
 */
function observeChanges() {
  const observer = new MutationObserver(() => {
    // 为新添加的元素应用样式
    const selects = document.querySelectorAll('.ant-select:not([data-text-fixed])');
    selects.forEach(select => {
      const element = select as HTMLElement;
      element.setAttribute('data-text-fixed', 'true');
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}
