<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <BasicForm @register="registerForm" ref="formRef" name="TrainingForm" />
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <a-tab-pane tab="培训记录" key="trainingRecord" :forceRender="true">
        <JVxeTable
          keep-source
          resizable
          ref="trainingRecord"
          :loading="trainingRecordTable.loading"
          :columns="trainingRecordTable.columns"
          :dataSource="trainingRecordTable.dataSource"
          :height="340"
          :rowNumber="true"
          :rowSelection="true"
          :disabled="formDisabled"
          :toolbar="true"
          :toolbar-config="{ btn: ['remove'] }"
          @selectRowChange="handleSelectRowChange"
        >
          <template #toolbarSuffix>
            <a-button @click="handleOpen">+ 按部门添加</a-button>
            <a-button v-if="selectedRows.length > 0" @click="handleConform">审核确认</a-button>
            <a-button @click="handleActivateDoc">文件生效</a-button>
            <a-button @click="handleRaiseCheckin">发起签到</a-button>
          </template>
        </JVxeTable>
      </a-tab-pane>
    </a-tabs>
    <UserSelectByDepModal rowKey="id" labelKey="username" @register="regModal" @getSelectResult="setValue" v-bind="$attrs"></UserSelectByDepModal>
  </BasicModal>
</template>

<script lang="ts" setup>
  import UserSelectByDepModal from '/@/components/Form/src/jeecg/components/modal/UserSelectByDepModal.vue';
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { useModal } from '/@/components/Modal';
  import { formSchema, trainingRecordColumns } from '../Training.data';
  import { raiseCheackIn, activateDoc, confirmBatch, saveOrUpdate, trainingRecordList } from '../Training.api';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  import { message } from 'ant-design-vue';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const refKeys = ref(['trainingRecord']);
  const activeKey = ref('trainingRecord');
  const trainingRecord = ref();
  const selectedRows = ref([]);
  const tableRefs = { trainingRecord };
  const modalData = ref({});
  //注册model
  const [regModal, { openModal }] = useModal();
  const trainingRecordTable = reactive({
    loading: false,
    dataSource: [],
    columns: trainingRecordColumns,
  });
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    modalData.value = data;
    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      requestSubTableData(trainingRecordList, { id: data?.record?.id }, trainingRecordTable);
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

  async function reset() {
    await resetFields();
    activeKey.value = 'trainingRecord';
    trainingRecordTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);
    return {
      ...main, // 展开
      trainingRecordList: allValues.tablesValue[0].tableData,
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    try {
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  /**
   * 打卡弹出框
   */
  function handleOpen() {
    openModal(true, {
      isUpdate: false,
    });
  }

  /**
   * 设置下拉框的值
   */
  function setValue(options, values) {
    const rows = trainingRecord.value!.getTableData();
    console.log(values);
    values.forEach((value) => {
      //rows中不能存在才添加
      if (rows.findIndex((row) => row.attendee === value) === -1)
        trainingRecord.value!.addRows([{ attendee: value }], {
          setActive: false,
        });
    });
  }

  /**
   * 表格选中行
   */
  function handleSelectRowChange(e) {
    selectedRows.value = e.selectedRows;
  }

  /**
   * 审核确认
   */
  function handleConform() {
    if (selectedRows.value.filter((row) => row.checkinTime == null).length > 0) {
      message.error('部分人员未打卡，请检查后再确认');
      return;
    }

    if (selectedRows.value.length === 0 || selectedRows.value.some((row) => !row.id)) {
      message.error('请选择有效的行进行操作');
      return;
    }
    confirmBatch({ ids: selectedRows.value.map((row) => row.id).join(',') }, handleSuccess);
  }

  /**
   * 文件生效
   */
  function handleActivateDoc() {
    activateDoc({ id: modalData.value.record?.id }, handleSuccess);
  }

  /**
   * 发起签到
   */
  function handleRaiseCheckin() {
    raiseCheackIn({ id: modalData.value.record?.id }, handleSuccess);
  }

  function handleSuccess() {
    requestSubTableData(trainingRecordList, { id: modalData.value.record?.id }, trainingRecordTable);
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
