<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <BasicForm @register="registerForm" name="WarehouseOutApplyForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { message } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../WarehouseOutApply.data';
  import { saveOrUpdate } from '../WarehouseOutApply.api';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const isInitializationPart = ref(false); // 自动带入部分项目，进行新增操作
  const initialAmount = ref<number[]>([]); // 存储初始库存量（数组）

  // 解析逗号分隔的字符串为数字数组
  const parseAmount = (amount: string | number | undefined): number[] => {
    if (typeof amount === 'undefined' || amount === null) return [];
    if (typeof amount === 'number') return [amount];
    return amount
      .split(',')
      .map((num) => parseFloat(num.trim()))
      .filter((num) => !isNaN(num));
  };

  // 表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, scrollToField }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  // 表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    // 重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    isInitializationPart.value = !!data?.isInitializationPart;
    if (unref(isUpdate) || unref(isInitializationPart)) {
      // 表单赋值
      await setFieldsValue({
        ...data.record,
      });
      // 存储初始库存量
      initialAmount.value = parseAmount(data.record?.amount);
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  // 设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
  // 表单提交事件
  async function handleSubmit(v) {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 检查是否包含中文或英文字母
      const invalidCharsRegex = /[\u4e00-\u9fff]|[a-zA-Z]/;
      if (invalidCharsRegex.test(values.amount)) {
        message.error('申请数量不能包含中文或英文字母，请输入有效数字！');
        scrollToField('amount', { behavior: 'smooth', block: 'center' });
        return Promise.reject('Invalid characters in amount');
      }

      // 解析提交的数量
      const submittedAmount = parseAmount(values.amount);
      // 检查是否包含0
      if (submittedAmount.length > 0 && submittedAmount.some((amount) => amount === 0)) {
        message.error('申请数量不能为0，请检查出库数量！');
        scrollToField('amount', { behavior: 'smooth', block: 'center' });
        return Promise.reject('Zero amount detected');
      }
      // 比较提交的数量和初始库存
      if (submittedAmount.length > 0 && initialAmount.value.length > 0) {
        // 确保长度一致，若不一致，说明数据有误
        if (submittedAmount.length !== initialAmount.value.length) {
          message.error('提交的数量与库存数量不匹配，请检查出库数量！');
          scrollToField('amount', { behavior: 'smooth', block: 'center' });
          return Promise.reject('Amount length mismatch');
        }
        // 比较每个数量
        const hasExcessAmount = submittedAmount.some((amount, index) => {
          return amount > (initialAmount.value[index] || 0);
        });
        if (hasExcessAmount) {
          message.error('提交的数量超过库存，请检查出库数量！');
          scrollToField('amount', { behavior: 'smooth', block: 'center' });
          return Promise.reject('Amount exceeds stock');
        }
      }

      // 提交表单
      await saveOrUpdate(values, isUpdate.value);
      // 关闭弹窗
      closeModal();
      // 刷新列表
      emit('success');
    } catch ({ errorFields, message: errorMessage }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      } else if (
        errorMessage === 'Zero amount detected' ||
        errorMessage === 'Amount exceeds stock' ||
        errorMessage === 'Invalid amount' ||
        errorMessage === 'Amount length mismatch' ||
        errorMessage === 'Invalid characters in amount'
      ) {
        return;
      }
      return Promise.reject(errorFields || errorMessage);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
