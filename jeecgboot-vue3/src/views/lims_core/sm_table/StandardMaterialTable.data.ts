import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编号',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'typeId_dictText',
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierId_dictText',
  },
  {
    title: '证书编号',
    align: 'center',
    dataIndex: 'certificateNo',
  },
  {
    title: '批号',
    align: 'center',
    dataIndex: 'lotNo',
  },
  {
    title: '规格数量',
    align:"center",
    dataIndex: 'spec'
  },
  {
    title: '规格单位',
    align:"center",
    dataIndex: 'specUnit_dictText'
  },
  {
    title: '浓或纯度及不确定度',
    align: 'center',
    dataIndex: 'purity',
  },
  {
    title: '购入日期',
    align: 'center',
    dataIndex: 'purchaseDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '价格',
    align: 'center',
    dataIndex: 'price',
  },
  {
    title: '有效期（天）',
    align: 'center',
    dataIndex: 'effectiveLength',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'smStatus_dictText',
  },
  {
    title: '包装数量',
    align: 'center',
    dataIndex: 'qty',
  },
  {
    title: '包装单位',
    align: 'center',
    dataIndex: 'unit_dictText',
  },
  {
    title: '存放地点',
    align: 'center',
    dataIndex: 'location',
  },
  {
    title: '储藏条件',
    align: 'center',
    dataIndex: 'storageCondition',
  },
  {
    title: '仓库存放地点',
    align: 'center',
    dataIndex: 'warehousebox',
  },
  {
    title: '有效日期',
    align: 'center',
    dataIndex: 'vaildDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '销毁原因',
    align: 'center',
    dataIndex: 'destroyReason',
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'comments',
  },
  {
    title: '开启人',
    align: 'center',
    dataIndex: 'openerId_dictText',
  },
  {
    title: '开启时间',
    align: 'center',
    dataIndex: 'openDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '确认效期',
    align: 'center',
    dataIndex: 'confirmValidDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '介质',
    align: 'center',
    dataIndex: 'mediumInfo',
  },
  {
    title: 'cas号',
    align: 'center',
    dataIndex: 'casNo',
  },
  {
    title: '样品',
    align: 'center',
    dataIndex: 'sampleId_dictText',
  },
  {
    title: '客户',
    align: 'center',
    dataIndex: 'customerId_dictText',
  },
  {
    title: '来源或生产商',
    align: 'center',
    dataIndex: 'manufacturer',
  },
  {
    title: '是否退回',
    align: 'center',
    dataIndex: 'isReturn_dictText',
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'code',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '样品',
    field: 'sampleId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'sample,sample_no,id',
    },
    // dynamicDisabled: true,
  },
  {
    label: '客户',
    field: 'customerId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_customer,name,id',
    },
    dynamicDisabled: true,
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
  },
  {
    label: '批号',
    field: 'lotNo',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填!' }];
    },
  },
  {
    label: '规格数量',
    field: 'spec',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入规格数量!' }];
    },
  },
  {
    label: '规格单位',
    field: 'specUnit',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"sys_unit,unit_name,id"
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填!' }];
    },
  },
  {
    label: '来源或生产商',
    field: 'manufacturer',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填!' }];
    },
  },
  {
    label: '浓或纯度及不确定度',
    field: 'purity',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '必填!' }];
    },
  },
  {
    label: '规格',
    field: 'spec',
    component: 'Input',
  },
  {
    label: '有效日期',
    field: 'vaildDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '储藏条件',
    field: 'storageCondition',
    component: 'Input',
  },
  {
    label: '是否退回',
    field: 'isReturn',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'yn',
      type: 'radio',
    },
  },
  {
    label: '类型',
    field: 'typeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_standard_material_type,name,id',
    },
  },
  {
    label: '证书编号',
    field: 'certificateNo',
    component: 'Input',
  },



  {
    label: '购入日期',
    field: 'purchaseDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '价格',
    field: 'price',
    component: 'InputNumber',
  },
  {
    label: '有效期（天）',
    field: 'effectiveLength',
    component: 'InputNumber',
  },
  {
    label: '状态',
    field: 'smStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sm_status',
    },
    defaultValue:'未入库',
    show: false, // 默认不显示状态字段
  },
  {
    label: '包装数量',
    field: 'qty',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入包装数量!' }];
    },
  },
  {
    label: '包装单位',
    field: 'unit',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_unit  where is_pack = 1,unit_name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入包装单位!' }];
    },
  },
  {
    label: '存放地点',
    field: 'location',
    component: 'Input',
  },


  {
    label: '销毁原因',
    field: 'destroyReason',
    component: 'Input',
  },

  {
    label: '开启人',
    field: 'openerId',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '开启时间',
    field: 'openDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '确认效期',
    field: 'confirmValidDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '介质',
    field: 'mediumInfo',
    component: 'Input',
  },
  {
    label: 'cas号',
    field: 'casNo',
    component: 'Input',
  },




  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: { title: '编号', order: 0, view: 'text', type: 'string' },
  name: { title: '名称', order: 1, view: 'text', type: 'string' },
  typeId: { title: '类型', order: 2, view: 'sel_search', type: 'string', dictTable: 'sys_standard_material_type', dictCode: 'id', dictText: 'name' },
  supplierId: { title: '供应商', order: 3, view: 'sel_search', type: 'string', dictTable: 'sys_supplier', dictCode: 'id', dictText: 'name' },
  certificateNo: { title: '证书编号', order: 4, view: 'text', type: 'string' },
  lotNo: { title: '批号', order: 5, view: 'text', type: 'string' },
  spec: {title: '规格数量',order: 6,view: 'number', type: 'number',},
  specUnit: {title: '规格单位',order: 7,view: 'list', type: 'string',dictTable: "sys_unit", dictCode: 'id', dictText: 'unit_name',},
  purity: { title: '浓或纯度及不确定度', order: 7, view: 'text', type: 'string' },
  purchaseDate: { title: '购入日期', order: 8, view: 'date', type: 'string' },
  price: { title: '价格', order: 9, view: 'number', type: 'number' },
  effectiveLength: { title: '有效期（天）', order: 10, view: 'number', type: 'number' },
  smStatus: { title: '状态', order: 12, view: 'list', type: 'string', dictCode: 'sm_status' },
  qty: { title: '数量', order: 13, view: 'number', type: 'number' },
  unit: { title: '单位', order: 14, view: 'list', type: 'string', dictTable: 'sys_unit', dictCode: 'unit_name', dictText: 'id' },
  location: { title: '存放地点', order: 15, view: 'text', type: 'string' },
  storageCondition: { title: '储藏条件', order: 16, view: 'text', type: 'string' },
  vaildDate: { title: '有效日期', order: 17, view: 'date', type: 'string' },
  destroyReason: { title: '销毁原因', order: 18, view: 'text', type: 'string' },
  comments: { title: '备注', order: 19, view: 'text', type: 'string' },
  openerId: { title: '开启人', order: 20, view: 'sel_user', type: 'string' },
  openDate: { title: '开启时间', order: 21, view: 'date', type: 'string' },
  confirmValidDate: { title: '确认效期', order: 22, view: 'date', type: 'string' },
  mediumInfo: { title: '介质', order: 23, view: 'text', type: 'string' },
  casNo: { title: 'cas号', order: 24, view: 'text', type: 'string' },
  sampleId: { title: '样品', order: 25, view: 'list', type: 'string', dictTable: 'sample', dictCode: 'id', dictText: 'name' },
  customerId: { title: '客户', order: 26, view: 'list', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  manufacturer: { title: '来源或生产商', order: 27, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
