<template>
  <div>
    <a-tabs>
      <a-tab-pane key="attach" :tab="(sourceName || '点击行获取') + '-附件'">
        <!--引用表格-->
        <BasicTable @register="registerTable" :rowSelection="rowSelection" ref="tableRef">
          <!--插槽:table标题-->
          <template #tableTitle>
            <a-button :disabled="!canEdit" type="primary" v-auth="'lims_core:attachment:add'" @click="handleAdd" preIcon="ant-design:plus-outlined">
              新增</a-button
            >
          </template>
          <!--操作栏-->
          <template #action="{ record }">
            <TableAction v-if="canEdit" :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
          </template>
          <!--字段回显插槽-->
          <template #bodyCell="{ column, record, index, text }"> </template>
        </BasicTable>
      </a-tab-pane>
    </a-tabs>
    <!-- 表单区域 -->
    <AttachmentModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="tsx" setup>
  import { ref, computed, unref, reactive, defineProps, watch } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, TableAction } from '@/components/Table';
  import AttachmentModal from '@/views/lims_core/attachment/components/AttachmentModal.vue';
  import { useListPage } from '@/hooks/system/useListPage';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from '@/views/lims_core/attachment/Attachment.api';
  import { columns, searchFormSchema, superQuerySchema } from '/@/views/lims_core/attachment/Attachment.data';
  import { useUserStore } from '@/store/modules/user';
  import { custom } from 'vue-types';
  // Emits声明

  /**
   * props声明
   */
  const props = defineProps({
    canEdit: {
      type: Boolean,
      default: false,
    },
    sourceTable: {
      type: [String, Number],
    },
    sourceId: {
      type: [String, Number],
    },
    //parentField="quotation_id" :parentFieldId="model?.quotationId"
    parentField: {
      type: String,
    },
    parentFieldId: {
      type: [String, Number],
    },
    displayText: {
      type: String,
    },
  });

  // 监听变化
  watch(
    () => props.sourceId,
    (val) => {
      queryParam.sourceId = val;
      reload();
    }
  );

  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const tableRef = ref();
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));

  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'AttachTable',
      },
      title: '附件表',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.sourceTable = props.sourceTable;
        queryParam.sourceId = props.sourceId;
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '附件表',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    console.log('props/////',props);
    if (props.sourceName == null) props.sourceName = props.sourceId;
    openModal(true, {
      sourceTable: props.sourceTable,
      sourceId: props.sourceId,
      parentField: props.parentField,
      parentFieldId: props.parentFieldId,
      displayText: props.displayText,
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      sourceTable: props.sourceTable,
      sourceId: props.sourceId,
      parentField: props.parentField,
      parentFieldId: props.parentFieldId,
      displayText: props.displayText,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      sourceTable: props.sourceTable,
      sourceId: props.sourceId,
      parentField: props.parentField,
      parentFieldId: props.parentFieldId,
      displayText: props.displayText,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:attachment:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_core:attachment:delete',
      },
    ];
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
