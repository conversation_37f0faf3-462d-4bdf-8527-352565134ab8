import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { message, Table } from 'ant-design-vue';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '用户名',
    align: 'center',
    dataIndex: 'username',
  },
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'realname',
  },
  {
    title: '部门',
    align: 'center',
    dataIndex: 'orgCodeTxt',

  },
  {
    title: '历史项目',
    align: 'center',
    dataIndex: 'historyItem',
  },

];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '用户名',
    field: 'username',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '姓名',
    field: 'realname',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '涉及部门',
    field: 'departId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_depart where is_lab = 1,depart_name,id',
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
  },
  {
    label: '业务类别',
    field: 'bizTypeId',
    component: 'JCategorySelect',
    componentProps: {
      pcode: 'C09', //TODO back和事件未添加，暂时有问题
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入业务类别!' }];
    },
  },
  {
    label: '标准单价',
    field: 'stdPrice',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入标准单价!' }];
    },
  },
  {
    label: '标准工时',
    field: 'stdTat',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入标准工时!' }];
    },
  },
  {
    label: '产品标准',
    field: 'standardId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_standard,name,id',
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  bizTypeId: { title: '业务类别', order: 1, view: 'text', type: 'string' },
  stdPrice: { title: '标准单价', order: 2, view: 'text', type: 'string' },
  stdTat: { title: '标准工时', order: 3, view: 'text', type: 'string' },
  createBy: { title: '创建人', order: 4, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 5, view: 'datetime', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
