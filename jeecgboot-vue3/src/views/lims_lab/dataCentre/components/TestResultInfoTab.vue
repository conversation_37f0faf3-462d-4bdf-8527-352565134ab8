<template>
  <!--引用表格-->
  <BasicTable
    @register="registerTable"
    @row-click="onRowClick"
    :beforeEditSubmit="beforeEditSubmit"
    ref="tableRef"
    :pagination="false"
    :rowClassName="getRowClassName"
  >
    <template #bodyCell="{ column, record }">
      <span v-if="column.key === 'precisionReq'"> {{ getPrecisionType(record) }} </span>
      <span v-if="column.key === 'roundingPrecision'"> {{ getRoundingRule(record) }} </span>
    </template>
    <!--插槽:table标题-->
    <template #tableTitle>
      <a-button type="primary" @click="saveTestResults">
        <span>保存 </span>
        <icon icon="ant-design:save-twotone" />
      </a-button>
      <a-button type="primary" @click="submitTestResults">
        <span>提交复核 </span>
        <icon icon="ant-design:save-twotone" />
      </a-button>
      <a-button type="primary" @click="checkTestResults" v-show="showFuHe">
        <span>复核 </span>
        <icon icon="ant-design:check-outlined" />
      </a-button>
      <a-button type="primary" @click="unlockTestResults" v-auth="'lims_core:data_centre:unlock'">
        <span>解锁 </span>
        <icon icon="ant-design:unlock-twotone" />
      </a-button>
      <a-button type="primary" @click="previewStd">
        <span>查看标准 </span>
        <icon icon="ant-design:file-pdf-twotone" />
      </a-button>
      <a-button type="primary" @click="previewCharts">
        <span>查看谱图 </span>
        <icon icon="ant-design:fund-outlined" />
      </a-button>
      <a-button type="primary" @click="handleChangeStandard">
        <span>重新指定评价标准 </span>
        <icon icon="ant-design:fund-outlined" />
      </a-button>
      <a-input disabled v-show="hasCalcExpr" v-model:value="calcExpr">
        <template #addonBefore>
          <icon icon="ant-design:function-outlined" />
        </template>
      </a-input>
    </template>

    <template #labelResult="{ record, column, text, index }">
      <template v-if="record.limit && record.limit.includes('标示量')">
        <div v-if="!record.submitted && !record.calcExpr">
          <a-dropdown :trigger="['contextmenu']">
            <a-input
              v-model:value="record.rawResult"
              :id="`input-${record.id}`"
              @press-enter="(e) => handlePressEnter(record, e.target.value)"
              @blur="(e) => onRawResultChange(record, e.target.value)"
              style="text-align: center"
            />
            <template #overlay>
              <a-menu>
                <a-menu-item key="copy-down" @click="copyRawResultDown(record)">
                  <span>向下复制</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
        <div v-else>
          {{ text }}
        </div>
      </template>
    </template>

    <!-- 自定义rawResult列的渲染 -->
    <template #rawResult="{ record, column, text, index }">
      <div v-if="!record.checkedBy && !record.submitted && !record.calcExpr">
        <a-dropdown :trigger="['contextmenu']">
          <a-textarea
            v-if="record.resultType === 1"
            v-model:value="record.rawResult"
            :id="`input-${record.id}`"
            @press-enter="(e) => handlePressEnter(record, e.target.value)"
            @blur="(e) => onRawResultChange(record, e.target.value)"
            @paste="(e) => handlePaste(e, record, index)"
            :auto-size="{ minRows: 1, maxRows: 3 }"
          />
          <a-input
            v-else
            v-model:value="record.rawResult"
            :id="`input-${record.id}`"
            @press-enter="(e) => handlePressEnter(record, e.target.value)"
            @blur="(e) => onRawResultChange(record, e.target.value)"
            @paste="(e) => handlePaste(e, record, index)"
            style="text-align: center"
          />
          <template #overlay>
            <a-menu>
              <a-menu-item key="copy-down" @click="copyRawResultDown(record)">
                <span>向下复制</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div v-else>
        {{ text }}
      </div>
    </template>

    <!-- 自定义conclusion列的渲染 -->
    <template #conclusion="{ record, column, text, index }">
      <div v-if="!record.submitted && !record.calcExpr">
        <a-dropdown :trigger="['contextmenu']">
          <JSearchSelect
            v-model:value="record.conclusion"
            :id="`input-${record.id}`"
            :getPopupContainer="(node) => node.parentNode.parentNode.parentNode.parentNode.parentNode"
            :dropdownMatchSelectWidth="false"
            dict="conclusion_type"
            @change="(e) => onConclusionChange(record, e)"
          />
          <template #overlay>
            <a-menu>
              <a-menu-item key="copy-down" @click="copyRawResultDown(record)">
                <span>向下复制</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div v-else>
        {{ text }}
      </div>
    </template>

    <!-- 自定义reportable列的渲染 -->
    <template #reportable="{ record, column, text, index }">
      <div v-if="!record.submitted && !record.calcExpr">
        <a-dropdown :trigger="['contextmenu']">
          <JSearchSelect
            v-model:value="record.reportable"
            :id="`input-${record.id}`"
            :getPopupContainer="(node) => node.parentNode.parentNode.parentNode.parentNode.parentNode"
            :dropdownMatchSelectWidth="false"
            dict="logic_name"
            @change="(e) => onReportableChange(record, e)"
          />
          <template #overlay>
            <a-menu>
              <a-menu-item key="copy-down" @click="copyRawResultDown(record)">
                <span>向下复制</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      <div v-else>
        {{ text }}
      </div>
    </template>

    <!-- 通用的带锁图标的列渲染函数 -->
    <template #lockIconColumn="{ record, column, text }">
      <div class="flex items-center">
        <span v-if="record.checkedBy" class="mr-1">
          <icon icon="ant-design:lock-outlined" style="color: #1890ff" />
        </span>
        <span>{{ text }}</span>
      </div>
    </template>

    <!-- 自定义limit列的渲染 -->
    <template #limit="{ record, column, text }">
      <div>
        <a-textarea v-if="record.limit" :value="record.limit" :auto-size="{ minRows: 1, maxRows: 3 }" readonly class="borderless-textarea" />
        <span v-else>{{ text }}</span>
      </div>
    </template>
  </BasicTable>
  <DocEditor @register="registerModalDocEditor" />
  <SysMethodAnalyteDrawer @register="registerDrawer" />
  <UserLabSelector @register="reguserModal" @success="setValue" />
  <JPopupOnlReportModal @register="regPopupModal" code="standard_by_methodid" :param="popupParam" valueFiled="id" @ok="callBackStandard" />
</template>

<script lang="ts" setup>
  import { ref, watch, nextTick } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import {
    getTestResults,
    saveTestResult,
    submitTestResult,
    checkTestResult,
    unlockTestResult,
    getOOEditorConfig,
    getConclusion,
    changeStandard,
  } from '../dataCentre.api';
  import { testResultInfoColumns } from '../dataCentre.data';
  import DocEditor from '@/views/dcs/components/DocEditor.vue';
  import { useModal } from '@/components/Modal';
  import { calcVariance, rounding } from '/@/utils/MethodUtils';
  import { getDictItemsByCode } from '@/utils/dict';
  import { useDrawer } from '/@/components/Drawer';
  import SysMethodAnalyteDrawer from '@/views/lims_core/components/SysMethodAnalyteDrawer.vue';
  import { debounce } from 'lodash-es';
  import { Icon } from '@/components/Icon';
  import { create, all } from 'mathjs';
  import UserLabSelector from '@/views/lims_core/user_lab_selector/UserLabSelector.vue';
  import { message, Modal } from 'ant-design-vue';
  import { assign } from '@/views/lims_order/myTask.api';
  import { JSearchSelect } from '@/components/Form';
  import { useUserStore } from '@/store/modules/user';
  import JPopupOnlReportModal from '@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';

  const math = create(all);
  math.config({
    number: 'BigNumber', // Default type of number:
    // 'number' (default), 'BigNumber', or 'Fraction'
    precision: 64, // Number of significant digits for BigNumbers
    relTol: 1e-60,
    absTol: 1e-63,
  });
  const [regPopupModal, { openModal: openPopupModal }] = useModal();
  // 定义Recordable类型
  type Recordable<T = any> = Record<string, T>;
  const [reguserModal, { openModal: openUserSelectModal }] = useModal();
  const props = defineProps({
    curTestIds: { require: true, type: String },
  });
  const emit = defineEmits(['save', 'submit']); // Add emit for save and submit events
  const tableRef = ref(); // 列表页面公共参数、方法
  const afterFetch = (data: Recordable[]) => {
    precisionType.value = getDictItemsByCode('precision_type');
    roundingWay.value = getDictItemsByCode('rounding_way');
    tableData.value = data;
    userStore.getUserInfo.username == data[0].preChecker ? (showFuHe.value = true) : (showFuHe.value = false);
    const sampleNos = new Set(data.map((item) => item.sampleNo));
    const sampleColumn = testResultInfoColumns.find((column) => column.title === '样品编号');
    if (sampleNos.size > 1) {
      if (sampleColumn) {
        sampleColumn.ifShow = true; // 将 ifShow 属性改为 true
      }
    } else {
      if (sampleColumn) {
        sampleColumn.ifShow = false; // 将 ifShow 属性改为 true
      }
    }
    const numericResultColumns = ['平行结果', '报告结果', '精密度类型', '精密度要求', '精密度', '修约算法', '修约方式', '修约规则'];
    let hasNumericResultType = data.some((item) => item.resultType !== 1);
    if (hasNumericResultType) {
      hasNumericResultType = userStore.getUserInfo?.orgCode !== 'A01A01A02A01A07';
    }
    testResultInfoColumns.forEach((column) => {
      if (numericResultColumns.includes(column.title)) {
        column.ifShow = hasNumericResultType;
      }
    });

    // 检查所有行的 resultType 是否都为 1
    const allResultTypeText = data.length > 0 && data.every((item) => item.resultType === 1);

    // 根据 resultType 设置列的显示状态
    const columnsToHideIfTextType = ['rawUnit', 'lod', 'loq', 'repUnit'];
    columnsToHideIfTextType.forEach((dataIndex) => {
      const column = testResultInfoColumns.find((col) => col.dataIndex === dataIndex);
      if (column) {
        column.ifShow = !allResultTypeText;
      }
    });

    // 新增：当resultType==1时，动态设置列宽度
    if (allResultTypeText) {
      // 文本类型结果时的列宽设置
      const columnWidthMap = {
        sampleNo: 120, // 样品编号
        analyte: 100, // 检测指标
        trialNo: 60,
        rawResult: 200, // 原始结果
        limit: 250, // 评价要求
        conclusion: 100, // 结论
        remark: 150, // 备注
        updateBy: 80, // 更新人
        updateTime: 100, // 更新时间
        checker: 80, // 复核人
        checkedBy: 100, // 已复核
        reportable: 100, //是否出在报告上
      };

      // 应用自定义宽度到可见列
      testResultInfoColumns.forEach((column) => {
        if (columnWidthMap[column.dataIndex]) {
          column.width = columnWidthMap[column.dataIndex];
        }
      });
      testResultInfoColumns.forEach((column) => {
        column.width = undefined;
      });
    }
    testResultInfoColumns.forEach((column) => {
      column.filters = column.filters;
    });

    tableRef.value.setColumns(testResultInfoColumns);
    tableRef.value.showActionColumn = false;
    //防止横向滚动条消失
    const totalWidth = testResultInfoColumns
      .filter((column) => column.ifShow !== false) // 只计算显示的列
      .reduce((sum, column) => sum + (column.width || 100), 0); // 默认宽度为100
    tableRef.value.setColumns(testResultInfoColumns);
    tableRef.value.showActionColumn = false;
    // 更新滚动配置
    setProps({
      scroll: { x: totalWidth },
    });
  };

  const { tableContext, createMessage, createConfirm } = useListPage({
    tableProps: {
      api: getTestResults,
      columns: testResultInfoColumns,
      showActionColumn: false,
      size: 'small',
      striped: true,
      rowKey: 'id',
      canResize: true,
      tableSetting: { cacheKey: 'test_result' },
      // 添加行选择配置
      rowSelection: {
        type: 'checkbox',
        columnWidth: 60,
        fixed: true,
      },
      // 请求之前对参数做处理
      beforeFetch(params) {
        params.testIds = props.curTestIds;
      },
      immediate: !!props.curTestIds,
      afterFetch: afterFetch,
    },
  });
  const showFuHe = ref(false);
  const userStore = useUserStore();
  const hasCalcExpr = ref(false);
  const calcExpr = ref('');
  const precisionType = ref([]);
  const roundingWay = ref([]);

  // 注册 ListTable
  const [registerTable, { reload, setProps }] = tableContext;

  //注册抽屉
  const [registerDrawer, { openDrawer }] = useDrawer();

  const recordsToSave = new Set<Recordable>();

  const tableData = ref<Recordable[]>([]);
  const popupParam = ref({});
  watch(
    () => props.curTestIds,
    (newVal) => {
      if (newVal) {
        reload(); // 有ID时加载数据
      } else {
        // 没有ID时清空表格数据
        tableData.value = [];
        recordsToSave.clear(); // 清空待保存的记录
        hasCalcExpr.value = false; // 清空计算表达式标志
        calcExpr.value = ''; // 清空计算表达式

        // 使用setProps方法清空表格数据
        setProps({
          dataSource: [],
        });

        // 如果表格组件有清空数据的方法，也可以调用
        if (tableRef.value) {
          if (typeof tableRef.value.clearData === 'function') {
            tableRef.value.clearData();
          } else if (typeof tableRef.value.setTableData === 'function') {
            tableRef.value.setTableData([]);
          }
        }
      }
    }
  );

  // 获取精密度类型文本
  const getPrecisionType = (record) => {
    if (!record.precisionTypeId || !record.precisionReq) return '';
    // 从字典数组中查找对应的精密度类型文本
    let precisionTypeText = '';
    if (record.precisionTypeId && precisionType.value) {
      // 将字符串转换为数字进行比较
      const typeItem = precisionType.value.find((item) => Number(item.value) === Number(record.precisionTypeId));
      precisionTypeText = typeItem ? typeItem.text : record.precisionTypeId;
    }
    if (precisionTypeText.includes('相对')) {
      return `${precisionTypeText} ${record.precisionReq}%`;
    } else {
      return `${precisionTypeText} ${record.precisionReq}`;
    }
  };

  // 获取修约规则文本
  const getRoundingRule = (record) => {
    if (!record.roundingPrecision) return '';
    // 从字典数组中查找对应的修约方式文本
    let roundingWayText = '';
    if (record.roundingWayId && roundingWay.value) {
      // 将字符串转换为数字进行比较
      const wayItem = roundingWay.value.find((item) => Number(item.value) === Number(record.roundingWayId));
      roundingWayText = wayItem ? wayItem.text : record.roundingWayId;
    }
    return `${record.roundingPrecision} 位 ${roundingWayText}`;
  };

  // 行点击事件
  const onRowClick = (record: Recordable) => {
    if (record.calcExpr) hasCalcExpr.value = true;
    else hasCalcExpr.value = false;
    calcExpr.value = record.calcExpr;
  };

  const saveTestResults = async () => {
    if (recordsToSave && recordsToSave.size > 0) {
      const testResults = Array.from(recordsToSave).map((record: Recordable) => ({
        id: record.id,
        rawResult: record.rawResult,
        dupResult: record.dupResult,
        repResult: record.repResult,
        conclusion: record.conclusion,
        reportable: record.reportable,
      }));
      try {
        const response = await saveTestResult(testResults);
        const testIds = Array.from(recordsToSave).map((record) => record.testId || record.id);
        emit('save', { testIds, statuses: response.statuses || testIds.map(() => '1') });
      } catch (error: any) {
        createMessage.error('保存失败!\n' + error.message);
      } finally {
        recordsToSave.clear();
      }
    }
  };

  const submitTestResults = async () => {
    // 检查复核人是否已填写
    const hasEmptyChecker = tableData.value.some((item) => item.preChecker == null || item.preChecker.trim() === '');
    console.log('hasEmptyChecker', hasEmptyChecker);
    console.log(tableData.value);
    if (hasEmptyChecker) {
      message.error('请先指定复核人!!!');
      console.log(tableData.value);
      openUserSelectModal(true, {
        assignType: 'checker',
        deptId: tableData.value[0].deptId,
        record: tableData.value[0],
        isUpdate: false,
        isChecker: true,
      });
      return;
    }
    // 检查是否有未填写的检测结果
    const hasEmptyResults = tableData.value.some((item) => !item.checkedBy && (!item.rawResult || item.rawResult.trim() === ''));
    if (hasEmptyResults) {
      createMessage.warning('存在未填写的检测结果，请先填写完整！');
      return;
    }

    // 检查是否有未保存的数据
    if (recordsToSave.size > 0) {
      createMessage.info('有未保存的数据，系统将自动保存...');
      try {
        await saveTestResults();
        createMessage.success('数据已自动保存');
      } catch (error) {
        createMessage.error('自动保存失败，请手动保存后再复核！');
        return;
      }
    }

    // 获取排重后的测试ID
    const testIds = getUniqueTestIdsFromTableData();

    if (!testIds) {
      createMessage.warning('没有找到有效的测试ID');
      return;
    }

    console.log('需要复核的测试ID:', testIds);

    // 执行复核操作
    try {
      const response = await submitTestResult({ testIds });
      emit('submit', { testIds: testIds.split(','), statuses: response.statuses || testIds.split(',').map(() => '2') });
      reload();
    } catch (error: any) {
      createMessage.error(`提交失败: ${error.message}`);
    }
  };

  /**
   * 检查是否有未保存的数据
   * @returns 返回一个Promise，resolve为true表示可以继续，false表示取消操作
   */
  const checkUnsavedData = async (): Promise<boolean> => {
    if (recordsToSave.size > 0) {
      return new Promise((resolve) => {
        createConfirm({
          iconType: 'warning',
          title: '未保存数据提示',
          content: '当前有未保存的数据，是否保存后继续？',
          onOk: async () => {
            try {
              await saveTestResults();
              resolve(true);
            } catch (error: any) {
              createMessage.error('保存失败，请手动保存后再继续！');
              resolve(false);
            }
          },
          onCancel: () => {
            resolve(true); // 用户选择不保存，但仍继续操作
          },
        });
      });
    }
    return Promise.resolve(true); // 没有未保存的数据，可以继续
  };

  // 暴露方法给父组件
  defineExpose({
    checkUnsavedData,
  });

  /**
   * 从表格数据中提取并排重testId
   * @returns 排重后的testId字符串，如果没有有效的testId则返回空字符串
   */
  const getUniqueTestIdsFromTableData = (): string => {
    // 检查数据有效性
    if (!tableData.value || tableData.value.length === 0) {
      return '';
    }

    // 从表格数据中提取所有的testId
    const testIdSet = new Set<string>();
    tableData.value.forEach((item) => {
      if (item.testId) {
        testIdSet.add(item.testId);
      }
    });

    // 如果没有找到testId，尝试使用id
    if (testIdSet.size === 0) {
      tableData.value.forEach((item) => {
        if (item.id) {
          testIdSet.add(item.id);
        }
      });
    }

    // 将Set转换为逗号分隔的字符串
    return Array.from(testIdSet).join(',');
  };

  const previewAnalyte = async () => {
    // 获取选中的行
    let selectedRows = [];
    if (tableRef.value) {
      if (typeof tableRef.value.getSelectRows === 'function') {
        selectedRows = tableRef.value.getSelectRows() || [];
      } else if (typeof tableRef.value.getRowSelection === 'function') {
        const selection = tableRef.value.getRowSelection();
        if (selection && selection.selectedRowKeys) {
          selectedRows = tableData.value.filter((item) => selection.selectedRowKeys.includes(item.id));
        }
      }
    }
    if (!selectedRows.length) {
      createMessage.warning('请先选择一行数据');
      return;
    }
    // 创建新对象
    const selectedRow = selectedRows[0];
    const record = {
      ...selectedRow,
      id: selectedRow.methodId,
      name: selectedRow.methodName,
    };
    // 打开抽屉
    try {
      openDrawer(true, {
        record,
        isUpdate: true,
      });
      // 清除表格选中状态
      if (tableRef.value) {
        if (typeof tableRef.value.clearSelectedRowKeys === 'function') {
          tableRef.value.clearSelectedRowKeys(); // 假设表格组件有此方法
        } else if (typeof tableRef.value.setRowSelection === 'function') {
          tableRef.value.setRowSelection({ selectedRowKeys: [] }); // 重置选中行
        }
      }
    } catch (error) {
      createMessage.error(`打开检测指标抽屉失败: ${error.message}`);
    }
  };

  const checkTestResults = async () => {
    // 检查是否有未填写的检测结果
    const hasEmptyResults = tableData.value.some((item) => !item.checkedBy && (!item.rawResult || item.rawResult.trim() === ''));
    if (hasEmptyResults) {
      createMessage.warning('存在未填写的检测结果，请先填写完整！');
      return;
    }

    // 检查是否有未保存的数据
    if (recordsToSave.size > 0) {
      createMessage.info('有未保存的数据，系统将自动保存...');
      try {
        await saveTestResults();
        createMessage.success('数据已自动保存');
      } catch (error) {
        createMessage.error('自动保存失败，请手动保存后再复核！');
        return;
      }
    }

    // 获取排重后的测试ID
    const testIds = getUniqueTestIdsFromTableData();

    if (!testIds) {
      createMessage.warning('没有找到有效的测试ID');
      return;
    }

    console.log('需要复核的测试ID:', testIds);

    // 执行复核操作
    try {
      await checkTestResult({ testIds });
      // 刷新数据
      reload();
    } catch (error: any) {
      createMessage.error(`复核失败: ${error.message}`);
    }
  };

  const unlockTestResults = async () => {
    // 获取排重后的测试ID
    const testIds = getUniqueTestIdsFromTableData();

    if (!testIds) {
      createMessage.warning('没有找到有效的测试ID');
      return;
    }

    console.log('需要解锁的测试ID:', testIds);

    // 执行解锁操作
    try {
      await unlockTestResult({ testIds });
      // 刷新数据
      reload();
    } catch (error: any) {
      createMessage.error(`解锁失败: ${error.message}`);
    }
  };

  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();

  const previewStd = async () => {
    // 获取排重后的测试ID
    const testIds = getUniqueTestIdsFromTableData();

    if (!testIds) {
      createMessage.warning('没有找到有效的测试ID');
      return;
    }

    // 当有多个ID时，只取第一个
    const testIdsArray = testIds.split(',');
    if (testIdsArray.length > 0) {
      const testId = testIdsArray[0];
      try {
        const ooConfig = await getOOEditorConfig(testId, 'sys_standard_test');
        if (ooConfig.document.url) {
          window.open(
            ooConfig.document.url,
            null,
            'height=600, width=1024, top=0, left=0, toolbar=no, menubar=no, scrollbars=no, resizable=no,location=no, status=no'
          );
        }
      } catch (error: any) {
        createMessage.error(`查看标准失败: ${error.message}`);
      }
    }
  };

  const previewCharts = async () => {
    // 获取排重后的测试ID
    const testIds = getUniqueTestIdsFromTableData();

    if (!testIds) {
      createMessage.warning('没有找到有效的测试ID');
      return;
    }

    // 当有多个ID时，只取第一个
    const testIdsArray = testIds.split(',');
    if (testIdsArray.length > 0) {
      const testId = testIdsArray[0];
      try {
        const ooConfig = await getOOEditorConfig(testId, 'test_chart');
        openDocEditorModal(true, ooConfig);
      } catch (error: any) {
        createMessage.error(`查看谱图失败: ${error.message}`);
      }
    }
  };

  const beforeEditSubmit = async ({ record, index, key, value }: { record: Recordable; index: number; key: string; value: any }) => {
    if (record.checkedBy) {
      createMessage.error('已复核的数据不能修改!');
      return Promise.reject('已复核的数据不能修改');
    }
    return Promise.resolve();
  };

  // 计算平行样品的平均值
  const calculateDupResult = (sameAnalyteRecords): number => {
    // 计算平均值
    const sum = sameAnalyteRecords.reduce((total, item) => {
      // 确保rawResult是数字
      const value = Number(item.rawResult) || 0;
      return total + value;
    }, 0);
    // 返回平均值，保留与原始值相同的小数位数
    const avg = sum / sameAnalyteRecords.length;
    sameAnalyteRecords.forEach((record) => {
      record.dupResult = avg;
    });
  };

  // 计算精密度并判断是否符合标准要求
  const checkPrecision = (record: Recordable): { isPrecisionOk: boolean; precision?: number; precisionReq?: number } => {
    if (record.precisionTypeId && record.precisionReq) {
      // 查找相同样品编号和检测指标的记录
      const sameAnalyteRecords = tableData.value.filter(
        (item) => item.sampleNo === record.sampleNo && item.analyte === record.analyte && item.rawResult
      );
      // 如果有多个记录，计算精密度
      if (sameAnalyteRecords.length > 1) {
        const rawResults = sameAnalyteRecords.map((item) => Number(item.rawResult) || 0);
        const precision = calcVariance(rawResults, Number(record.precisionTypeId));
        // 将精密度保存到所有相同样品和检测指标的记录中
        if (precision !== -1) {
          record.resultPrecision = precision;
          const precisionReq = Number(record.precisionReq);
          if (!isNaN(precisionReq) && record.resultPrecision > precisionReq) {
            return { isPrecisionOk: false, precision, precisionReq }; // 精密度不符合要求
          }
        }
      }
    }
    return { isPrecisionOk: true }; // 精密度符合要求或无需检查
  };

  function calcNumericConcusion(record: Recordable<any>) {
    if (record.limit && record.resultType === 2 && record.repResult) {
      if (record.repResult === '未检出' || record.repResult === 'Not Detected') {
        if (record.limit.startsWith('≤') || record.limit.startsWith('<')) {
          record.conclusion = '符合规定';
        }
      } else if (record.repResult.startsWith('<') || (record.repResult.startsWith('＜') && record.repResult.substring(1) === record.loq)) {
        record.conclusion = '符合规定';
      } else {
        // 替换特殊比较符号为JavaScript可识别的符号
        let limitExpr = record.limit
          .replace('小于', '<')
          .replace(/≤/g, '<=')
          .replace(/≥/g, '>=')
          .replace('不得过', '<=')
          .replace('不得超过', '<=')
          .replace('不得大于', '<=')
          .replace('不得高于', '<=')
          .replace('不少于', '>=')
          .replace('不得少于', '>=')
          .replace('不得低于', '>=')
          .replace('不低于', '>=');
        // 声明表达式变量
        let expr = '';
        // 处理波浪符号(~)，如"3~5"转换为"3<=x && x<=5"
        if (limitExpr.includes('~')) {
          const parts = limitExpr.split('~');
          if (parts.length === 2) {
            // 正确格式：左边界 <= 值 && 值 <= 右边界
            expr = parts[0].trim() + '<=' + record.repResult + ' && ' + record.repResult + '<=' + parts[1].trim();
          } else {
            // 如果格式不正确，使用原始处理逻辑
            expr = record.repResult + limitExpr;
          }
        } else if (limitExpr.includes('～')) {
          const parts = limitExpr.split('～');
          if (parts.length === 2) {
            // 正确格式：左边界 <= 值 && 值 <= 右边界
            expr = parts[0].trim() + '<=' + record.repResult + ' && ' + record.repResult + '<=' + parts[1].trim();
          } else {
            // 如果格式不正确，使用原始处理逻辑
            expr = record.repResult + limitExpr;
          }
        } else if (limitExpr.includes('-')) {
          const parts = limitExpr.split('-');
          if (parts.length === 2) {
            // 正确格式：左边界 <= 值 && 值 <= 右边界
            expr = parts[0].trim() + '<=' + record.repResult + ' && ' + record.repResult + '<=' + parts[1].trim();
          } else {
            // 如果格式不正确，使用原始处理逻辑
            expr = record.repResult + limitExpr;
          }
        } else if (limitExpr.includes('至')) {
          const parts = limitExpr.split('至');
          if (parts.length === 2) {
            // 正确格式：左边界 <= 值 && 值 <= 右边界
            expr = parts[0].trim() + '<=' + record.repResult + ' && ' + record.repResult + '<=' + parts[1].trim();
          } else {
            // 如果格式不正确，使用原始处理逻辑
            expr = record.repResult + limitExpr;
          }
        } else {
          // 原来的处理逻辑
          expr = record.repResult + limitExpr;
        }
        console.log('expr', expr);
        // 使用 eval 计算关系表达式的结果
        let concResult = false;
        try {
          concResult = eval(expr);
          // 根据表达式结果设置结论
          record.conclusion = concResult ? '符合规定' : '不符合规定';
        } catch (error) {
          console.error('表达式计算错误:', expr, error); // 表达式计算出错时，不修改结论
        }
      }
    }
  }

  const onRawResultChange = (record: Recordable, value: string) => {
    // 先验证输入
    validateInput(record, value);
    if (record.resultType == 1) {
      record.dupResult = record.rawResult;
      record.repResult = record.rawResult;
      if (record.repResult && record.limit) {
        debouncedGetConclusion(record);
      }
    } else {
      // 查找相同样品编号和检测指标的记录
      const sameAnalyteRecords = tableData.value.filter(
        (item) => item.sampleNo === record.sampleNo && item.analyte === record.analyte && item.rawResult
      );
      if (sameAnalyteRecords.length >= 2) {
        calculateDupResult(sameAnalyteRecords);
        // 计算精密度并判断是否符合标准要求
        const checkResult = checkPrecision(record);
        if (!checkResult.isPrecisionOk && checkResult.precision !== undefined && checkResult.precisionReq !== undefined) {
          // 精密度不符合要求，显示警告
          createMessage.warning(`精密度 ${checkResult.precision}% 超出要求 ${checkResult.precisionReq}%`);
        } else {
          // 计算报告结果
          let repResult;
          if (record.ucf && record.ucf !== 1) {
            repResult = record.dupResult * record.ucf;
          } else {
            repResult = record.dupResult;
          }
          // 应用修约算法
          if (record.roundingMethodId && record.decimalPlaces) {
            // 将结果转为字符串
            const repResultStr = repResult.toString();
            // 应用修约方法
            const roundedResult = rounding(
              repResultStr,
              Number(record.decimalPlaces),
              Number(record.roundingMethodId),
              false, // 是否使用科学计数法
              record.significantFigures ? Number(record.significantFigures) : -1 // 有效数字位数
            );
            sameAnalyteRecords.forEach((record) => {
              record.repResult = roundedResult;
            });
          } else {
            sameAnalyteRecords.forEach((record) => {
              record.repResult = repResult;
            });
          }
        }
      } else {
        record.dupResult = record.rawResult;
        record.repResult = record.rawResult;
      }
      //处理计算公式
      doCalcExpr(record);
      calcNumericConcusion(record);
    }
    recordsToSave.add(record);
  };

  const onConclusionChange = (record: Recordable, value: string) => {
    recordsToSave.add(record);
  };
  const onReportableChange = (record: Recordable, value: string) => {
    recordsToSave.add(record);
  };

  function doCalcExpr(record: Recordable) {
    const sameSampleRecords = tableData.value.filter(
      (item) =>
        item.sampleNo === record.sampleNo && item.analyte !== record.analyte && item.calcExpr && item.calcExpr.includes('{' + record.analyte + '}')
    );
    // 遍历包含当前分析物占位符的记录，尝试计算公式
    if (sameSampleRecords.length > 0) {
      console.log('找到包含当前分析物的计算公式记录:', sameSampleRecords);
      // 处理每一个包含计算公式的记录
      sameSampleRecords.forEach((calcRecord) => {
        if (!calcRecord.calcExpr) return;
        let formula = calcRecord.calcExpr;
        let allPlaceholdersReplaced = true;
        // 查找公式中的所有占位符 {analyte}
        const placeholderRegex = /\{([^}]+)\}/g;
        let match: RegExpExecArray | null;
        let placeholders: string[] = [];
        // 提取所有占位符
        while ((match = placeholderRegex.exec(formula)) !== null) {
          if (match[1]) {
            placeholders.push(match[1]);
          }
        }
        console.log('公式中的占位符:', placeholders);
        // 尝试替换所有占位符
        for (const placeholder of placeholders) {
          // 在tableData中查找对应的分析物记录
          const analyteRecord = tableData.value.find(
            (item) => item.sampleNo === calcRecord.sampleNo && item.trialNo === calcRecord.trialNo && item.analyte === placeholder && item.rawResult
          );
          if (analyteRecord && analyteRecord.rawResult) {
            // 确保结果是数字
            const numericResult = Number(analyteRecord.rawResult);
            if (!isNaN(numericResult)) {
              // 替换占位符为实际值
              formula = formula.replace(new RegExp(`\\{${placeholder}\\}`, 'g'), analyteRecord.rawResult);
            } else {
              allPlaceholdersReplaced = false;
              console.log(`分析物 ${placeholder} 的结果不是有效数字:`, analyteRecord.rawResult);
              break;
            }
          } else {
            allPlaceholdersReplaced = false;
            console.log(`未找到分析物 ${placeholder} 的记录或结果为空`);
            break;
          }
        }
        // 如果所有占位符都已替换，执行计算
        if (allPlaceholdersReplaced) {
          try {
            console.log('执行计算公式:', formula);
            const mathResult = math.evaluate(formula);

            // 将BigNumber对象转换为字符串，然后再转换为普通数字
            let finalResult: number | string | boolean;
            if (typeof mathResult === 'object' && mathResult !== null) {
              // 检查是否是BigNumber对象
              if (mathResult.toString && typeof mathResult.toString === 'function') {
                // 使用toString方法将BigNumber转换为字符串
                const resultStr = mathResult.toString();
                // 将字符串转换为普通数字
                finalResult = Number(resultStr);
                console.log('BigNumber转换为数字:', finalResult);
              } else {
                // 如果不是BigNumber对象，尝试直接转换
                finalResult = Number(mathResult);
              }
            } else {
              // 如果已经是原始类型，直接使用
              finalResult = mathResult;
            }

            console.log('计算结果:', finalResult, '(原始类型:', typeof mathResult, ')');

            // 更新计算记录的原始结果
            calcRecord.rawResult = finalResult;
            // 同时更新其他结果字段
            calcRecord.dupResult = finalResult;
            calcRecord.repResult = finalResult;
            // 将更新的记录添加到待保存集合
            recordsToSave.add(calcRecord);
            console.log(`已更新分析物 ${calcRecord.analyte} 的计算结果:`, finalResult);
          } catch (error) {
            console.error('计算公式执行错误:', error);
          }
        }
      });
    }
  }

  const debouncedGetConclusion = debounce((record: Recordable) => {
    getConclusion(record).then((res) => {
      if (res == '符合规定') {
        record.conclusion = '符合规定';
      } else if (res == '不符合规定') {
        record.conclusion = '不符合规定';
      }
    });
  }, 500);
  const handlePressEnter = (record: Recordable, value: string) => {
    // 先保存当前值
    onRawResultChange(record, value);

    // 找到当前记录在数据中的索引
    const currentIndex = tableData.value.findIndex((item) => item.id === record.id);
    if (currentIndex !== -1) {
      // 从当前索引开始，查找下一个未复核的记录
      for (let i = currentIndex + 1; i < tableData.value.length; i++) {
        const nextRecord = tableData.value[i];
        if (nextRecord && !nextRecord.checkedBy) {
          // 使用nextTick确保DOM已更新
          nextTick(() => {
            // 找到下一个输入框并聚焦
            const nextInputContainer = document.getElementById(`input-${nextRecord.id}`);
            if (nextInputContainer) {
              // 在Ant Design Vue中，a-input组件内部有一个input元素
              const inputElement = nextInputContainer.querySelector('input');
              if (inputElement) {
                inputElement.focus();
                inputElement.select();
              } else {
                // 如果找不到input元素，尝试直接聚焦容器
                nextInputContainer.focus();
              }
            }
          });
          break;
        }
      }
    }
  };

  /**
   * 向下复制rawResult值
   * 如果表格有多行选中，则将第一行的值复制到所有选中行
   */
  const copyRawResultDown = (record: Recordable) => {
    // 获取当前表格选中的行
    let selectedRows: Recordable[] = [];

    // 获取选中行的不同尝试方法
    if (tableRef.value) {
      if (typeof tableRef.value.getSelectRows === 'function') {
        selectedRows = tableRef.value.getSelectRows() || [];
      } else if (typeof tableRef.value.getRowSelection === 'function') {
        const selection = tableRef.value.getRowSelection();
        if (selection && selection.selectedRowKeys) {
          selectedRows = tableData.value.filter((item) => selection.selectedRowKeys.includes(item.id));
        }
      }
    }

    console.log('选中行:', selectedRows);

    if (!selectedRows.length || selectedRows.length <= 1) {
      // 如果没有选中多行，则尝试找到当前行之后的所有行
      const currentIndex = tableData.value.findIndex((item) => item.id === record.id);
      if (currentIndex !== -1 && currentIndex < tableData.value.length - 1) {
        // 获取当前行之后的所有未复核行
        const rowsToUpdate = tableData.value.slice(currentIndex + 1).filter((item) => !item.checkedBy);

        if (rowsToUpdate.length > 0) {
          // 确认是否要复制
          createConfirm({
            iconType: 'warning',
            title: '确认操作',
            content: `是否将当前值复制到后续的 ${rowsToUpdate.length} 行？`,
            onOk: () => {
              // 将当前行的值复制到后续行
              rowsToUpdate.forEach((item) => {
                item.rawResult = record.rawResult;
                item.dupResult = record.rawResult;
                item.repResult = record.rawResult;
                recordsToSave.add(item);
              });
              createMessage.success(`已将值复制到 ${rowsToUpdate.length} 行`);
            },
          });
        } else {
          createMessage.warning('没有可复制的行');
        }
      }
    } else {
      // 如果选中了多行，则将第一行的值复制到其他选中行
      const firstRow = selectedRows[0];
      const rowsToUpdate = selectedRows.slice(1).filter((item) => !item.checkedBy);

      if (rowsToUpdate.length > 0) {
        createConfirm({
          iconType: 'warning',
          title: '确认操作',
          content: `是否将第一行的值复制到其他 ${rowsToUpdate.length} 个选中行？`,
          onOk: () => {
            // 将第一行的值复制到其他选中行
            rowsToUpdate.forEach((item) => {
              item.rawResult = firstRow.rawResult;
              item.dupResult = firstRow.rawResult;
              item.repResult = firstRow.rawResult;
              recordsToSave.add(item);
            });
            createMessage.success(`已将值复制到 ${rowsToUpdate.length} 行`);
          },
        });
      } else {
        createMessage.warning('没有可复制的行或选中的行已复核');
      }
    }
  };

  // 验证输入值是否符合类型要求
  const validateInput = (record: Recordable, value: string) => {
    // 如果 resultType 为 2，则只允许输入数字
    if (record.resultType === 2) {
      // 允许输入数字、小数点、负号
      const numericRegex = /^-?\d*\.?\d*$/;
      if (value && !numericRegex.test(value)) {
        // 如果输入不是数字，则恢复为上一个有效值或清空
        const regex = /[<＜]\d/;
        let isND: boolean = value === '未检出' || value === 'Not Detected' || regex.test(value);
        if (!isND) {
          createMessage.warning('此字段只能输入数字');
          // 移除非数字字符
          record.rawResult = value.replace(/[^\d.-]/g, '');
        }
        // 处理多个小数点或负号的情况
        if (record.rawResult) {
          // 确保只有一个小数点
          const parts = record.rawResult.split('.');
          if (parts.length > 2) {
            record.rawResult = parts[0] + '.' + parts.slice(1).join('');
          }
          // 确保负号只在开头
          if (record.rawResult.indexOf('-') > 0) {
            record.rawResult = record.rawResult.replace(/-/g, '');
            if (record.rawResult.startsWith('.')) {
              record.rawResult = '0' + record.rawResult;
            }
          } else if (record.rawResult.startsWith('-') && record.rawResult.indexOf('-', 1) > 0) {
            record.rawResult = '-' + record.rawResult.substring(1).replace(/-/g, '');
          }
        }
      }
    }
  };
  // 根据结论设置行样式
  const getRowClassName = (record: Recordable) => {
    // 检查结论是否不合格
    if (record.conclusion && record.conclusion !== '符合规定') {
      return 'text-red';
    }

    if (record.resultType == 2) {
      // 检查精密度是否符合要求
      const precisionResult = checkPrecision(record);
      if (!precisionResult.isPrecisionOk) {
        return 'text-red';
      }
    }

    return '';
  };

  /**
   * 设置下拉框的值
   */
  function setValue(options) {
    console.log('options', options);

    assign({ id: tableData.value[0].taskId, userName: options[2].id, type: 'checker' }).then(() => {
      reload();
    });
  }

  function handleChangeStandard() {
    popupParam.value.method_id = tableData.value[0].methodId;
    if (tableData.value[0].checkedBy && tableData.value[0].checkedBy.trim() !== '') {
      createMessage.warning('已复核的数据不能修改标准!');
      return;
    }
    openPopupModal(true);
  }
  function callBackStandard(rows) {
    console.log(rows);
    changeStandard({ taskId: tableData.value[0].taskId, standardId: rows[0].id })
      .then(async () => {
        createMessage.success('标准修改成功!');
        await reload();
        onRawResultChange(tableData.value[0], tableData.value[0].rawResult); // 重新计算结果
      })
      .catch((error) => {
        createMessage.error('标准修改失败: ' + error.message);
      });
  }

  /**
   * 处理粘贴事件
   */
  const handlePaste = (event: ClipboardEvent, record: Recordable, index: number) => {
    event.preventDefault();
    const clipboardData = event.clipboardData;
    if (!clipboardData) return;

    const pastedText = clipboardData.getData('text');
    if (!pastedText) return;

    // 检查是否是多行数据（Excel列数据）
    const lines = pastedText.split(/\r?\n/).filter((line) => line.trim() !== '');

    if (lines.length > 1) {
      // 多行数据，应用到表格
      applyPastedColumnData(lines, index);
    } else {
      // 单行数据，直接设置到当前输入框
      record.rawResult = lines[0];
      onRawResultChange(record, lines[0]);
    }
  };

  /**
   * 应用粘贴的列数据到表格
   */
  const applyPastedColumnData = (lines: string[], startIndex: number) => {
    const availableRows = tableData.value.slice(startIndex).filter((row) => !row.submitted && !row.calcExpr && !row.checkedBy);

    if (availableRows.length === 0) {
      createMessage.warning('没有可编辑的行');
      return;
    }

    const dataToApply = lines.slice(0, availableRows.length);
    const skippedCount = lines.length - dataToApply.length;

    // 确认操作
    const confirmMessage = `将要填充 ${dataToApply.length} 行数据${skippedCount > 0 ? `，跳过 ${skippedCount} 行（已复核或锁定）` : ''}，是否继续？`;

    createConfirm({
      iconType: 'info',
      title: '确认粘贴',
      content: confirmMessage,
      onOk: () => {
        let successCount = 0;

        dataToApply.forEach((value, i) => {
          const targetRow = availableRows[i];
          if (targetRow) {
            targetRow.rawResult = value.trim();
            onRawResultChange(targetRow, value.trim());
            successCount++;
          }
        });

        createMessage.success(`成功填充 ${successCount} 行数据`);
      },
    });
  };
</script>

<style lang="less" scoped>
  .flex {
    display: flex;
  }

  .items-center {
    align-items: center;
  }

  .mr-1 {
    margin-right: 4px;
  }

  :deep(.text-red) {
    color: red;
  }

  :deep(.borderless-textarea) {
    border: none;
    resize: none;
    background: transparent;

    .ant-input {
      border: none;
      box-shadow: none;
      padding: 0;
      background-color: transparent;
    }
  }

  .borderless-textarea :deep(.ant-input:hover),
  .borderless-textarea :deep(.ant-input:focus) {
    border: none;
    box-shadow: none;
    outline: none;
  }
</style>
