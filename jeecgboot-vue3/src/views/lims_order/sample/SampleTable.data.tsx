import { BasicColumn } from '/src/components/Table';
import { FormSchema } from '/src/components/Table';
import { rules } from '/src/utils/helper/validator';
import { render } from '/src/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/src/utils';
import { list as listProduct } from '../../lims_core/SysProduct.api';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编号',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'sampleNo',
    fixed: 'left',
    width: 100,
  },
  {
    title: '测试状态',
    align: 'center',
    resizable: true,
    sorter: true,
    width: 80,
    dataIndex: 'testControlStatus_dictText',
    customRender: ({ text }) => {
      const color = {
        正常: 'success',
        取消: 'error',
        部分取消: 'warning',
        补样中: 'orange',
      };

      return (
        <a-tag color={color[text]} style={{ fontSize: '12px' }}>
          {text}
        </a-tag>
      );
    },
  },
  {
    title: '名称',
    align: 'left',
    resizable: true,
    sorter: true,
    dataIndex: 'name',
    fixed: 'left',
  },
  {
    title: '批号',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'lotNo',
  },
  {
    title: '研发项目编码',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'rdId_dictText',
  },
  {
    title: '研发项目名称',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'rdName',
  },
  {
    title: '检品状态',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'sampleFlowStatus_dictText',
    width: 60,
  },
  {
    title: '方法',
    align: 'left',
    resizable: true,
    sorter: true,
    dataIndex: 'method',
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'specification',
    resizable: true,
    sorter: true,
    width: 100,
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
    resizable: true,
    sorter: true,
    width: 150,
  },
  {
    title: '项目内容',
    align: 'center',
    dataIndex: 'testContent',
    resizable: true,
    sorter: true,
    width: 150,
  },
  {
    title: '业务类别',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'bizTypeId_dictText',
  },
  {
    title: '标准价格',
    align: 'center',
    resizable: true,
    sorter: true,
    fixed: 'right',
    dataIndex: 'standardPrice',
    width: 80,
  },
  {
    title: 'PM价格',
    align: 'center',
    resizable: true,
    sorter: true,
    fixed: 'right',
    dataIndex: 'pmPrice',
    edit: true,
    width: 110,
  },
  {
    title: '申请单价',
    align: 'center',
    resizable: true,
    sorter: true,
    fixed: 'right',
    dataIndex: 'applyPrice',
    edit: true,
    width: 110,
  },
  {
    title: '标准工期',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'standardLeadTime',
    width: 110,
  },
  {
    title: 'PM工期 ',
    align: 'center',
    resizable: true,
    sorter: true,
    dataIndex: 'pmLeadTime',
    edit: true,
    width: 110,
  },
  {
    title: '折扣',
    align: 'center',
    dataIndex: 'discount',
    width: 80,
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '产品名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入产品名称!' }];
    },
  },
  {
    label: '业务类别',
    field: 'bizTypeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'biz_type,name,id',
      getPopupContainer: () => document.body,
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入业务类别!' }];
    },
  },
  {
    label: '是否生成研发编码',
    field: 'isGenerateRdNo',
    component: 'RadioGroup',
    componentProps: ({ formModel, formActionType }) => {
      return {
        options: [
          { label: '是', value: 1, key: '1' },
          { label: '否', value: 0, key: '0' },
        ],
        onChange: (e) => {
          // 如果选择否，则清空研发项目名称
          if (e.target.value === 0) {
            formModel.rdName = '';
            formActionType.updateSchema({
              field: 'rdName',
              show: false,
            });
          } else {
            formActionType.updateSchema({
              field: 'rdName',
              show: true,
            });
          }
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入是否生成研发编码!' }];
    },
  },
  {
    label: '研发项目名称',
    field: 'rdName',
    component: 'Input',
    show: false,
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'productIds_dictText',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'initial_productIds',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'quotationId',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    component: 'Input',
    field: 'customerId',
    show: false,
  },
  {
    label: '',
    component: 'Input',
    field: 'customerContactId',
    show: false,
  },
  {
    label: '商机',
    field: 'opportunityId',
    component: 'JDictSelectTag',
    show: false,
  },
];

export const searchFormSchema: FormSchema[] = [];

// 高级查询数据
export const superQuerySchema = {
  quotationId: { title: '报价单ID', order: 0, view: 'text', type: 'string' },
  sampleNo: { title: '编号', order: 1, view: 'text', type: 'string' },
  name: { title: '产品名称', order: 2, view: 'text', type: 'string' },
  lotNo: { title: '批号', order: 3, view: 'text', type: 'string' },
  spec: { title: '规格', order: 4, view: 'text', type: 'string' },
  manufacturerId: { title: '生产厂家', order: 5, view: 'sel_search', type: 'string', dictCode: '' },
  standardPrice: { title: '标准价格', order: 6, view: 'number', type: 'number' },
  pmPrice: { title: 'PM价格', order: 7, view: 'number', type: 'number' },
  applyPrice: { title: '申请单价', order: 8, view: 'number', type: 'number' },
  standardLeadTime: { title: '标准工期', order: 9, view: 'number', type: 'number' },
  pmLeadTime: { title: 'PM工期 ', order: 10, view: 'number', type: 'number' },
  discount: { title: '折扣', order: 11, view: 'number', type: 'number' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
