import { FormSchema } from '/@/components/Form';

// 邮件表单字段定义
export const mailFormSchema: FormSchema[] = [
  {
    field: 'to',
    label: '收件人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入收件人邮箱，多个邮箱用分号(;)分隔',
    },
  },
  {
    field: 'cc',
    label: '抄送',
    component: 'Input',
    componentProps: {
      placeholder: '请输入抄送邮箱，多个邮箱用分号(;)分隔',
    },
  },
  {
    field: 'subject',
    label: '主题',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入邮件主题',
    },
  },
  {
    field: 'attachments',
    label: '附件',
    component: 'Input',
    slot: 'attachmentSlot',
  },
  {
    field: 'body',
    label: '正文',
    component: 'JEditor',
    componentProps: {
      placeholder: '请输入邮件正文',
      height: 300,
      menubar: 'false',
    },
  },
];

// 邮件表单数据接口
export interface MailFormData {
  to: string;
  cc?: string;
  bcc?: string;
  subject: string;
  body?: string;
  attachments?: string[] | string; // 文档URL作为附件，可以是字符串或字符串数组
}
