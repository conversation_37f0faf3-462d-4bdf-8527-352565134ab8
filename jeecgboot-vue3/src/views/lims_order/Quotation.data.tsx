import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { initDictOptions } from '@/utils/dict';
import { useRouter } from 'vue-router';

import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { useUserStore } from '@/store/modules/user';
//列表数据
const userStore = useUserStore();
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '状态',
    align: 'center',
    dataIndex: 'statusId_dictText',
    sorter: true,
    resizable: true,
    width: 120,
  },
  {
    title: '项目名称',
    align: 'center',
    dataIndex: 'name',
    sorter: true,
    resizable: true,
    width: 120,
  },
  {
    title: '客户',
    align: 'center',
    dataIndex: 'customerId_dictText',
    sorter: true,
    resizable: true,
    width: 120,
  },
  {
    title: '客户联系人',
    align: 'center',
    dataIndex: 'customerContactId_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '业务员',
    align: 'center',
    dataIndex: 'responsiblePerson_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '业务类别',
    align: 'center',
    dataIndex: 'bizTypeId_dictText',
    sorter: true,
    resizable: true,
    width: 120,
  },
  {
    title: '申请用途',
    align: 'center',
    dataIndex: 'applyForId_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '是否需要方案',
    align: 'center',
    dataIndex: 'needSolution_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '方案状态',
    align: 'center',
    dataIndex: 'solutionStatusId_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: 'PM价',
    align: 'center',
    dataIndex: 'pmPrice',
    resizable: true,
    width: 120,
  },
  {
    title: '销售申请价',
    align: 'center',
    dataIndex: 'applyPrice',
    resizable: true,
    width: 120,
  },
  {
    title: '审批折扣',
    align: 'center',
    dataIndex: 'discount',
    resizable: true,
    width: 120,
  },
  {
    title: '工期',
    align: 'center',
    dataIndex: 'leadTime',
    resizable: true,
    width: 120,
  },
  {
    title: '方案反馈时间要求',
    align: 'center',
    dataIndex: 'solutionReplyDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
    resizable: true,
    width: 120,
  },
  {
    title: '加急类别',
    align: 'center',
    dataIndex: 'serviceTypeId_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '是否发补项目',
    align: 'center',
    dataIndex: 'isSupplementary_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '邮寄单号',
    align: 'center',
    dataIndex: 'expressNo',
    resizable: true,
    width: 120,
  },
  {
    title: '信息调研表',
    align: 'center',
    dataIndex: 'researchAttachs',
    resizable: true,
    width: 120,
  },
  {
    title: '初稿方案',
    align: 'center',
    dataIndex: 'draftSolutionAttach',
    resizable: true,
    width: 120,
  },
  {
    title: '已签批方案',
    align: 'center',
    dataIndex: 'approvedSolutionAttach',
    resizable: true,
    width: 120,
  },
  {
    title: '币种',
    align: 'center',
    dataIndex: 'currencyId_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
    resizable: true,
    width: 120,
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
    resizable: true,
    width: 120,
  },
  {
    title: '剂型',
    align: 'center',
    dataIndex: 'dosageForm',
    resizable: true,
    width: 120,
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'specification',
    resizable: true,
    width: 120,
  },
  {
    title: '是否需要立项报告',
    align: 'center',
    dataIndex: 'isProjectInitiationReport_dictText',
    resizable: true,
    width: 120,
  },
  {
    title: '是否需要报价文件',
    align: 'center',
    dataIndex: 'isQuotationDocument_dictText',
    resizable: true,
    width: 120,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '业务员',
    field: 'responsiblePerson',
    component: 'JSelectUser',
    componentProps: ({ model }) => ({
      disabled: userStore.getUserInfo.roles.length === 2 &&
        userStore.getUserInfo.roles.includes('saler') &&
        userStore.getUserInfo.roles.includes('default_permisson'),
    }),
    defaultValue: userStore.getUserInfo.roles.length === 2 &&
    userStore.getUserInfo.roles.includes('saler') &&
    userStore.getUserInfo.roles.includes('default_permisson')
      ? userStore.getUserInfo.realname
      : undefined,
    colProps: { span: 4 },
  },
  {
    label: '报价单状态',
    field: 'statusId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'quotation_status',
    },
    defaultValue: '0',
    colProps: { span: 4 },
  },
  {
    label: '入库状态',
    field: 'quotationsamplestatus',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'quotation_sample_status',
    },
    colProps: { span: 4 },
  },
  {
    label: '报价单编号',
    field: 'quotationNo',
    component: 'JInput', //TODO 范围查询
    colProps: { span: 4 },
  },
  {
    label: '项目名称',
    field: 'name',
    component: 'JInput',
    colProps: { span: 4 },
  },
  {
    label: '业务类别',
    field: 'bizTypeId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'biz_type,name,id',
    },
    colProps: { span: 4 },
  },
  {
    label: '合同编号',
    field: 'contractno',
    component: 'JInput',
    colProps: { span: 4 },
  },
  {
    label: '客户',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_customer,name,id',
    },
    colProps: { span: 4 },
  },
  {
    label: '加急类别',
    field: 'serviceTypeId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'service_type',
    },
    colProps: { span: 4 },
  },
  {
    label: '创建人',
    field: 'createBy',
    component: 'Input',
    colProps: { span: 4 },
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    colProps: { span: 4 },
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '项目名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入项目名称!' }];
    },
  },
  {
    label: '客户',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: ({ formModel, formActionType }) => {
      return {
        dict: 'sys_customer,name,id',
        onChange: async (val: any) => {
          const { updateSchema } = formActionType;
          const dictData = await initDictOptions(`sys_customer_contact where customer_id = '${val}',name,id`);
          formModel.customerContactId = undefined;
          console.log(dictData);
          updateSchema([
            {
              field: 'customerContactId',
              componentProps: {
                options: dictData,
              },
            },
          ]);
        },
      };
    },
  },
  {
    label: '客户联系人',
    field: 'customerContactId',
    component: 'JSearchSelect',
    componentProps: {
      replaceFields: {
        title: 'name',
        key: 'id',
        value: 'id',
      },
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户联系人!' }];
    },
  },
  {
    label: '业务类别',
    field: 'bizTypeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'biz_type,name,id',
      getPopupContainer: () => document.body,
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入业务类别!' }];
    },
  },
  {
    label: '剂型',
    field: 'dosageForm',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      if (model.bizTypeId === '2') {
        return [{ required: true, message: '请输入剂型!' }];
      }
      return [];
    },
  },
  {
    label: '规格',
    field: 'specification',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      if (model.bizTypeId === '2') {
        return [{ required: true, message: '请输入规格!' }];
      }
      return [];
    },
  },
  {
    label: '是否需要立项报告',
    field: 'isProjectInitiationReport',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'logic_code',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      if (model.bizTypeId === '2') {
        return [{ required: true, message: '请选择是否需要立项报告!' }];
      }
      return [];
    },
  },
  {
    label: '是否需要报价文件',
    field: 'isQuotationDocument',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'logic_code',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      if (model.bizTypeId === '2') {
        return [{ required: true, message: '请选择是否需要报价文件!' }];
      }
      return [];
    },
  },
  {
    label: '申请用途',
    field: 'applyForId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'apply_for',
    },
  },
  {
    label: '是否需要方案',
    field: 'needSolution',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'logic_code',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入是否需要方案!' }];
    },
  },
  {
    label: '方案状态',
    field: 'solutionStatusId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'solution_status',
    },
  },
  {
    label: 'PM价',
    field: 'pmPrice',
    component: 'InputNumber',
    dynamicDisabled: true,
    componentProps: ({ formModel }) => {
      return {
        step: 0.01,
        onChange: (val) => {
          formModel.discount = (formModel.applyPrice / val).toFixed(2);
        },
      };
    },
  },
  {
    label: '销售申请价',
    field: 'applyPrice',
    component: 'InputNumber',
    dynamicDisabled: true,
    componentProps: ({ formModel }) => {
      return {
        step: 0.01,
        onChange: (val) => {
          formModel.discount = (val / formModel.pmPrice).toFixed(2);
        },
      };
    },
  },
  {
    label: '审批折扣',
    field: 'discount',
    dynamicDisabled: true,
    component: 'InputNumber',
  },
  {
    label: '工期',
    field: 'leadTime',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入工期!' }];
    },
    slot: 'leadTime',
  },
  {
    label: '工期类型',
    field: 'dayType',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入工期类别!' }];
    },
    show: false,
  },
  {
    label: '方案反馈时间要求',
    field: 'solutionReplyDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '加急类别',
    field: 'serviceTypeId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'service_type',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入加急类别!' }];
    },
  },
  {
    label: '是否发补项目',
    field: 'isSupplementary',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'logic_code',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入是否发补项目!' }];
    },
  },
  {
    label: '邮寄单号',
    field: 'expressNo',
    component: 'Input',
  },
  {
    label: '信息调研表',
    field: 'researchAttachs',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '初稿方案',
    field: 'draftSolutionAttach',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '已签批方案',
    field: 'approvedSolutionAttach',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '币种',
    field: 'currencyId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'currency',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入币种!' }];
    },
  },
  {
    label: '业务员',
    field: 'responsiblePerson',
    component: 'JSelectUser',
    componentProps: {},
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入业务员!' }];
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'quotationNo',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'isLocked',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'sample_sn',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'statusId',
    component: 'Input',
    show: false,
  },
  {
    label: '商机',
    field: 'opportunityId',
    component: 'JDictSelectTag',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  quotationNo: { title: '报价单编号', order: 0, view: 'text', type: 'string' },
  name: { title: '项目名称', order: 1, view: 'text', type: 'string' },
  customerId: { title: '客户', order: 2, view: 'sel_search', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  customerContactId: {
    title: '客户联系人',
    order: 3,
    view: 'sel_search',
    type: 'string',
    dictTable: 'sys_customer_contact',
    dictCode: 'id',
    dictText: 'name',
  },
  bizTypeId: { title: '业务分类', order: 4, view: 'list', type: 'string', dictCode: 'biz_type,name,id', pcode: 'C09' },
  applyForId: { title: '申请用途', order: 5, view: 'list', type: 'string', dictCode: 'apply_for' },
  needSolution: { title: '是否需要方案', order: 6, view: 'number', type: 'number', dictCode: 'logic_code' },
  solutionStatusId: { title: '方案状态', order: 7, view: 'list', type: 'string', dictCode: 'solution_status' },
  pmPrice: { title: 'PM价', order: 8, view: 'number', type: 'number' },
  applyPrice: { title: '销售申请价', order: 9, view: 'number', type: 'number' },
  discount: { title: '审批折扣', order: 10, view: 'number', type: 'number' },
  solutionReplyDate: { title: '方案反馈时间要求', order: 11, view: 'date', type: 'string' },
  serviceTypeId: { title: '加急类别', order: 12, view: 'radio', type: 'string', dictCode: 'service_type' },
  isSupplementary: { title: '是否发补项目', order: 13, view: 'number', type: 'number', dictCode: 'logic_code' },
  expressNo: { title: '邮寄单号', order: 14, view: 'text', type: 'string' },
  researchAttachs: { title: '信息调研表', order: 15, view: 'file', type: 'string' },
  draftSolutionAttach: { title: '初稿方案', order: 16, view: 'file', type: 'string' },
  approvedSolutionAttach: { title: '已签批方案', order: 17, view: 'file', type: 'string' },
  currencyId: { title: '币种', order: 18, view: 'radio', type: 'string', dictCode: 'currency' },
  remark: { title: '备注', order: 19, view: 'textarea', type: 'string' },
  createBy: { title: '创建人', order: 20, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 21, view: 'datetime', type: 'string' },
  dosageForm: { title: '剂型', order: 26, view: 'text', type: 'string' },
  specification: { title: '规格', order: 27, view: 'text', type: 'string' },
  isProjectInitiationReport: { title: '是否需要立项报告', order: 28, view: 'number', type: 'number', dictCode: 'logic_code' },
  isQuotationDocument: { title: '是否需要报价文件', order: 29, view: 'number', type: 'number', dictCode: 'logic_code' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
