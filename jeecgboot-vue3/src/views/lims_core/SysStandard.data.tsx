import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
import component from '/@/locales/lang/en/component';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'left',
    resizable: true,
    width: 250,
    resizable: true,
    dataIndex: 'name',
    fixed: 'left',
  },
  {
    title: '版本',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'version',
    fixed: 'left',
  },
  {
    title: '描述',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'description',
  },
  {
    title: '等级类别',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'gradeTypeId',
    customRender: ({ text }) => {
      return render.renderCategoryTree(text, 'C05');
    },
  },
  {
    title: '客户',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'customerId_dictText',
  },
  {
    title: '内容性质类别',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'contentTypeId_dictText',
  },
  {
    title: '备案日期',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'registeredDate',
  },
  {
    title: '发布日期',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'publishDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '生效日期',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'effectiveDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '作废日期',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'abandonedDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '状态',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'effectiveStatus_dictText',
  },
  {
    title: '文件地址',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'url',
  },
  {
    title: '受控号',
    align: 'center',
    resizable: true,
    width: 120,
    dataIndex: 'dcsNo',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'JInput',
    //colProps: {span: 6},
  },
  {
    label: '描述',
    field: 'description',
    component: 'JInput',
    //colProps: {span: 6},
  },
  {
    label: '等级类别',
    field: 'gradeTypeId',
    component: 'JCategorySelect',
    componentProps: {
      pcode: 'C05', //back和事件未添加，暂时有问题
    },
    //colProps: {span: 6},
  },
  {
    label: '内容性质类别',
    field: 'contentTypeId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'standard_content_type',
    },
    //colProps: {span: 6},
  },
  {
    label: '备案日期',
    field: 'registeredDate',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '发布日期',
    field: 'publishDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
  {
    label: '生效日期',
    field: 'effectiveDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
  {
    label: '作废日期',
    field: 'abandonedDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
  {
    label: '状态',
    field: 'effectiveStatus',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'standard_status',
    },
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
    needTran: true,
  },
  {
    label: '版本',
    field: 'version',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入版本!' }];
    },
  },
  {
    label: '描述',
    field: 'description',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入描述!' }];
    },
  },
  {
    label: '等级类别',
    field: 'gradeTypeId',
    component: 'JCategorySelect',
    componentProps: {
      pcode: 'C05', //TODO back和事件未添加，暂时有问题
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入等级类别!' }];
    },
  },
  {
    label: '客户',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_customer,name,id',
    },
  },
  {
    label: '内容性质类别',
    field: 'contentTypeId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'standard_content_type',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入内容性质类别!' }];
    },
  },
  {
    label: '备案日期',
    field: 'registeredDate',
    component: 'Input',
  },
  {
    label: '发布日期',
    field: 'publishDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '生效日期',
    field: 'effectiveDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '作废日期',
    field: 'abandonedDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '状态',
    field: 'effectiveStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'standard_status',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入状态!' }];
    },
  },
  {
    label: '文件地址',
    field: 'url',
    component: 'JUpload',
    componentProps: {
      maxCount: 1,
    },
  },
  {
    label: '受控号',
    field: 'dcsNo',
    component: 'Input',
  },
  {
    label: '文件地址',
    field: 'url',
    component: 'Input', //TODO 范围查询
    //colProps: {span: 6},
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表表格配置
export const sysStandardEvaluationLimtColumns: JVxeColumn[] = [
  {
    title: '顺序',
    key: 'sortNum',
    type: JVxeTypes.inputNumber,
    width: '80px',
    disabled: true,
    placeholder: '请输入${title}',
    defaultValue: '',
    fixed: 'left',
  },
  {
    title: '指标',
    key: 'analyteId',
    type: JVxeTypes.select,
    options: [],
    allowSearch: true,
    dictCode: 'sys_analyte,name,id',
    width: '120px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
    treeNode: true,
    linkageKey: 'methodIds',
    fixed: 'left',

    needTran: true,
    sourceTable: 'sys_analyte',
    sourceField: 'name',
  },
  {
    title: '报告名称',
    key: 'reportName',
    type: JVxeTypes.input,
    width: '120px',
    placeholder: '请输入${title}',
    defaultValue: '',

    needTran: true,
  },
  {
    title: '方法',
    key: 'methodIds',
    type: JVxeTypes.select,
    props: {
      mode: 'multiple',
    },
    options: [],
    dictCode: 'sys_method,name,id',
    width: '250px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '决定因素类型',
    key: 'paraType',
    type: JVxeTypes.selectSearch,
    dictCode: 'lab_dict',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '决定因素',
    key: 'paraValue',
    type: JVxeTypes.selectSearch,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '评定要求',
    key: 'elimit',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],

    needTran: true,
  },
  {
    title: '计量单位',
    key: 'unitId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_unit,unit_name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '第一组别',
    key: 'groupOne',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '第二组别',
    key: 'groupTwo',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '父级ID',
    key: 'parentId',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    visible: false,
  },
  {
    title: '操作',
    key: 'action',
    width: '120px',
    fixed: 'right',
    slots: { default: 'action' },
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  version: { title: '版本', order: 1, view: 'text', type: 'string' },
  description: { title: '描述', order: 2, view: 'text', type: 'string' },
  gradeTypeId: { title: '等级类别', order: 3, view: 'cat_tree', type: 'string', pcode: 'C05' },
  customerId: { title: '客户', order: 4, view: 'sel_search', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  contentTypeId: { title: '内容性质类别', order: 5, view: 'list', type: 'string', dictCode: 'standard_content_type' },
  registeredDate: { title: '备案日期', order: 6, view: 'text', type: 'string' },
  publishDate: { title: '发布日期', order: 7, view: 'date', type: 'string' },
  effectiveDate: { title: '生效日期', order: 8, view: 'date', type: 'string' },
  abandonedDate: { title: '作废日期', order: 9, view: 'date', type: 'string' },
  effectiveStatus: { title: '状态', order: 10, view: 'list', type: 'string', dictCode: 'standard_status' },
  url: { title: '文件地址', order: 11, view: 'file', type: 'string' },
  dcsNo: { title: '受控号', order: 12, view: 'text', type: 'string' },
  //子表高级查询
  sysStandardEvaluationLimt: {
    title: '标准指标评定要求',
    view: 'table',
    fields: {
      sortNum: { title: '序号', order: 0, view: 'text', type: 'string' },
      analyteId: { title: '指标', order: 1, view: 'list', type: 'string', dictTable: 'sys_analyte', dictCode: 'id', dictText: 'name' },
      reportName: { title: '指标报告名称', order: 2, view: 'text', type: 'string' },
      methodIds: { title: '方法', order: 3, view: 'list_multi', type: 'string', dictTable: 'sys_method', dictCode: 'id', dictText: 'name' },
      paraType: { title: '决定因素类型', order: 4, view: 'sel_search', type: 'string', dictCode: 'lab_dict' },
      paraValue: { title: '决定因素', order: 5, view: 'text', type: 'string' },
      elimit: { title: '评定要求', order: 6, view: 'text', type: 'string' },
      unitId: { title: '计量单位', order: 7, view: 'sel_search', type: 'string', dictTable: 'sys_unit', dictCode: 'id', dictText: 'unit_name' },
      parentId: { title: '父级ID', order: 8, view: 'text', type: 'string' },
      groupOne: { title: '第一组别', order: 9, view: 'text', type: 'string' },
      groupTwo: { title: '第二组别', order: 10, view: 'text', type: 'string' },
    },
  },
};
