import { defHttp } from '/src/utils/http/axios';
import { useMessage } from '/src/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_order/testTask/list',
  listVo = '/lims_order/testTask/listVo',
  save = '/lims_order/testTask/add',
  edit = '/lims_order/testTask/edit',
  deleteOne = '/lims_order/testTask/delete',
  deleteBatch = '/lims_order/testTask/deleteBatch',
  importExcel = '/lims_order/testTask/importExcel',
  exportXls = '/lims_order/testTask/exportXls',
  assign = '/lims_order/testTask/assign',
  myTasklist = '/lims_order/testTask/querymyTasklistByMainId',
  consumptiveOrstandardmateriallist = '/lims_order/testTask/consumptiveOrstandardmateriallist',
  existForeignGoods = '/lims_core/consumptive/existForeignGoods',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 子表单查询接口
 * @param params
 */
export const querymyTasklist = Api.myTasklist;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 分配
 * @param params
 */
export const assign = (params) => {
  return defHttp.get({ url: Api.assign, params });
};

/**
 * 列表接口
 * @param params
 */
export const listVo = (params) => defHttp.get({ url: Api.listVo, params });

/**
 * 子表列表接口
 * @param params
 */
export const sysTaskList = (params) => defHttp.get({url: Api.myTasklist, params},{isTransformResponse:false});

/** 根据名称查询耗材或者标品的编号，
 *
 * @param params
 */
export const consumptiveOrstandardmateriallist = (params) => {
  return defHttp.get({ url: Api.consumptiveOrstandardmateriallist, params });
};
/**
 * 提示
 * @param params
 */
export const existForeignGoods = (params) => {
  return defHttp.post({ url: Api.existForeignGoods, params });
}
