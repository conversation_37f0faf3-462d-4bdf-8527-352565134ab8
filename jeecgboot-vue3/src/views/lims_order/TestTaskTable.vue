<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" ref="tableRef">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button :disabled="disabled" type="primary" preIcon="ant-design:printer-outlined" @click="handleAddItem">添加</a-button>
        <a-button :disabled="disabled" type="primary" preIcon="ant-design:printer-outlined" @click="subtractorBatch">选择分包方</a-button>
        <a-button :disabled="disabled" type="primary" preIcon="ant-design:printer-outlined" @click="handleChangeDept">指派实验组</a-button>
        <a-button v-if="isRevert" type="primary" preIcon="ant-design:printer-outlined" @click="handleRevert">流程回退</a-button>
        <a-button
          :disabled="disabled"
          v-if="selectedRowKeys.length > 0"
          type="primary"
          preIcon="ant-design:delete-outlined"
          @click="batchHandleDelete"
          >批量删除</a-button
        >
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction v-if="!disabled" :actions="getTableAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <TestTaskModal @register="registerModal" @success="handleSuccess" />
    <UserLabSelector @register="regModal" @success="setValue" />
    <ProductSelector @register="regProductSelector" @success="handleProductSelectorSuccess" />
    <JPopupOnlReportModal @register="regPopupModal" :param="popupParam" code="sys_method_by_cid" @ok="callBack" />
    <JPopupOnlReportModal @register="regCapabilityModal" :param="capaParam" code="sys_capability_selector" :multi="true" @ok="callBackCapability" />
    <a-modal v-model:open="open" title="请选择添加方式" width="300px">
      <a-card hoverable :style="{ textAlign: 'center' }" @click="handleAddByProduct">按营销产品添加</a-card>
      <a-card hoverable :style="{ marginTop: '10px', textAlign: 'center' }" @click="() => handleAddByCapability('Y')">标检-按检测能力添加</a-card>
      <a-card hoverable :style="{ marginTop: '10px', textAlign: 'center' }" @click="() => handleAddByCapability('N')">非标检-按检测能力添加</a-card>
    </a-modal>
    <a-modal v-model:open="openDepat" title="请选择部门" width="300px" @ok="handleOkChangeDept">
      <span>请选择部门</span>
      <JSearchSelect
        style="width: 100%; margin-top: 10px; margin-bottom: 10px"
        v-model:value="selectedDeptId"
        dict="sys_depart where is_lab = 1,depart_name,id"
        :showSearch="true"
        :multiple="false"
      />
    </a-modal>
    <a-modal v-model:open="openFlow" title="流程退回" width="300px" @ok="handleOkRevert">
      <a-form layout="vertical" style="width: 100%">
        <a-form-item label="退回节点" required style="padding-top: 10px; padding-left: 20px; padding-right: 20px">
          <JSearchSelect v-model:value="selectedStep" dict="flow_step" defaultValue="1924764827675287553" :showSearch="true" :multiple="false" />
        </a-form-item>
        <a-form-item label="退回原因" required style="padding-left: 20px; padding-right: 20px">
          <a-input placeholder="退回原因" v-model:value="selectedDeptReson" />
        </a-form-item>
      </a-form>
    </a-modal>
    <JPopupOnlReportModal
      showAdvancedButton="true"
      @register="subtractorModal"
      code="test_task_subtractor"
      valueFiled="id"
      :multi="true"
      :groupId="''"
      @ok="refcallBack"
    />
  </div>
</template>

<script lang="tsx" name="lims_order-testTask" setup>
  import { hiprint } from 'vue-plugin-hiprint';
  import { ref, reactive, defineProps, watch, isReactive } from 'vue';
  import { BasicTable, TableAction } from '/src/components/Table';
  import { useModal } from '/src/components/Modal';
  import { useListPage } from '/src/hooks/system/useListPage';
  import TestTaskModal from './components/TestTaskModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './TestTask.data.tsx';
  import { listVo, deleteOne, getImportUrl, getExportUrl, assign, changeMethod, changesubtractor, saveOrUpdate, revert } from './TestTask.api';
  import { list as listSample } from './sample/Sample.api';
  import { useUserStore } from '/src/store/modules/user';
  import { message, Modal } from 'ant-design-vue';
  import barcode from '@/templates/barcode.js';
  import UserLabSelector from '../lims_core/user_lab_selector/UserLabSelector.vue';
  import ProductSelector from '@/views/lims_order/product_selector/index.vue';
  import JPopupOnlReportModal from '@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';
  import { addProduct, addCapability } from '@/views/lims_order/sample/SampleTable.api';
  import { JSearchSelect } from '@/components/Form';

  /**
   * props声明
   */
  const props = defineProps({
    sampleId: {
      type: [String, Array],
    },
    // disabled: {
    //   type: Boolean,
    //   default: false,
    // },
    sampleNo: {
      type: String,
      default: '',
    },
    isRevert: {
      type: Boolean,
      default: false,
    },
  });

  const open = ref<boolean>(false);

  const showModal = () => {
    open.value = true;
  };
  const hideModal = () => {
    open.value = false;
  };
  const selectedDeptId = ref('');

  function handleAddItem(record: Recordable) {
    console.log(props.sampleNo.indexOf('YPT'));
    if (props.sampleId instanceof Array && props.sampleId.length > 1) {
      Modal.confirm({
        title: '提示',
        content: '当前选择了多个样品,增加项目会批量增加到这些样品中,是否继续?',
        onOk() {
          showModal();
        },
      });
    } else {
      showModal();
    }
  }
  function subtractorBatch() {
    if (selectedRows.value.length == 0) {
      message.error('请选择要修改分包方的数据');
      return;
    }
    openSelectModal(true);
  }
  function refcallBack(data) {
    if (selectedRows.value.length == 0) {
      message.error('请选择要修改分包方的数据');
      return;
    }
    currentRow.value = selectedRows.value;
    console.log(currentRow.value);
    console.log(data);

    currentRow.value.forEach((item) => {
      let record = {
        id: item.id,
        subtractorId: data[0].id,
      };
      setLoading(true);
      changesubtractor(record)
        .then((res) => {
          handleSuccess();
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }
  function handleAddByProduct() {
    console.log('按营销产品添加');
    hideModal();
    openProductSelector(true);
  }

  function handleAddByCapability(isBiao) {
    console.log('按检测能力添加', isBiao);
    hideModal();
    capaParam.value.is_biao = isBiao;
    openCapabilitySelector(true);
  }

  const handleProductSelectorSuccess = (data) => {
    console.log('handleProductSelectorSuccess', data);
    setLoading(true);
    if (props.sampleId instanceof Array) {
      props.sampleId.forEach((item, index) => {
        console.log('11111', item);
        addProduct(data, item)
          .then((res) => {
            handleSuccess();
          })
          .finally(() => {
            setLoading(false);
          });
      });
    } else {
      console.log('22222', props.sampleId);
      addProduct(data, props.sampleId)
        .then((res) => {
          handleSuccess();
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };
  const callBackCapability = (data) => {
    console.log('callBackCapability', data);
    data = data.map((item) => {
      return item.id;
    });
    console.log(data);
    setLoading(true);
    if (props.sampleId instanceof Array) {
      props.sampleId.forEach((item, index) => {
        console.log('11111', item);
        addCapability(data, item)
          .then((res) => {
            handleSuccess();
          })
          .finally(() => {
            setLoading(false);
          });
      });
    } else {
      addCapability(data, props.sampleId)
        .then((res) => {
          handleSuccess();
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const disabled = (!userStore.getUserInfo.roles.includes('register') && !userStore.getUserInfo.roles.includes('PM')) || props.isRevert;
  const [regModal, { openModal: openUserSelectModal }] = useModal();
  const currentRow = ref([]);
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [regProductSelector, { openModal: openProductSelector }] = useModal();
  const [regCapabilityModal, { openModal: openCapabilitySelector }] = useModal();
  const [regPopupModal, { openModal: openPopupModal }] = useModal();
  const popupParam = ref({});
  const capaParam = ref({});
  const currentEditRow = ref({});
  //注册表格
  const tableRef = ref();
  const [subtractorModal, { openModal: openSelectModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting: {
        cacheKey: 'test_task_table',
      },
      title: '测试任务',
      api: listVo,
      size: 'small',
      columns,
      canResize: false,
      formConfig: {},
      clickToRowSelect: true,
      actionColumn: {
        width: 180,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.sampleId = props.sampleId;
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '测试任务',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload, setLoading }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  async function batchHandleDelete() {
    selectedRowKeys.value.forEach((item, index) => {
      deleteOne({ id: item }, handleSuccess)
        .then((res) => {
          handleSuccess();
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 指定方法
   */

  function handleChangeMethod(record: Recordable) {
    console.log('12123123213123', record);
    popupParam.value.cid = record.capabilityId;
    currentEditRow.value = record;
    openPopupModal(true);
  }
  const callBack = (rows) => {
    console.log('callBack', rows);
    console.log('currentEditRow', currentEditRow.value);
    let record = {
      id: currentEditRow.value.id,
      methodId: rows[0].id,
    };
    setLoading(true);
    changeMethod(record)
      .then((res) => {
        handleSuccess();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '改方法',
        onClick: handleChangeMethod.bind(null, record),
        auth: 'lims_order:test_task:edit',
      },
      {
        label: '指派',
        onClick: handleOpen.bind(null, record),
        auth: 'lims_order:test_task:assign',
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除吗?',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_order:test_task:delete',
      },
    ];
  }

  function handleOpen(record) {
    currentRow.value = [record];
    openUserSelectModal(true, {
      deptId: currentRow.value[0].deptId,
      isUpdate: false,
    });
  }

  /**
   * 设置下拉框的值
   */
  function setValue(options) {
    console.log('options', options);
    console.log('currentRow.value', currentRow.value);

    // currentRow.value.forEach((item) => {
    //   if (item.assignee != null) {
    //     Modal.confirm({
    //       title: '提示',
    //       content: '当前任务已指派给[' + item.assignee + ']，是否重新指派？',
    //       onOk() {
    //         assign({ id: item.id, userName: options[0].username }).then(() => {
    //           handleSuccess();
    //         });
    //       },
    //     });
    //     console.log('currentRow', item);
    //   } else {
    //     console.log('currentRow', item);
    //     assign({ id: item.id, userName: options[0].username }).then(() => {
    //       handleSuccess();
    //     });
    //   }
    // });
  }

  function assignBatch() {
    if (selectedRows.value.length == 0) {
      message.error('请选择要指派的数据');
      return;
    }
    currentRow.value = selectedRows.value;
    console.log(currentRow.value);
    openUserSelectModal(true, {
      deptId: currentRow.value[0].deptId,
      isUpdate: false,
    });
  }
  /**
   * 打印
   */
  function print() {
    if (selectedRows.value.length == 0) {
      message.error('请选择要打印的数据');
      return;
    }
    let hiprintTemplate = new hiprint.PrintTemplate({ template: barcode });
    let printDatas = [];
    selectedRows.value.forEach((item) => {
      console.log(item);

      let printData = { barcode: item.id, title: item.methodId_dictText };
      printDatas.push(printData);
    });
    // 打印
    hiprintTemplate.print(printDatas);
  }

  function handleSampleNoClick(record) {
    listSample({ id: record.sampleId }).then((res) => {
      console.log('res', res);
      if (res.records) {
        openSampleModal(true, {
          record: res.records[0],
          isUpdate: true,
          hideFooter: true,
        });
      } else {
        message.error(res.msg);
      }
    });
  }

  //处理处理指派实验组
  const openDepat = ref(false);
  function handleChangeDept() {
    if (selectedRows.value.length == 0) {
      message.error('请至少选择一条数据');
      return;
    }
    openDepat.value = true;
  }

  function handleOkChangeDept() {
    console.log(selectedDeptId.value);
    openDepat.value = false;

    selectedRows.value.forEach((item) => {
      let record = {
        id: item.id,
        departmentId: selectedDeptId.value,
      };
      setLoading(true);
      saveOrUpdate(record, true)
        .then((res) => {
          handleSuccess();
        })
        .finally(() => {
          setLoading(false);
          selectedDeptId.value = '';
        });
    });
  }
  // 监听变化
  watch(
    () => props.sampleId,
    (val) => {
      queryParam.sampleId = val;
      reload();
    }
  );

  //处理流程退回
  const openFlow = ref(false);
  const selectedStep = ref('');
  const selectedDeptReson = ref('');
  function handleRevert() {
    if (selectedRows.value.length == 0) {
      message.error('请至少选择一条数据');
      return;
    }
    openFlow.value = true;
  }
  function handleOkRevert() {
    console.log(selectedStep.value);
    if (selectedStep.value == '' || selectedDeptReson.value == '') {
      message.error('请选择退回节点和填写退回原因');
      return;
    }
    selectedRowKeys.value.forEach((item) => {
      let record = {
        id: item,
        step: selectedStep.value,
        reason: selectedDeptReson.value,
      };
      setLoading(true);
      revert(record)
        .then((res) => {
          handleSuccess();
        })
        .finally(() => {
          setLoading(false);
          selectedFLowId.value = '';
          selectedDeptReson.value = '';
        });
    });
    openFlow.value = false;
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
