<template>
  <BasicModal  v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <BasicForm @register="registerForm" ref="formRef" name="SysProductPackageForm" />
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs" v-show="canShow">
      <a-tab-pane tab="套餐详情表" key="sysProductPackageDetails" :forceRender="true">
        <JVxeTable

          keep-source
          resizable
          ref="sysProductPackageDetails"
          :loading="sysProductPackageDetailsTable.loading"
          :columns="sysProductPackageDetailsTable.columns"
          :dataSource="sysProductPackageDetailsTable.dataSource"
          :height="340"
          :rowNumber="true"
          :rowSelection="true"
          :disabled="formDisabled"
          :linkage-config="linkageConfig"
          :toolbar="true"
        />
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { sysProductPackageDetailsColumns } from '../SysProductPackage.data';
  import { saveOrUpdate, sysProductPackageDetailsList } from '../SysProductPackage.api';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  import { initDictOptions } from '@/utils/dict';
  import { JVxeLinkageConfig, JVxeTypes } from '@/components/jeecg/JVxeTable/src/types';
  import { querySysStandardByMethodId } from '@/views/lims_core/SysStandard.api';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const canShow = ref(true);
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const refKeys = ref(['sysProductPackageDetails']);
  const activeKey = ref('sysProductPackageDetails');
  const sysProductPackageDetails = ref();
  const tableRefs = { sysProductPackageDetails };
  const sysProductPackageDetailsTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysProductPackageDetailsColumns,
  });
  const formSchema: FormSchema[] = [
    {
      label: '名称',
      field: 'name',
      component: 'Input',
      dynamicRules: ({ model, schema }) => {
        return [{ required: true, message: '请输入名称!' }];
      },
    },
    {
      label: '业务类别',
      field: 'bizTypeId',
      component: 'JSearchSelect',
      componentProps:{
        dict:"biz_type,name,id",
        getPopupContainer: () => document.body,
      },
      dynamicRules: ({model,schema}) => {
        return [
          { required: true, message: '请输入业务类别!'},
        ];
      },
    },
    {
      label: '标准单价',
      field: 'stdPrice',
      component: 'InputNumber',
      dynamicRules: ({ model, schema }) => {
        return [{ required: true, message: '请输入标准单价!' }];
      },
    },
    {
      label: '标准工时',
      field: 'stdTat',
      component: 'Input',
      dynamicRules: ({ model, schema }) => {
        return [{ required: true, message: '请输入标准工时!' }];
      },
    },
    {
      label: '分类',
      field: 'category',
      component: 'JDictSelectTag',
      componentProps: ({ formModel }) => ({
        dictCode: 'package_category',
        onChange: (v) => {
          if (v == 'CAPABILITY') {
            formModel.standardId = '';
            canShow.value = true;
          } else {
            canShow.value = false;
          }
        },
      }),
      dynamicRules: ({ model, schema }) => {
        return [{ required: true, message: '请输入分类!' }];
      },
      defaultValue: 'CAPABILITY',
    },
    {
      label: '标准',
      field: 'standardId',
      component: 'JSearchSelect',
      componentProps: {
        getPopupContainer: () => document.body,
        dict: 'sys_standard where content_type_id != 1,name,id',
      },
      dynamicDisabled: (renderCallbackParams) => {
        return renderCallbackParams.model.category == 'CAPABILITY';
      },
    },
    // TODO 主键隐藏字段，目前写死为ID
    {
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
  ];
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    //labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      if (data?.record?.category == 'CAPABILITY') {
        canShow.value = true;
      } else {
        setTimeout(() => {
          canShow.value = false;
        }, 100);

      }
      requestSubTableData(sysProductPackageDetailsList, { id: data?.record?.id }, sysProductPackageDetailsTable);
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

  async function reset() {
    await resetFields();
    activeKey.value = 'sysProductPackageDetails';
    sysProductPackageDetailsTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);
    return {
      ...main, // 展开
      sysProductPackageDetailsList: allValues.tablesValue[0].tableData,
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    try {
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);

      await reset();
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  const linkageConfig = ref<JVxeLinkageConfig[]>([{ requestData: requestLinkageMethodData, key: 'capabilityId' }]);
  async function requestLinkageMethodData(parent) {
    if (parent == '' || parent == null || parent == undefined) {
      let result = await initDictOptions('sys_capability,name,id');
      console.log('result', result);
      return result;
    }
    let result = await initDictOptions('sys_method where cid=' + parent + ',name,id');
    console.log('result', result);
    if (result.length == 0) {
      result = await querySysStandardByMethodId({ id: parent });
      console.log('result1', result);
    }
    return result;
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
