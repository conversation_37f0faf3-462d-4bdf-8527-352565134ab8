/**
 * 简单有效的 a-select 文本选择修复方案
 * 专门针对 JDictSelectTag 等组件
 */

/**
 * 初始化简单的文本选择修复
 */
export function initSimpleSelectTextFix() {
  console.log('初始化简单的 a-select 文本选择修复');
  
  // 添加强制样式
  addForceStyles();
  
  // 添加事件处理
  addEventHandlers();
  
  // 监听DOM变化
  observeDOM();
}

/**
 * 添加强制样式
 */
function addForceStyles() {
  const style = document.createElement('style');
  style.id = 'simple-select-text-fix';
  style.textContent = `
    /* 强制所有文本可选择 */
    .ant-select *,
    .ant-select-selector *,
    .ant-select-selection-item *,
    .ant-select-selection-item-content * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      cursor: text !important;
    }
    
    /* 移除阻止选择的样式 */
    .ant-select-selector {
      user-select: text !important;
      -webkit-user-select: text !important;
      pointer-events: auto !important;
    }
    
    .ant-select-selection-item {
      user-select: text !important;
      -webkit-user-select: text !important;
      pointer-events: auto !important;
    }
  `;
  
  document.head.appendChild(style);
}

/**
 * 添加事件处理
 */
function addEventHandlers() {
  let isTextSelecting = false;
  let mouseDownTarget: HTMLElement | null = null;
  let mouseDownTime = 0;

  // 拦截所有可能阻止文本选择的事件
  const eventTypes = ['mousedown', 'mouseup', 'click', 'selectstart'];

  eventTypes.forEach(eventType => {
    document.addEventListener(eventType, (e) => {
      const target = e.target as HTMLElement;

      if (isInSelectComponent(target)) {
        if (eventType === 'mousedown') {
          mouseDownTarget = target;
          mouseDownTime = Date.now();
          isTextSelecting = false;

          // 允许文本选择开始
          e.stopPropagation();

        } else if (eventType === 'mouseup') {
          // 检查是否有文本被选中
          const selection = window.getSelection();
          if (selection && selection.toString().length > 0) {
            isTextSelecting = true;
            e.stopPropagation();
          }

        } else if (eventType === 'click') {
          // 如果正在选择文本或刚刚选择了文本，阻止下拉框打开
          const timeDiff = Date.now() - mouseDownTime;
          if (isTextSelecting || timeDiff > 200) {
            e.preventDefault();
            e.stopPropagation();
            return false;
          }

        } else if (eventType === 'selectstart') {
          // 明确允许文本选择开始
          e.stopPropagation();
          return true;
        }
      }
    }, true);
  });

  // 重置选择状态
  document.addEventListener('mousedown', (e) => {
    const target = e.target as HTMLElement;
    if (!isInSelectComponent(target)) {
      isTextSelecting = false;
      mouseDownTarget = null;
    }
  });

  // 双击复制
  document.addEventListener('dblclick', (e) => {
    const target = e.target as HTMLElement;
    if (isInSelectComponent(target)) {
      e.preventDefault();
      e.stopPropagation();

      const text = getTextContent(target);
      if (text) {
        copyText(text);
        showMessage('已复制: ' + text);
      }
    }
  }, true);
}

/**
 * 监听DOM变化，为新元素添加修复
 */
function observeDOM() {
  const observer = new MutationObserver(() => {
    fixNewElements();
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  // 初始修复
  fixNewElements();
}

/**
 * 修复新元素
 */
function fixNewElements() {
  const selects = document.querySelectorAll('.ant-select');
  selects.forEach(select => {
    const element = select as HTMLElement;
    if (!element.hasAttribute('data-text-fixed')) {
      element.setAttribute('data-text-fixed', 'true');

      // 彻底修复文本选择
      makeTextSelectable(element);
    }
  });
}

/**
 * 使元素及其所有子元素的文本完全可选择
 */
function makeTextSelectable(element: HTMLElement) {
  // 设置元素本身
  setTextSelectableStyles(element);

  // 递归处理所有子元素
  const walker = document.createTreeWalker(
    element,
    NodeFilter.SHOW_ELEMENT,
    null,
    false
  );

  let node;
  while (node = walker.nextNode()) {
    const el = node as HTMLElement;
    setTextSelectableStyles(el);

    // 特别处理文本节点的父元素
    if (el.childNodes.length > 0) {
      for (let i = 0; i < el.childNodes.length; i++) {
        const child = el.childNodes[i];
        if (child.nodeType === Node.TEXT_NODE) {
          setTextSelectableStyles(el);
          break;
        }
      }
    }
  }

  // 移除可能阻止文本选择的事件监听器
  removeBlockingEventListeners(element);
}

/**
 * 设置文本可选择样式
 */
function setTextSelectableStyles(element: HTMLElement) {
  // 设置CSS样式
  element.style.userSelect = 'text';
  element.style.webkitUserSelect = 'text';
  element.style.mozUserSelect = 'text';
  element.style.msUserSelect = 'text';
  element.style.cursor = 'text';

  // 移除可能阻止选择的属性
  element.removeAttribute('unselectable');
  element.removeAttribute('onselectstart');

  // 设置属性确保文本可选择
  element.setAttribute('style',
    element.getAttribute('style') +
    '; user-select: text !important; -webkit-user-select: text !important; cursor: text !important;'
  );
}

/**
 * 移除可能阻止文本选择的事件监听器
 */
function removeBlockingEventListeners(element: HTMLElement) {
  // 克隆元素来移除所有事件监听器，但保留Vue的事件
  const selectors = element.querySelectorAll('.ant-select-selector, .ant-select-selection-item');
  selectors.forEach(selector => {
    const el = selector as HTMLElement;

    // 只移除可能阻止文本选择的特定事件
    ['selectstart', 'dragstart'].forEach(eventType => {
      el.addEventListener(eventType, (e) => {
        e.stopPropagation();
        return true;
      }, true);
    });
  });
}

/**
 * 检查元素是否在select组件内
 */
function isInSelectComponent(element: HTMLElement): boolean {
  let current = element;
  while (current && current !== document.body) {
    if (current.classList.contains('ant-select') ||
        current.classList.contains('ant-select-selector') ||
        current.classList.contains('ant-select-selection-item')) {
      return true;
    }
    current = current.parentElement as HTMLElement;
  }
  return false;
}

/**
 * 获取文本内容
 */
function getTextContent(element: HTMLElement): string {
  // 尝试从选择项获取文本
  const selectionItem = element.closest('.ant-select-selection-item');
  if (selectionItem) {
    return (selectionItem.textContent || '').trim();
  }
  
  // 尝试从选择器获取文本
  const selector = element.closest('.ant-select-selector');
  if (selector) {
    return (selector.textContent || '').trim();
  }
  
  return (element.textContent || '').trim();
}

/**
 * 复制文本
 */
async function copyText(text: string) {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
  }
}

/**
 * 显示消息
 */
function showMessage(message: string) {
  const div = document.createElement('div');
  div.textContent = message;
  div.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
  `;
  
  document.body.appendChild(div);
  setTimeout(() => {
    if (div.parentNode) {
      div.parentNode.removeChild(div);
    }
  }, 2000);
}
