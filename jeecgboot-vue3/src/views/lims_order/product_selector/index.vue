<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="1200" @ok="handleSubmit">
    <BasicTable @register="register" :rowSelection="rowSelection" @edit-end="handleEditEnd" @edit-cancel="handleEditCancel" ref="tableRef">
      <template #toolbar>
        <a-button type="primary" @click="expandAll">展开全部</a-button>
        <a-button type="primary" @click="collapseAll">折叠全部</a-button>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<script lang="tsx" name="lims_core-sysProduct" setup>
  import { defineComponent, reactive, ref } from 'vue';
  import { BasicColumn, BasicTable, EditRecordRow } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { listVo } from './ProductSelector.api';
  import { list as listMethod } from '../../lims_core/SysMethod.api';
  import { columns, searchFormSchema } from './ProductSelector.data';
  import { message } from 'ant-design-vue';
  const emit = defineEmits(['register', 'success']);
  //设置标题
  const title = '营销产品选择器';
  const queryParam = reactive<any>({});
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    selectedRowKeys.value = []
  });
  const tableRef = ref();
  const { tableContext } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'productselector',
      },
      title: '营销产品选择器',
      isTreeTable: true,
      size: 'small',
      canResize: true,
      rowSelection: {
        type: 'checkbox',
        checkStrictly: false,
      },
      columns: columns,
      api: listVo,
      rowKey: 'id',
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: false,
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
  });
  function handleEditEnd({ record, index, key, value }: Recordable) {
    console.log(record, index, key, value);
    return false;
  }

  function handleEditCancel() {
    console.log('cancel');
  }

  //BasicTable绑定注册
  const [register, { expandAll, collapseAll }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  const handleSubmit = async () => {
    let canClose = true;
    selectedRows.value.forEach((item) => {
      if (item.methods && item.methods.length > 0 && item.methodId == null) {
        message.error('{' + item.name + '}-未选择方法');
        canClose = false;
        return;
      }
    });
    if (!canClose) {
      return;
    }
    emit('success', selectedRows.value);
    selectedRowKeys.value = []
    closeModal();
  };
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
