import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '分类',
    align: 'center',
    dataIndex: 'type_dictText',
  },
  {
    title: '客户代码',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '级别',
    align: 'center',
    dataIndex: 'grade_dictText',
  },
  {
    title: '省市（区）县',
    align: 'center',
    dataIndex: 'area',
  },
  {
    title: '地址',
    align: 'center',
    dataIndex: 'addr',
  },
  {
    title: '销售人员',
    align: 'center',
    dataIndex: 'salerId_dictText',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},

  },
  {
    label: '分类',
    field: 'type',
    mode: 'token',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'customer_type',
    },
    //colProps: {span: 6},
  },
  {
    label: '级别',
    field: 'grade',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'customer_grade',
    },
    //colProps: {span: 6},
  },
  {
    label: '省市（区）县',
    field: 'area',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
    //colProps: {span: 6},
  },
  {
    label: '销售人员',
    field: 'salerId',
    component: 'JSelectUser',
    componentProps: {},
    show:false,
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
    needTran: true,
  },
  {
    label: '分类',
    field: 'type',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_type',
    },
  },
  {
    label: '客户代码',
    field: 'code',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户代码!' }];
    },
  },
  {
    label: '级别',
    field: 'grade',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'customer_grade',
    },
  },
  {
    label: '省市（区）县',
    field: 'area',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入省市（区）县!' }];
    },
  },
  {
    label: '地址',
    field: 'addr',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入地址!' }];
    },
    needTran: true,
  },
  {
    label: '销售人员',
    field: 'salerId',
    component: 'JSelectUser',
    componentProps: {},
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表列表数据
export const sysCustomerContactColumns: BasicColumn[] = [
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '手机',
    align: 'center',
    dataIndex: 'phone',
  },
  {
    title: '电子邮箱',
    align: 'center',
    dataIndex: 'email',
  },
  {
    title: '传真',
    align: 'center',
    dataIndex: 'fax',
  },
  {
    title: '座机',
    align: 'center',
    dataIndex: 'telephone',
  },
  {
    title: '职位',
    align: 'center',
    dataIndex: 'title',
  },
  {
    title: '性别',
    align: 'center',
    dataIndex: 'sex',
  },
  {
    title: '生日',
    align: 'center',
    dataIndex: 'birthday',
  },
  {
    title: '爱好',
    align: 'center',
    dataIndex: 'hobby',
  },
];
//子表表格配置
export const sysCustomerContactJVxeColumns: JVxeColumn[] = [
  {
    title: '姓名',
    key: 'name',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '手机',
    key: 'phone',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '电子邮箱',
    key: 'email',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '传真',
    key: 'fax',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '座机',
    key: 'telephone',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '职位',
    key: 'title',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '性别',
    key: 'sex',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '生日',
    key: 'birthday',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '爱好',
    key: 'hobby',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  code: { title: '客户代码', order: 1, view: 'text', type: 'string' },
  type: { title: '分类', order: 2, view: 'list', type: 'string', dictCode: 'customer_type' },
  grade: { title: '级别', order: 3, view: 'list', type: 'string', dictCode: 'customer_grade' },
  area: { title: '省市（区）县', order: 4, view: 'pca', type: 'string' },
  addr: { title: '地址', order: 5, view: 'text', type: 'string' },
  salerId: { title: '销售人员', order: 6, view: 'sel_user', type: 'string' },
  //子表高级查询
  sysCustomerContact: {
    title: '客户联系人',
    view: 'table',
    fields: {
      name: { title: '姓名', order: 0, view: 'text', type: 'string' },
      phone: { title: '手机', order: 1, view: 'text', type: 'string' },
      email: { title: '电子邮箱', order: 2, view: 'text', type: 'string' },
      fax: { title: '传真', order: 3, view: 'text', type: 'string' },
      telephone: { title: '座机', order: 4, view: 'text', type: 'string' },
      title: { title: '职位', order: 5, view: 'text', type: 'string' },
      sex: { title: '性别', order: 6, view: 'text', type: 'string' },
      birthday: { title: '生日', order: 7, view: 'text', type: 'string' },
      hobby: { title: '爱好', order: 8, view: 'text', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
