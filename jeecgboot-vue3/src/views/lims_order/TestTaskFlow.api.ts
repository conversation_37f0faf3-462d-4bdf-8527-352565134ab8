import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_order/testTaskFlow/list',
  save = '/lims_order/testTaskFlow/add',
  edit = '/lims_order/testTaskFlow/edit',
  deleteOne = '/lims_order/testTaskFlow/delete',
  deleteBatch = '/lims_order/testTaskFlow/deleteBatch',
  importExcel = '/lims_order/testTaskFlow/importExcel',
  exportXls = '/lims_order/testTaskFlow/exportXls',
  submit = '/lims_order/testTaskFlow/submit',
  listProgress = '/lims_order/testTaskFlow/listProgress',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

/**
 * 扫节点
 * @param params
 */
export const submit = (params) => {
  return defHttp
    .get({ url: Api.submit, params })
    .then()
    .catch((err) => {
      console.log(err.toString().replace('Error: ', ''));
      console.log('speechSynthesis' in window);
      const synth = window.speechSynthesis;
      const utterance = new SpeechSynthesisUtterance(err.toString().replace('Error: ', ''));
      utterance.lang = 'zh-CN'; // 设置中文
      utterance.rate = 1; // 语速
      utterance.pitch = 1; // 音调
      utterance.volume = 50; // 音量
      synth.speak(utterance);
    });
};

/**
 * 进度列表
 */
export const listProgress = (params) => {
  return defHttp.get({ url: Api.listProgress, params });
};
