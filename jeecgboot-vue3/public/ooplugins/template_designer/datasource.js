(function (window, undefined) {
  window.oncontextmenu = function(e) {
    if (e.preventDefault)
      e.preventDefault();
    if (e.stopPropagation)
      e.stopPropagation();
    return false;
  };

  window.Asc.plugin.init = function(initData) {
    let dragSourceIsDataSourceChild = false;
    // 在插件 iframe 之外释放鼠标按钮时调用的函数
    window.Asc.plugin.onExternalMouseUp = function () {
      console.log("onExternalMouseUp");
      var event = document.createEvent('MouseEvents');
      event.initMouseEvent('mouseup', true, true, window, 1, 0, 0, 0, 0, false, false, false, false, 0, null);
      document.dispatchEvent(event);
    };

    // 递归生成树形结构的函数
    function createTree(data, parentText = null) {
      const ul = document.createElement('ul');
      ul.className = 'tree-list';

      data.forEach(item => {
        const li = document.createElement('li');
        li.className = 'tree-node expanded';

        // 创建容器 div
        const contentDiv = document.createElement('div');
        contentDiv.className = 'node-content';

        // 如果有子节点，添加展开/折叠箭头
        const hasChildren = item.children && item.children.length > 0;
        if (hasChildren) {
          const arrow = document.createElement('span');
          arrow.className = 'icon-arrow icon-arrow-down';
          arrow.addEventListener('click', function(e) {
            e.stopPropagation();
            const parentLi = e.target.closest('.tree-node');
            if (parentLi) {
              const isExpanded = parentLi.classList.contains('expanded');
              if (isExpanded) {
                parentLi.classList.remove('expanded');
                e.target.className = 'icon-arrow icon-arrow-right';
              } else {
                parentLi.classList.add('expanded');
                e.target.className = 'icon-arrow icon-arrow-down';
              }
            }
          });
          contentDiv.appendChild(arrow);
        } else {
          // 如果没有子节点，添加空白占位符保持对齐
          const spacer = document.createElement('span');
          spacer.className = 'icon-spacer';
          contentDiv.appendChild(spacer);
        }

        // 创建图标元素
        const icon = document.createElement('span');
        if (item.text === '数据源') {
          icon.className = 'icon-database';
        } else if (item.text === '动作') {  // 确保这个判断条件正确
          icon.className = 'icon-action';
        }else if (item.text === '子模板') {
          icon.className = 'icon-subreport';
        } else if (parentText === '数据源') {
          icon.className = 'icon-table';
        } else if (parentText === '子模板') {
          icon.className = 'icon-subtemplate';
        } else {
          const hasChildren = item.children && item.children.length > 0;
          icon.className = hasChildren ? 'icon-folder-opened' : 'icon-file';
        }

        // 创建文本节点
        const textSpan = document.createElement('span');
        textSpan.className = 'node-text';
        textSpan.textContent = item.text;

        // 组装节点
        contentDiv.appendChild(icon);
        contentDiv.appendChild(textSpan);
        li.appendChild(contentDiv);

        // 设置拖拽
        li.setAttribute('draggable', 'true');
        // li.addEventListener('dragstart', dragStart);

        // // 添加选中事件
        // li.addEventListener('click', function(e) {
        //     e.stopPropagation();
        //     const allTextSpans = document.querySelectorAll('.node-text');
        //     allTextSpans.forEach(span => span.classList.remove('highlighted')); // 移除高亮类
        //     textSpan.classList.add('highlighted'); // 添加高亮类到当前文本
        // });

        // 添加双击事件
        if (parentText === '子模板') {
          li.addEventListener('dblclick', function() {
            window.parent.parent.postMessage({ type: 'openDocEditor', payload: item }, '*'); // 发送消息到父窗口
          });
        }

        if (hasChildren) {
          const childrenContainer = document.createElement('div');
          childrenContainer.className = 'children';
          childrenContainer.appendChild(createTree(item.children, item.text));
          li.appendChild(childrenContainer);
        }

        ul.appendChild(li);
      });

      return ul;
    }

    // function dragStart(event) {
    //     const targetNode = event.target;
    //     const nodeText = targetNode.innerText.replace(/\s*\(.*?\)\s*/g, '').trim();

    //     // 获取父节点信息
    //     const parentNode = targetNode.closest('.tree-node').parentElement.closest('.tree-node');
    //     if (parentNode) {
    //         const parentContent = parentNode.querySelector('.node-text');
    //         if (parentContent) {
    //             const parentText = parentContent.innerText.replace(/\s*\(.*?\)\s*/g, '').trim();
    //             // 检查是否是"动作"的子节点
    //             if (parentText === '动作') {
    //                 const field = 'Action->' + nodeText;
    //                 event.dataTransfer.setData('text/plain', field);
    //             }
    //             // 检查是否是"数据源"的子节点的字段
    //             else {
    //                 const grandParentNode = parentNode.parentElement.closest('.tree-node');
    //                 if (grandParentNode && grandParentNode.querySelector('.node-text').innerText.includes('数据源')) {
    //                     dragSourceIsDataSourceChild = true;
    //                     const field = parentText + '.' + nodeText;
    //                     event.dataTransfer.setData('text/plain', field);
    //                 } else {
    //                     event.dataTransfer.setData('text/plain', nodeText);
    //                 }
    //             }
    //         }
    //     } else {
    //         event.dataTransfer.setData('text/plain', nodeText);
    //     }

    //     event.target.classList.add('dragging');
    // }

    function onDropEvent(data, ctrlKeyPressed) {
      Asc.scope.ctrlKeyPressed = ctrlKeyPressed;
      Asc.scope.field = data;
      Asc.scope.dragSourceIsDataSourceChild = dragSourceIsDataSourceChild;
      window.Asc.plugin.callCommand(function() {
        var oDocument = Api.GetDocument();
        console.log("oDocument",oDocument.Document);
        var selection = oDocument.Document.Selection;
        let oElement;
        if(!selection.La){
          oElement = oDocument.GetElement(selection.sa);
        }else{
          var oSection = oDocument.GetFinalSection();
          console.log("oSection",oSection);
          var oHeader = oSection.GetHeader("default", false);
          if (oHeader) {
            console.log("oHeader",oHeader);
            selection = oHeader.Document.Selection;
            console.log("oHeader Document",oHeader.Document);
            oElement = oHeader.GetElement(selection.sa);
            console.log("oElement",oElement);
          }
          var oFooter = oSection.GetFooter("default", false);
          if(oFooter) {
            console.log("oFooter",oFooter);
            selection = oFooter.Document.Selection;
            console.log("oFooter Document",oHeader.Document);
            oElement = oFooter.GetElement(selection.sa);
            console.log("oElement",oElement);
          }
          // if(!oElement){
          //     selection = oDocument.Document.Selection;
          //     oElement = oDocument.GetElement(selection.sa);
          // }
          // if(!oElement){

          // }
        }

        if (oElement) {
          let targetParagraph = null;
          let isFirstCell = false;
          let isLastCell = false;
          let table = null;
          if(oElement.Table){
            table = Asc.scope.field.split(".")[0];
            Asc.scope.field = Asc.scope.field.split(".")[1];
            var row = oElement.GetRow(oElement.Table.Selection.lE);
            const cell = oElement.GetCell(oElement.Table.Selection.sa.Ja.Ra,oElement.Table.Selection.sa.Ja.Nb);
            isFirstCell = oElement.Table.Selection.sa.Ja.Nb === 0;
            isLastCell = oElement.Table.Selection.sa.Ja.Nb === row.GetCellsCount() - 1;
            targetParagraph = cell.GetContent().GetElement(cell.GetContent().Document.Selection.sa);
          }else{
            targetParagraph = oElement;
          }

          if(targetParagraph){
            // 只有在按住Ctrl键且目标位置在表格中时才添加TableStart和TableEnd
            if(oElement.Table && Asc.scope.dragSourceIsDataSourceChild && Asc.scope.ctrlKeyPressed){
              if(isFirstCell){
                var oRunTableStart = Api.CreateRun();
                oRunTableStart.AddText("TableStart:"+table);
                targetParagraph.AddElement(oRunTableStart);
                oRunTableStart.WrapInMailMergeField();
              }else if(isLastCell){
                var oRunTableEnd = Api.CreateRun();
                oRunTableEnd.AddText("TableEnd:"+table);
                targetParagraph.AddElement(oRunTableEnd);
                oRunTableEnd.WrapInMailMergeField();
              }
            }
            var oRunField = Api.CreateRun();
            oRunField.AddText(Asc.scope.field);
            targetParagraph.AddElement(oRunField);
            oRunField.WrapInMailMergeField();
          }
        } else
          console.error('No paragraph found at the current selection.');
      }, undefined, undefined, function(result) {
        paste_done = true;
      });
    }
    //console.log("Asc.plugin.info.options",Asc.plugin.info.options);
    if (!Asc.plugin.info.options) {
      console.error('Token not available');
      return;
    }

    // 获取 templateId
    const templateId = Asc.plugin.info.options.docId;

    const headers = {
      'X-Access-Token': Asc.plugin.info.options.token,
      'Content-Type': 'application/json'
    };
    // 从后端接口获取树形结构数据并生成树形结构
    fetch(`/jeecg-boot/lims_core/sysTemplate/getDataSource?templateId=${templateId}`, {
      method: 'GET',
      headers: headers
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        //console.log('Fetched data:', data);
        const treeContainer = document.getElementById('treeContainer');
        const tree = createTree(data);
        treeContainer.appendChild(tree);
        //console.log('Final DOM structure:', treeContainer.innerHTML);
      })
      .catch(error => console.error('Error fetching tree data:', error));

    //监听来自 DocEditor.vue 的消息
    window.addEventListener('message', (event) => {
        if (event.data.type === 'customDropEvent') {
            event.preventDefault(); // 防止默认行为
            event.stopPropagation();
            const data = event.data.data;
            const ctrlKeyPressed = event.data.ctrlKey || false; // 获取 Ctrl 键状态
            // 处理接收到的数据
            onDropEvent(data, ctrlKeyPressed);
        }
    });
  };
})(window, undefined);
