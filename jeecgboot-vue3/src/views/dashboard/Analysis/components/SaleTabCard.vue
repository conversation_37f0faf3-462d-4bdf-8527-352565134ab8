<template>
  <a-card :loading="loading" :bordered="false" :body-style="{ padding: '0' }">
    <div class="salesCard">
      <a-tabs default-active-key="1" size="large" :tab-bar-style="{ marginBottom: '24px', paddingLeft: '16px' }">
        <template #rightExtra>
          <div class="extra-wrapper">
            <div class="extra-item">
              <a>今日</a>
              <a>本周</a>
              <a>本月</a>
              <a>本年</a>
            </div>
            <a-range-picker :style="{ width: '256px' }" />
          </div>
        </template>
        <a-tab-pane loading="true" tab="销售额" key="1">
          <a-row>
            <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
              <Bar :chartData="barData" :option="{ title: { text: '', textStyle: { fontWeight: 'lighter' } } }" height="40vh" :seriesColor="seriesColor"  />
            </a-col>
            <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <RankList title="销售排行榜" :list="rankList" />
            </a-col>
          </a-row>
        </a-tab-pane>
        <a-tab-pane tab="销售趋势" key="2">
          <a-row>
            <a-col :xl="16" :lg="12" :md="12" :sm="24" :xs="24">
              <Bar :chartData="barData.reverse()" :option="{ title: { text: '', textStyle: { fontWeight: 'lighter' } } }" height="40vh" :seriesColor="seriesColor" />
            </a-col>
            <a-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
              <RankList title="门店销售排行榜" :list="rankList" />
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-card>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import Bar from '/@/components/chart/Bar.vue';
  import RankList from '/@/components/chart/RankList.vue';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
    

  defineProps({
    loading: {
      type: Boolean,
    },
  });
  const { getThemeColor } = useRootSetting();
  const rankList = [];
  // for (let i = 0; i < 7; i++) {
  //   rankList.push({
  //     name: '白鹭岛 ' + (i + 1) + ' 号店',
  //     total: 1234.56 - i * 100,
  //   });
  // }

  rankList.push({
    name: '华南大区',
    total: 1234.56 - 1 * 100,
  });

  rankList.push({
    name: '华东大区',
    total: 1234.56 - 2 * 100,
  });

  rankList.push({
    name: '华北大区',
    total: 1234.56 - 3 * 100,
  });

  rankList.push({
    name: '西南大区',
    total: 1234.56 - 4* 100,
  });

  rankList.push({
    name: '检测业务专线',
    total: 1234.56 - 5 * 100,
  });

  rankList.push({
    name: '研发业务专线',
    total: 1234.56 - 6 * 100,
  });

  rankList.push({
    name: '国际业务专线',
    total: 1234.56 - 7 * 100,
  });

  const barData = [];
  for (let i = 0; i < 12; i += 1) {
    barData.push({
      name: `${i + 1}月`,
      value: Math.floor(Math.random() * 1000) + 200,
    });
  }
  const seriesColor = computed(() => {
    return getThemeColor.value
  })
</script>

<style lang="less" scoped>
  .extra-wrapper {
    line-height: 55px;
    padding-right: 24px;

    .extra-item {
      display: inline-block;
      margin-right: 24px;

      a {
        margin-left: 24px;
      }
    }
  }
</style>
