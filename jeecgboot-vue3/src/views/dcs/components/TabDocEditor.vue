<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    destroyOnClose
    :title="ooTitle"
    :width="800"
    :height="700"
    :minHeight="600"
    :canFullscreen="true"
    :maskClosable="false"
    :footer="null"
    :bodyStyle="{ padding: '0', overflow: 'hidden', height: 'calc(100% - 46px)' }"
    @full-screen="handleFullScreen"
    class="tab-doc-editor-modal"
  >
    <!-- 页签容器(单独作为绝对定位子元素，确保始终可见) -->
    <div class="tab-header-fixed" style="z-index: 9999; position: absolute;">
      <a-tabs
        v-model:activeKey="activeTabKey"
        type="editable-card"
        :hideAdd="true"
        @edit="onTabEdit"
        @change="handleTabChange"
      >
        <a-tab-pane v-for="doc in documents" :key="doc.id" :closable="documents.length > 1">
          <template #tab>
            <span :title="doc.title">
              {{ doc.title.length > 15 ? doc.title.substring(0, 15) + '...' : doc.title }}
              <a-badge v-if="doc.loading" status="processing" />
            </span>
          </template>
        </a-tab-pane>
      </a-tabs>
    </div>

    <!-- 文档内容区域 -->
    <div class="document-container">
      <div
        v-for="doc in documents"
        :key="doc.id"
        class="document-wrapper"
        :style="{ display: activeTabKey === doc.id ? 'block' : 'none' }"
      >
        <div v-if="doc.loading" class="loading-overlay">
          <a-spin tip="正在加载文档..." />
          <div class="loading-timeout">
            <a-button type="link" @click="() => forceLoadDocument(doc.id)">
              加载时间过长？点击此处继续
            </a-button>
          </div>
        </div>

        <!-- 添加错误状态显示 -->
        <div v-if="doc.hasError" class="error-overlay">
          <a-alert type="error" :message="doc.errorMessage || '文档加载失败'" show-icon />
          <div class="error-actions">
            <a-button type="primary" @click="() => forceLoadDocument(doc.id)">
              重试加载
            </a-button>
          </div>
        </div>

        <div
          v-if="activeTabKey === doc.id && doc.loaded"
          :id="'docEditorContainer_' + doc.id"
          class="editor-container"
        >
          <iframe
            v-if="false"
            style="display: none"
            name="dummy-frame"
          ></iframe>
          <DocumentEditor
            :id="'docEditor_' + doc.id"
            :ref="(el) => setEditorRef(el, doc.id)"
            :documentServerUrl="documentServerUrl"
            :config="doc.config"
            :events_onDocumentReady="() => onDocumentReady(doc.id)"
            :events_onError="onDocumentError"
            :onLoadComponentError="onLoadComponentError"
          />
        </div>
      </div>
    </div>

    <!-- 浮动按钮 -->
    <template v-if="floatButtons && floatButtons.length > 0">
      <FloatButton
        v-for="(btn, index) in floatButtons"
        :key="index"
        :tooltip="btn.tooltip"
        type="primary"
        class="doc-float-button"
        :style="{
          right: '0px',
          bottom: `${24 + index * 60}px`
        }"
        @click="handleButtonClick(btn)"
      >
        <template #icon>
          <component :is="getIconComponent(btn.icon)" />
        </template>
      </FloatButton>
    </template>
  </BasicModal>
  <MailModal @register="registerMailModal" />
</template>

<script setup lang="ts">
  import { ref, reactive, computed, onMounted, nextTick, onBeforeUnmount } from 'vue';
  import { getOnlyOfficeGlobalConfig } from '/@/utils/onlyOfficeGlobalConfig';
  import { DocumentEditor } from '@onlyoffice/document-editor-vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { useGlobSetting } from '/@/hooks/setting';
  import { FloatButton } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import * as Icons from '@ant-design/icons-vue';
  import MailModal from './mail/MailModal.vue';

  // 文档接口
  interface DocumentItem {
    id: string;
    title: string;
    config: any;
    loading: boolean;
    loaded: boolean;
    retryCount?: number;
    hasError?: boolean;
    errorMessage?: string;
  }

  // 定义按钮配置类型
  interface FloatButtonConfig {
    tooltip: string;
    icon: string | any;
    type?: string;
    action: string;
  }

  // 获取图标组件
  const getIconComponent = (icon: string | any) => {
    if (typeof icon !== 'string') {
      return icon;
    }
    return Icons[icon];
  };

  // 定义组件属性
  defineProps({
    floatButtons: {
      type: Array as () => FloatButtonConfig[],
      default: () => [],
    },
  });

  // 定义事件
  const emit = defineEmits(['register', 'buttonClick']);

  // 状态变量
  const documents = ref<DocumentItem[]>([]);
  const activeTabKey = ref<string>('');
  const ooTitle = ref('文档查看器');
  const isFullScreen = ref(false);
  const documentServerUrl = ref(getOnlyOfficeGlobalConfig().onlyOffice.documentServerUrl);
  const { title } = useGlobSetting();

  // 编辑器引用
  const editorRefs = reactive(new Map());

  // 邮件模态框
  const [registerMailModal, { openModal: openMailModal }] = useModal();

  // 设置编辑器引用
  const setEditorRef = (el: any, docId: string) => {
    if (el) {
      editorRefs.set(docId, el);
    }
  };

  // 获取当前活动文档
  const activeDocument = computed(() => {
    return documents.value.find((doc) => doc.id === activeTabKey.value);
  });

  // 处理按钮点击事件
  const handleButtonClick = (btn: FloatButtonConfig) => {
    if (!activeDocument.value) return;

    const editorInstance = editorRefs.get(activeDocument.value.id);

    // 收集所有tab的key，并提取下划线前的数字部分
    const allKeys = documents.value
      .map(doc => {
        const key = doc.config?.document?.key;
        if (!key) return null;
        // 匹配下划线前的数字部分
        const match = key.match(/^report(\d+)_/);
        return match ? match[1] : null;
      })
      .filter(Boolean);

    emit('buttonClick', {
      action: btn.action,
      editorInstance,
      documentId: activeDocument.value.id,
      doc: activeDocument.value,
      allKeys, // 新增：所有tab的key数组
    });

    if (btn.action === 'email') {
      handleEmail();
    }
  };

  // 处理邮件按钮点击事件
  const handleEmail = () => {
    if (!activeDocument.value) return;

    const editorInstance = editorRefs.get(activeDocument.value.id);
    if (editorInstance) {
      openMailModal(true, activeDocument.value.config?.editorConfig.plugins?.options?.all);
    }
  };

  // 处理标签页编辑（关闭标签）
  const onTabEdit = (targetKey: string, action: string) => {
    if (action === 'remove') {
      removeDocument(targetKey);
    }
  };

  // 处理标签页切换
  const handleTabChange = (activeKey: string) => {
    try {
      const doc = documents.value.find((d) => d.id === activeKey);
      if (!doc) return;

      console.log(`标签切换到: ${doc.title}, 加载状态: ${doc.loading}, 已加载: ${doc.loaded}`);

      // 如果文档尚未加载，标记为已加载，触发加载过程
      if (!doc.loaded) {
        doc.loaded = true;
        console.log(`首次加载文档: ${doc.title}`);
        return;
      }

      // 如果文档仍在加载中，不做特殊处理
      if (doc.loading) return;

      // 文档已加载且不在加载中，获取编辑器实例并刷新布局
      setTimeout(() => {
        const editorInstance = editorRefs.get(activeKey);
        if (editorInstance && editorInstance.refreshLayout) {
          editorInstance.refreshLayout();
        }
        fixIframeStyles();
      }, 300);
    } catch (error) {
      console.error('标签页切换处理出错:', error);
    }
  };

  // 移除文档
  const removeDocument = (docId: string) => {
    const docIndex = documents.value.findIndex((doc) => doc.id === docId);
    if (docIndex === -1) return;

    // 如果关闭的是当前活动标签，则切换到其他标签
    if (activeTabKey.value === docId) {
      if (documents.value.length > 1) {
        const newIndex = docIndex === 0 ? 1 : docIndex - 1;
        activeTabKey.value = documents.value[newIndex].id;
      }
    }

    // 移除文档
    documents.value.splice(docIndex, 1);
    editorRefs.delete(docId);
  };

  // 文档准备就绪事件
  const onDocumentReady = (docId: string) => {
    try {
      console.log(`文档准备就绪: ${docId}`);

      // 查找对应的文档
      const doc = documents.value.find((d) => d.id === docId);
      if (!doc) {
        console.warn(`找不到文档: ${docId}`);
        return;
      }

      // 更新加载状态
      doc.loading = false;
      doc.loaded = true;

      console.log(`文档 ${doc.title} 加载完成`);

      // 如果这是第一个文档，并且当前没有活动标签，则设置为活动标签
      if ((documents.value.length === 1 || documents.value[0]?.id === docId) && !activeTabKey.value) {
        activeTabKey.value = docId;
        console.log(`自动设置活动标签为: ${docId}`);
      }

      // 调整iframe样式确保页签可见
      nextTick(() => {
        // 延迟执行，确保DOM已更新
        setTimeout(() => {
          // 先固定样式
          fixIframeStyles();

          // 如果当前文档是活动文档，刷新布局
          if (activeTabKey.value === docId) {
            const editorInstance = editorRefs.get(docId);
            if (editorInstance?.refreshLayout) {
              console.log(`刷新文档 ${docId} 的布局`);
              editorInstance.refreshLayout();

              // 再次延迟执行，确保刷新布局后样式正确
              setTimeout(() => {
                fixIframeStyles();

                // 获取文档编辑器容器并调整其iframe位置
                const container = document.getElementById(`docEditorContainer_${docId}`);
                if (container) {
                  const iframe = container.querySelector('iframe');
                  if (iframe) {
                    // 确保iframe填满整个容器
                    adjustIframePosition(iframe);
                  }
                }
              }, 500);
            }
          }
        }, 300);
      });
    } catch (error) {
      console.error('文档准备就绪事件处理出错:', error);
    }
  };

  // 调整单个iframe的位置和大小
  const adjustIframePosition = (iframe: HTMLIFrameElement) => {
    try {
      // 设置基础样式
      iframe.style.position = 'absolute';
      iframe.style.left = '0';
      iframe.style.top = '46px'; // 给页签留出空间
      iframe.style.width = '100%';
      iframe.style.height = 'calc(100% - 46px)'; // 减去页签的高度
      iframe.style.border = 'none';
      iframe.style.margin = '0';
      iframe.style.padding = '0';
      iframe.style.zIndex = '1'; // 低于页签的z-index

      // 修复可能的空白问题
      if (iframe.contentDocument) {
        const htmlElement = iframe.contentDocument.documentElement;
        const bodyElement = iframe.contentDocument.body;

        if (htmlElement) {
          htmlElement.style.margin = '0';
          htmlElement.style.padding = '0';
          htmlElement.style.height = '100%';
          htmlElement.style.overflow = 'hidden';
        }

        if (bodyElement) {
          bodyElement.style.margin = '0';
          bodyElement.style.padding = '0';
          bodyElement.style.height = '100%';
          bodyElement.style.overflow = 'hidden';
        }
      }

      console.log('已优化iframe内部样式');
    } catch (error) {
      console.error('调整iframe位置出错:', error);
    }
  };

  // 文档加载错误处理
  const onDocumentError = (errorCode: string, errorDescription: string) => {
    console.log('文档加载错误被触发:', { errorCode, errorDescription });

    // 使用console.error确保错误始终被记录到控制台
    console.error(`文档加载错误 (${new Date().toLocaleTimeString()}): `, errorCode, errorDescription);

    try {
      if (!activeDocument.value) {
        console.warn('activeDocument为空，无法更新文档状态');
        return;
      }

      // 更新加载状态并标记错误
      activeDocument.value.loading = false;
      activeDocument.value.hasError = true;
      activeDocument.value.errorMessage = `${errorCode}: ${errorDescription}`;

      // 显示更友好的错误通知
      message.error(`文档加载错误: ${errorDescription || errorCode}`);
    } catch (err) {
      console.error('处理文档错误时发生异常:', err);
      alert(`文档加载失败: ${errorCode}`);
    }
  };

  // 加载组件错误处理
  const onLoadComponentError = (error: any) => {
    console.error('加载OnlyOffice组件错误:', error);
  };

  // 强制加载文档
  const forceLoadDocument = (docId: string) => {
    const doc = documents.value.find((d) => d.id === docId);
    if (!doc) return;

    // 标记为已加载，并重置loading状态，触发重新加载
    doc.loaded = true;
    doc.loading = true;

    // 清除错误状态
    doc.hasError = false;
    doc.errorMessage = '';

    // 增加重试计数
    doc.retryCount = (doc.retryCount || 0) + 1;

    console.log(`正在重试加载文档 ${docId}，重试次数: ${doc.retryCount}`);
  };

  // 修复所有iframe样式，确保它们不会覆盖页签
  const fixIframeStyles = () => {
    try {
      // 强制调整所有iframe的样式
      const iframes = document.querySelectorAll('.tab-doc-editor-modal iframe');
      iframes.forEach((iframe: HTMLIFrameElement) => {
        iframe.style.position = 'absolute';
        iframe.style.left = '0';
        iframe.style.width = '100%';
        iframe.style.border = 'none';
        iframe.style.margin = '0';
        iframe.style.padding = '0';
        iframe.style.zIndex = '1';

        // 确保iframe不遮挡到页签栏
        iframe.style.top = '46px';
        iframe.style.height = 'calc(100% - 46px)';

        // 检查iframe是否有父元素，并适当调整其高度
        const parent = iframe.parentElement;
        if (parent) {
          parent.style.height = '100%';
          parent.style.overflow = 'hidden';
          parent.style.margin = '0';
          parent.style.padding = '0';
        }
      });

      // 查找编辑器容器并确保其样式正确
      const editorContainers = document.querySelectorAll('.tab-doc-editor-modal .editor-container');
      editorContainers.forEach((container: HTMLElement) => {
        container.style.position = 'absolute';
        container.style.left = '0';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.overflow = 'hidden';
        container.style.margin = '0';
        container.style.padding = '0';
      });

      // 确保页签容器可见
      const tabHeader = document.querySelector('.tab-doc-editor-modal .tab-header-fixed');
      if (tabHeader) {
        (tabHeader as HTMLElement).style.display = 'block';
        (tabHeader as HTMLElement).style.visibility = 'visible';
        (tabHeader as HTMLElement).style.zIndex = '9999';
      }

      // 直接注入全局样式
      injectGlobalCSS();

      console.log('已应用iframe样式优化');
    } catch (error) {
      console.error('修复iframe样式出错:', error);
    }
  };

  // 尝试刷新当前活动文档，解决初始加载问题
  const refreshActiveDocument = () => {
    if (!activeTabKey.value) return;

    const doc = documents.value.find(d => d.id === activeTabKey.value);
    if (!doc) return;

    console.log(`尝试刷新当前活动文档: ${doc.title}`);

    // 如果文档卡在加载状态超过3秒，尝试强制刷新
    if (doc.loading) {
      // 如果文档长时间未加载成功，尝试重新激活它
      doc.loaded = true;

      // 延迟执行，给DOM时间进行渲染
      setTimeout(() => {
        // 尝试通过切换到其他标签再切回来解决问题
        if (documents.value.length > 1) {
          const otherDoc = documents.value.find(d => d.id !== activeTabKey.value);
          if (otherDoc) {
            console.log('暂时切换到其他标签再切回以刷新视图');
            activeTabKey.value = otherDoc.id;

            setTimeout(() => {
              activeTabKey.value = doc.id;
            }, 100);
          }
        } else {
          // 只有一个标签的情况下，尝试直接刷新编辑器
          const editorInstance = editorRefs.get(doc.id);
          if (editorInstance?.refreshLayout) {
            console.log('刷新编辑器布局');
            editorInstance.refreshLayout();
          }
        }
      }, 300);
    }
  };

  // 注入全局CSS，强制控制iframe样式
  const injectGlobalCSS = () => {
    const styleId = 'tab-doc-editor-style';
    if (document.getElementById(styleId)) return;

    const styleEl = document.createElement('style');
    styleEl.id = styleId;
    styleEl.innerHTML = `
      .tab-doc-editor-modal .tab-header-fixed {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 46px !important;
        z-index: 9999 !important;
        background: #fff !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        padding: 8px 16px 0 16px !important;
      }
      .tab-doc-editor-modal .ant-tabs-nav {
        margin-bottom: 0 !important;
      }
      .tab-doc-editor-modal .document-container {
        position: absolute !important;
        top: 46px !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        overflow: hidden !important;
      }
      .tab-doc-editor-modal iframe {
        position: absolute !important;
        top: 46px !important;
        left: 0 !important;
        width: 100% !important;
        height: calc(100% - 46px) !important;
        z-index: 1 !important;
        border: none !important;
        margin: 0 !important;
        padding: 0 !important;
      }
      .tab-doc-editor-modal .document-wrapper {
        position: absolute !important;
        top: 46px !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
      }
      .tab-doc-editor-modal .editor-container {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        overflow: hidden !important;
        margin: 0 !important;
        padding: 0 !important;
      }
      .tab-doc-editor-modal .loading-overlay {
        z-index: 9999 !important;
      }
      .tab-doc-editor-modal .doc-float-button {
        z-index: 9999 !important;
        right: 0px !important;
      }
      .tab-doc-editor-modal .ant-modal-body {
        height: 100% !important;
        padding: 0 !important;
        overflow: hidden !important;
      }
      .tab-doc-editor-modal .document-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
      .tab-doc-editor-modal .editor-container {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
      .tab-doc-editor-modal .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .tab-doc-editor-modal .loading-timeout {
        margin-top: 20px;
      }
      .tab-doc-editor-modal .error-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 100;
        padding: 20px;
      }
      .tab-doc-editor-modal .error-actions {
        margin-top: 20px;
      }
      .tab-doc-editor-modal .doc-float-button {
        z-index: 9999 !important;
      }
      .tab-doc-editor-modal .ant-modal-body {
        height: 100% !important;
        padding: 0 !important;
        overflow: hidden !important;
      }
      .tab-doc-editor-modal .aui-body {
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
      }
    `;
    document.head.appendChild(styleEl);
  };

  // 处理全屏事件
  const handleFullScreen = (val: boolean) => {
    isFullScreen.value = val;

    // 在全屏状态变化后，调整编辑器大小
    if (activeDocument.value?.id) {
      const editorInstance = editorRefs.get(activeDocument.value.id);
      if (editorInstance && editorInstance.refreshLayout) {
        setTimeout(() => {
          fixIframeStyles();
          editorInstance.refreshLayout();
        }, 300);
      }
    }
  };

  // Modal内部注册
  const [registerModal] = useModalInner(async (data) => {
    if (data) {
      console.log('TabDocEditor初始化数据:', data);
      // 兼容 configs 和 documents 两种写法
      const docArr = data.documents || data.configs;
      if (docArr && Array.isArray(docArr) && docArr.length > 0) {
        documents.value = [];
        editorRefs.clear();

        console.log(`初始化 ${docArr.length} 个文档`);

        docArr.forEach((docData: any, idx: number) => {
          // 兼容 configs 结构
          let config = docData.config || docData;
          const docId = docData.id || `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          const docTitle = docData.title || config.document?.title || `文档${idx + 1}`;

          console.log(`文档${idx + 1} ID: ${docId}, 标题: ${docTitle}`);
          console.log(`文档${idx + 1} 配置:`, config);

          if (config.document?.url) {
            console.log(`文档${idx + 1} URL: ${config.document.url}`);
          } else {
            console.warn(`文档${idx + 1} 没有URL!`);
          }

          documents.value.push({
            id: docId,
            title: docTitle,
            config: config,
            loading: true,
            loaded: idx === 0, // 预先加载第一个文档
          });
        });

        if (documents.value.length > 0) {
          activeTabKey.value = documents.value[0].id;
          console.log('设置活动标签为:', activeTabKey.value);

          // 确保在下一个渲染周期后激活第一个文档
          nextTick(() => {
            setTimeout(() => {
              // 手动触发一次布局刷新
              fixIframeStyles();
            }, 500);
          });
        }
      } else {
        console.warn('没有提供有效的文档数据', data);
      }
    }
  });

  // 监听DOM变化
  const setupDOMObserver = () => {
    const observer = new MutationObserver((mutations) => {
      let iframeAdded = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node: any) => {
            if (node.nodeName === 'IFRAME') {
              iframeAdded = true;
            }
          });
        }
      });

      if (iframeAdded) {
        fixIframeStyles();
      }
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    return observer;
  };

  // 组件挂载
  let domObserver: any = null;
  let initDocumentTimer: any = null;
  let styleCheckInterval: any = null;

  onMounted(() => {
    // 注入全局样式
    injectGlobalCSS();

    // 设置观察器
    domObserver = setupDOMObserver();

    // 设置窗口大小变化监听
    window.addEventListener('resize', fixIframeStyles);

    // 延迟尝试刷新初始文档，解决首次加载问题
    initDocumentTimer = setTimeout(() => {
      if (documents.value.length > 0 && documents.value[0].loading) {
        console.log('尝试自动刷新初始文档');
        refreshActiveDocument();
      }
    }, 2000);

    // 设置周期性检查样式的定时器，确保在组件渲染过程中样式始终正确
    styleCheckInterval = setInterval(() => {
      if (activeDocument.value && !activeDocument.value.loading) {
        // 只对已加载完成的活动文档执行样式修复
        fixIframeStyles();

        // 特别检查活动文档的iframe
        const container = document.getElementById(`docEditorContainer_${activeDocument.value.id}`);
        if (container) {
          const iframe = container.querySelector('iframe');
          if (iframe) {
            adjustIframePosition(iframe);
          }
        }
      }
    }, 3000); // 每3秒检查一次
  });

  // 组件卸载前清理
  onBeforeUnmount(() => {
    // 删除全局样式
    const styleEl = document.getElementById('tab-doc-editor-style');
    if (styleEl) {
      styleEl.remove();
    }

    // 断开观察器
    if (domObserver) {
      domObserver.disconnect();
    }

    // 清除定时器
    if (initDocumentTimer) {
      clearTimeout(initDocumentTimer);
    }

    // 清除样式检查定时器
    if (styleCheckInterval) {
      clearInterval(styleCheckInterval);
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', fixIframeStyles);
  });
</script>

<style lang="less" scoped>
  :deep(.ant-modal-body) {
    padding: 0 !important;
    overflow: hidden !important;
  }

  /* 加载覆盖层 */
  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .loading-timeout {
    margin-top: 20px;
  }

  /* 错误覆盖层 */
  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 100;
    padding: 20px;
  }

  .error-actions {
    margin-top: 20px;
  }

  /* 文档包装器 */
  .document-wrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  /* 编辑器容器 */
  .editor-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    overflow: hidden;
  }

  :deep(.aui-body) {
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
  }

  /* 浮动按钮样式 */
  .doc-float-button {
    position: absolute !important;
    right: 0px !important;
    z-index: 9999 !important;
  }
</style>
