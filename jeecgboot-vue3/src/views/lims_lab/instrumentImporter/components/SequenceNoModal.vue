<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="设置序列号"
    :width="600"
    :okText="'确定'"
    @ok="handleSubmit"
    destroyOnClose
  >
    <div class="sequence-modal-content">
      <a-form :model="formState" layout="vertical">
        <a-alert
          type="info"
          message="所有行将使用相同的序列号"
          style="margin-bottom: 16px"
          show-icon
        />
        <a-form-item label="序列号前缀" :help="'当前日期: ' + currentDate">
          <a-input v-model:value="formState.prefix" placeholder="请输入序列号前缀" />
        </a-form-item>
        <a-form-item label="序号">
          <a-input-number
            v-model:value="formState.startNumber"
            :min="1"
            :max="999"
            style="width: 100%"
            placeholder="请输入序号(1-999)"
          />
        </a-form-item>
        <a-divider />
        <div class="preview-section">
          <h3>预览</h3>
          <a-table
            :columns="columns"
            :dataSource="previewData"
            :pagination="false"
            size="small"
            :scroll="{ y: 300 }"
          />
        </div>
      </a-form>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
  import { BasicModal } from '@/components/Modal';
  import { useModalInner } from '@/components/Modal';
  import { reactive, ref, computed, watch } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import dayjs from 'dayjs';

  const emit = defineEmits(['register', 'success']);
  const { createMessage } = useMessage();

  // 表格列定义
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
    },
    {
      title: '样品编号',
      dataIndex: 'sampleNo',
      width: 150,
    },
    {
      title: '序列号',
      dataIndex: 'sequenceNo',
      width: 150,
    },
  ];

  // 表单状态
  const formState = reactive({
    prefix: '',
    startNumber: 1,
  });

  // 原始数据
  const originalData = ref<any[]>([]);

  // 预览数据
  const previewData = computed(() => {
    // 生成一个序列号，所有行共用
    const sequenceNo = generateSequenceNo();

    return originalData.value.map((item, index) => {
      return {
        ...item,
        index: index + 1,
        sequenceNo,
      };
    });
  });

  // 当前日期，格式为YYYYMMDD
  const currentDate = computed(() => {
    return dayjs().format('YYYYMMDD');
  });

  // 监听表单变化，更新预览
  watch([() => formState.prefix, () => formState.startNumber], () => {
    // 预览数据会自动更新，因为它是计算属性
  });

  // 生成序列号 - 所有行使用相同的序列号
  function generateSequenceNo(): string {
    const prefix = formState.prefix || currentDate.value;
    const number = formState.startNumber.toString().padStart(3, '0');
    return `${prefix}${number}`;
  }

  // 注册弹窗
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false });

    if (data && Array.isArray(data)) {
      originalData.value = data.map(item => ({
        ...item,
        sampleNo: item.sampleNo || '未命名样品'
      }));

      // 默认使用当前日期作为前缀
      formState.prefix = currentDate.value;
      formState.startNumber = 1;
    }
  });

  // 提交表单
  async function handleSubmit() {
    try {
      // 验证
      if (!formState.prefix) {
        createMessage.warning('请输入序列号前缀');
        return;
      }

      if (!formState.startNumber || formState.startNumber < 1 || formState.startNumber > 999) {
        createMessage.warning('起始序号必须在1-999之间');
        return;
      }

      setModalProps({ confirmLoading: true });

      // 生成序列号 - 所有行使用相同的序列号
      const sequenceNo = generateSequenceNo();

      // 为所有行设置相同的序列号
      const dataWithSequenceNo = originalData.value.map((item) => {
        return {
          ...item,
          sequenceNo
        };
      });

      // 返回处理后的数据
      emit('success', dataWithSequenceNo);
      closeModal();
    } catch (error: any) {
      createMessage.error(error.message || '操作失败');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
</script>

<style scoped lang="less">
.sequence-modal-content {
  padding: 0 10px;

  .preview-section {
    margin-top: 10px;

    h3 {
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
