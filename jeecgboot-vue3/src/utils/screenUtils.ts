/**
 * 屏幕工具函数 - 用于多显示器支持 (PWA环境)
 */
import { ref, onMounted, onUnmounted, shallowRef } from 'vue';

// 定义显示器信息接口
export interface ScreenInfo {
  id: string;
  left: number;
  top: number;
  width: number;
  height: number;
  isPrimary?: boolean;
  label?: string;
}

// 声明Window Management API类型
declare global {
  interface Window {
    getScreenDetails?: () => Promise<{
      screens: Array<{
        availWidth: number;
        availHeight: number;
        availLeft: number;
        availTop: number;
        width: number;
        height: number;
        left: number;
        top: number;
        id: string;
        isPrimary: boolean;
        devicePixelRatio: number;
        label?: string;
      }>;
      currentScreen: {
        availWidth: number;
        availHeight: number;
        availLeft: number;
        availTop: number;
        width: number;
        height: number;
        left: number;
        top: number;
        id: string;
        isPrimary: boolean;
        devicePixelRatio: number;
        label?: string;
      };
    }>;
  }

  interface Screen {
    // 扩展Screen接口，添加可能存在的属性
    availLeft?: number;
    availTop?: number;
  }
}

// 存储检测到的显示器信息
const screens = ref<ScreenInfo[]>([]);
// 存储原始的屏幕详情对象
const screenDetails = shallowRef<any>(null);
// 标记是否已经初始化
const initialized = ref(false);

// 权限状态
const permissionStatus = ref<'granted' | 'denied' | 'prompt' | 'unknown'>('unknown');

// 初始化函数，确保只调用一次异步检测
let initPromise: Promise<ScreenInfo[]> | null = null;

/**
 * 请求屏幕详情权限
 * 这个函数必须在用户交互事件处理程序中调用（如点击事件）
 * 返回是否成功获取权限
 */
export async function requestScreenPermission(): Promise<boolean> {
  if (!('getScreenDetails' in window)) {
    console.warn('此浏览器不支持Window Management API');
    permissionStatus.value = 'denied';
    return false;
  }

  // 保存当前的屏幕信息，以便在请求失败时恢复
  const originalScreens = [...screens.value];
  console.log('保存原始屏幕信息:', originalScreens);

  try {
    console.log('正在请求屏幕详情权限...');
    // 请求屏幕详情权限 - 这必须在用户交互事件处理程序中调用
    const details = await window.getScreenDetails();
    screenDetails.value = details;
    permissionStatus.value = 'granted';

    // 更新屏幕信息
    const detectedScreens = details.screens.map((screen, index) => {
      // 确定显示器ID - 如果是主显示器，使用'primary'，否则使用'extended'或原始ID
      let id = screen.id;
      let label = screen.label;

      // 如果是主显示器，强制使用'primary'作为ID
      if (screen.isPrimary) {
        id = 'primary';
        label = '主显示器';
      }
      // 如果是第二个显示器且不是主显示器，使用'extended'
      else if (index === 0 || index === 1) {
        id = 'extended';
        label = '扩展显示器';
      }
      // 其他显示器使用原始ID或生成一个唯一ID
      else {
        id = screen.id || `screen-${index}`;
        label = screen.label || `显示器 ${index + 1}`;
      }

      return {
        id,
        left: screen.availLeft || 0,
        top: screen.availTop || 0,
        width: screen.availWidth || screen.width,
        height: screen.availHeight || screen.height,
        isPrimary: screen.isPrimary,
        label,
      };
    });

    // 检查是否检测到了显示器
    if (detectedScreens.length > 0) {
      screens.value = detectedScreens;
      console.log('成功获取屏幕详情:', detectedScreens);
      return true;
    } else {
      console.warn('Window Management API未返回任何显示器信息，使用回退方法');
      // 如果没有检测到显示器，使用回退方法
      const fallbackScreens = await initScreenDetection();
      screens.value = fallbackScreens.length > 0 ? fallbackScreens : originalScreens;
      return fallbackScreens.length > 0;
    }
  } catch (error) {
    console.error('请求屏幕详情权限失败:', error);
    permissionStatus.value = 'denied';

    // 恢复原始屏幕信息
    if (originalScreens.length > 0) {
      console.log('恢复原始屏幕信息:', originalScreens);
      screens.value = originalScreens;
    } else {
      // 如果没有原始屏幕信息，尝试使用基本方法检测
      const fallbackScreens = await initScreenDetection();
      screens.value = fallbackScreens;
    }

    return false;
  }
}

/**
 * 初始化屏幕检测
 * 这个函数只会被调用一次，后续调用会返回缓存的结果
 * 注意：此函数不会请求高级权限，只会使用基本的screen API
 */
async function initScreenDetection(): Promise<ScreenInfo[]> {
  // 如果已经有检测到的屏幕，并且不是强制刷新，直接返回
  if (screens.value.length > 0 && initialized.value) {
    console.log('使用已缓存的屏幕信息:', screens.value);
    return screens.value;
  }

  // 如果已经有一个初始化过程在进行中，等待它完成
  if (initPromise) {
    console.log('等待已有的初始化过程完成');
    return initPromise;
  }

  console.log('开始初始化屏幕检测');
  initPromise = (async () => {
    try {
      let detectedScreens: ScreenInfo[] = [];

      // 如果已经有屏幕详情权限，使用它
      if (screenDetails.value && screenDetails.value.screens && screenDetails.value.screens.length > 0) {
        console.log('使用已有的屏幕详情');
        try {
          detectedScreens = screenDetails.value.screens.map((screen, index) => {
            // 确定显示器ID - 保持与requestScreenPermission函数中相同的逻辑
            let id = screen.id;
            let label = screen.label;

            // 如果是主显示器，强制使用'primary'作为ID
            if (screen.isPrimary) {
              id = 'primary';
              label = '主显示器';
            }
            // 如果是第二个显示器且不是主显示器，使用'extended'
            else if (index === 0 || index === 1) {
              id = 'extended';
              label = '扩展显示器';
            }
            // 其他显示器使用原始ID或生成一个唯一ID
            else {
              id = screen.id || `screen-${index}`;
              label = screen.label || `显示器 ${index + 1}`;
            }

            return {
              id,
              left: screen.availLeft || screen.left || 0,
              top: screen.availTop || screen.top || 0,
              width: screen.availWidth || screen.width || window.screen.width,
              height: screen.availHeight || screen.height || window.screen.height,
              isPrimary: screen.isPrimary || false,
              label,
            };
          });
          console.log('从screenDetails解析的显示器:', detectedScreens);
        } catch (e) {
          console.error('解析screenDetails时出错:', e);
        }
      }

      // 如果高级API失败或没有返回有效数据，使用基本的screen API
      if (detectedScreens.length === 0) {
        console.log('使用基本screen API回退方法');

        // 主显示器信息
        const primaryScreen: ScreenInfo = {
          id: 'primary',
          left: 0,
          top: 0,
          width: window.screen.width,
          height: window.screen.height,
          isPrimary: true,
          label: '主显示器',
        };

        detectedScreens = [primaryScreen];

        // 检查是否有扩展显示器的迹象
        // 注意：这些属性在某些浏览器中可能不存在
        const availLeft = (window.screen as any).availLeft;
        const availTop = (window.screen as any).availTop;

        const hasExtendedDisplay =
          (typeof availLeft !== 'undefined' && availLeft !== 0) ||
          (typeof availTop !== 'undefined' && availTop !== 0) ||
          window.screen.availWidth !== window.screen.width ||
          window.screen.availHeight !== window.screen.height;

        if (hasExtendedDisplay) {
          console.log('检测到可能存在扩展显示器');
          // 尝试推断扩展显示器
          const extendedScreen: ScreenInfo = {
            id: 'extended',
            left: availLeft < 0 ? availLeft : window.screen.width,
            top: availTop < 0 ? availTop : 0,
            width: Math.abs(window.screen.availWidth - window.screen.width) || 1920, // 使用合理的默认值
            height: window.screen.availHeight || 1080, // 使用合理的默认值
            isPrimary: false,
            label: '扩展显示器',
          };

          // 确保扩展显示器的尺寸合理
          if (extendedScreen.width < 100) extendedScreen.width = 1920;
          if (extendedScreen.height < 100) extendedScreen.height = 1080;

          detectedScreens.push(extendedScreen);
          console.log('添加了扩展显示器:', extendedScreen);
        }
      }

      // 如果仍然没有检测到任何显示器，添加模拟显示器
      if (detectedScreens.length === 0) {
        console.log('未检测到任何显示器，添加模拟显示器');
        detectedScreens = [
          {
            id: 'primary',
            left: 0,
            top: 0,
            width: window.screen.width || 1920,
            height: window.screen.height || 1080,
            isPrimary: true,
            label: '主显示器',
          },
        ];

        // 添加一个模拟的扩展显示器
        detectedScreens.push({
          id: 'extended',
          left: window.screen.width || 1920,
          top: 0,
          width: 1920,
          height: 1080,
          isPrimary: false,
          label: '扩展显示器 (模拟)',
        });
      }

      // 确保至少有一个显示器被标记为主显示器
      const hasPrimary = detectedScreens.some((s) => s.isPrimary);
      if (!hasPrimary && detectedScreens.length > 0) {
        detectedScreens[0].isPrimary = true;
        detectedScreens[0].label = detectedScreens[0].label || '主显示器';
      }

      // 更新ref
      console.log('最终检测到的显示器:', detectedScreens);
      screens.value = detectedScreens;
      initialized.value = true;

      return detectedScreens;
    } catch (error) {
      console.error('检测显示器时出错:', error);
      // 确保至少返回主显示器和一个扩展显示器
      const fallbackScreens = [
        {
          id: 'primary',
          left: 0,
          top: 0,
          width: window.screen.width || 1920,
          height: window.screen.height || 1080,
          isPrimary: true,
          label: '主显示器 (回退)',
        },
        {
          id: 'extended',
          left: window.screen.width || 1920,
          top: 0,
          width: 1920,
          height: 1080,
          isPrimary: false,
          label: '扩展显示器 (回退)',
        },
      ];

      console.log('使用回退显示器:', fallbackScreens);
      screens.value = fallbackScreens;
      return fallbackScreens;
    } finally {
      // 完成后清除initPromise，允许将来重新初始化
      setTimeout(() => {
        initPromise = null;
      }, 1000);
    }
  })();

  return initPromise;
}

/**
 * 检测当前可用的显示器
 * 这是一个同步函数，返回当前已知的显示器信息
 * 如果尚未初始化，它会触发异步初始化并返回一个默认值
 */
export function detectScreens(): ScreenInfo[] {
  if (screens.value.length === 0) {
    // 如果尚未初始化，触发异步初始化
    initScreenDetection().catch(console.error);

    // 返回一个默认值
    return [
      {
        id: 'primary',
        left: 0,
        top: 0,
        width: window.screen.width,
        height: window.screen.height,
        isPrimary: true,
        label: '主显示器 (正在检测中...)',
      },
    ];
  }

  return screens.value;
}

/**
 * 获取当前鼠标位置所在的显示器
 * @param x 全局X坐标（相对于整个虚拟屏幕空间）
 * @param y 全局Y坐标（相对于整个虚拟屏幕空间）
 */
export function getCurrentScreen(x: number, y: number): ScreenInfo {
  const allScreens = screens.value.length > 0 ? screens.value : detectScreens();

  // 调试信息
  console.log(`检测坐标 (${x}, ${y}) 所在的显示器`);

  // 在PWA环境中，我们需要考虑窗口位置
  // 将浏览器窗口坐标转换为虚拟多显示器空间坐标
  const globalX = window.screenX + x;
  const globalY = window.screenY + y;

  // 检查每个屏幕
  for (const screen of allScreens) {
    // 调试每个屏幕的边界
    console.log(`检查显示器 ${screen.id}: 边界 (${screen.left}, ${screen.top}) 到 (${screen.left + screen.width}, ${screen.top + screen.height})`);

    if (globalX >= screen.left && globalX <= screen.left + screen.width && globalY >= screen.top && globalY <= screen.top + screen.height) {
      console.log(`找到显示器: ${screen.id}`);
      return screen;
    }
  }

  // 如果找不到匹配的显示器，返回主显示器
  console.log('未找到匹配的显示器，使用主显示器');
  const primaryScreen = allScreens.find((s) => s.isPrimary);
  return primaryScreen || allScreens[0];
}

/**
 * 在指定显示器上居中定位对话框
 * @param screenId 显示器ID
 * @returns 居中位置的坐标
 */
export function centerOnScreen(screenId: string): { x: number; y: number } {
  const allScreens = screens.value.length > 0 ? screens.value : detectScreens();

  // 查找指定的显示器，如果找不到则使用主显示器或第一个显示器
  const screen = allScreens.find((s) => s.id === screenId) || allScreens.find((s) => s.isPrimary === true) || allScreens[0];

  console.log(`为显示器 ${screen.id} 计算中心位置, 显示器信息:`, screen);

  // 计算显示器中心点（全局坐标）
  const centerX = screen.left + screen.width / 2;
  const centerY = screen.top + screen.height / 2;

  console.log(`显示器 ${screen.id} 的中心点(全局坐标): (${centerX}, ${centerY})`);
  console.log(`当前窗口位置: (${window.screenX}, ${window.screenY}), 尺寸: ${window.innerWidth}x${window.innerHeight}`);

  // 关键修改：直接使用全局坐标，而不是相对于窗口的坐标
  // 这样对话框将直接定位到指定显示器的中心，而不受当前窗口位置的影响
  // 注意：这要求对话框使用 position: fixed 并且坐标系统是相对于整个屏幕的
  return {
    x: centerX,
    y: centerY,
  };
}

/**
 * 使用组合式API的钩子，用于在组件中监听显示器变化
 * @param options 配置选项
 * @param options.addMockScreens 是否在只有一个显示器时添加模拟显示器（默认：true）
 */
export function useScreenDetection(options: { addMockScreens?: boolean } = {}) {
  // 设置默认选项
  const { addMockScreens = true } = options;
  // 加载状态
  const loading = ref(screens.value.length === 0);
  // 权限请求状态
  const permissionRequested = ref(false);

  // 异步更新屏幕信息（不请求高级权限）
  const updateScreens = async () => {
    if (screens.value.length === 0) {
      loading.value = true;
    }

    try {
      // 触发异步初始化（只使用基本API）
      await initScreenDetection();
    } catch (error) {
      console.error('初始化屏幕检测失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 请求屏幕详情权限（必须在用户交互事件中调用）
  const requestPermission = async () => {
    loading.value = true;
    permissionRequested.value = true;

    try {
      console.log('请求屏幕权限前的显示器:', screens.value);
      const success = await requestScreenPermission();

      if (!success) {
        console.log('权限请求失败，回退到基本方法');
        // 如果权限请求失败，回退到基本方法
        await initScreenDetection();
      }

      // 如果启用了模拟显示器选项，并且检测到的显示器数量不足2个，则添加模拟显示器
      if (addMockScreens && screens.value.length < 2) {
        console.log('检测到的显示器数量不足，添加模拟显示器');

        // 清空现有显示器列表，确保ID一致性
        const existingScreens = [...screens.value];
        screens.value = [];

        // 添加主显示器（保留原有的如果存在）
        const primaryScreen = existingScreens.find((s) => s.isPrimary) || {
          id: 'primary',
          left: 0,
          top: 0,
          width: window.screen.width || 1920,
          height: window.screen.height || 1080,
          isPrimary: true,
          label: '主显示器',
        };

        // 确保ID和标签一致
        primaryScreen.id = 'primary';
        primaryScreen.label = '主显示器';
        primaryScreen.isPrimary = true;

        screens.value.push(primaryScreen);

        // 只有在启用模拟显示器选项时才添加扩展显示器
        if (addMockScreens) {
          // 添加扩展显示器（保留原有的如果存在）
          const extendedScreen = existingScreens.find((s) => !s.isPrimary) || {
            id: 'extended',
            left: window.screen.width || 1920,
            top: 0,
            width: 1920,
            height: 1080,
            isPrimary: false,
            label: '扩展显示器',
          };

          // 确保ID和标签一致
          extendedScreen.id = 'extended';
          extendedScreen.label = '扩展显示器';
          extendedScreen.isPrimary = false;

          screens.value.push(extendedScreen);
        }
      }

      console.log('权限请求后的显示器:', screens.value);
    } catch (error) {
      console.error('请求屏幕权限失败:', error);
      // 回退到基本方法
      await initScreenDetection();

      // 如果启用了模拟显示器选项，并且检测到的显示器数量不足2个，则添加模拟显示器
      if (addMockScreens && screens.value.length < 2) {
        console.log('错误处理：检测到的显示器数量不足，添加模拟显示器');

        // 清空现有显示器列表，确保ID一致性
        const existingScreens = [...screens.value];
        screens.value = [];

        // 添加主显示器（保留原有的如果存在）
        const primaryScreen = existingScreens.find((s) => s.isPrimary) || {
          id: 'primary',
          left: 0,
          top: 0,
          width: window.screen.width || 1920,
          height: window.screen.height || 1080,
          isPrimary: true,
          label: '主显示器',
        };

        // 确保ID和标签一致
        primaryScreen.id = 'primary';
        primaryScreen.label = '主显示器';
        primaryScreen.isPrimary = true;

        screens.value.push(primaryScreen);

        // 只有在启用模拟显示器选项时才添加扩展显示器
        if (addMockScreens) {
          // 添加扩展显示器（保留原有的如果存在）
          const extendedScreen = existingScreens.find((s) => !s.isPrimary) || {
            id: 'extended',
            left: window.screen.width || 1920,
            top: 0,
            width: 1920,
            height: 1080,
            isPrimary: false,
            label: '扩展显示器',
          };

          // 确保ID和标签一致
          extendedScreen.id = 'extended';
          extendedScreen.label = '扩展显示器';
          extendedScreen.isPrimary = false;

          screens.value.push(extendedScreen);
        }
      }
    } finally {
      loading.value = false;
    }
  };

  // 同步更新（使用已缓存的值）
  const refreshScreens = () => {
    // 如果已经有缓存的值，直接使用
    if (screens.value.length > 0) {
      return;
    }

    // 否则触发异步更新（只使用基本API）
    updateScreens();
  };

  // 尝试打开一个新窗口在指定显示器上
  const openWindowOnScreen = (screenId: string, url: string, windowName: string = '_blank') => {
    const allScreens = screens.value.length > 0 ? screens.value : detectScreens();
    const screen = allScreens.find((s) => s.id === screenId) || allScreens.find((s) => s.isPrimary === true) || allScreens[0];

    // 计算窗口位置
    const left = screen.left + 50; // 稍微偏移一点，不要完全在边缘
    const top = screen.top + 50;

    // 窗口尺寸（不要太大）
    const width = Math.min(800, screen.width - 100);
    const height = Math.min(600, screen.height - 100);

    // 打开窗口
    try {
      const newWindow = window.open(url, windowName, `width=${width},height=${height},left=${left},top=${top}`);

      return newWindow;
    } catch (error) {
      console.error('无法在指定显示器上打开窗口:', error);
      return null;
    }
  };

  onMounted(() => {
    // 初始检测
    updateScreens();

    // 监听可能导致显示器变化的事件
    window.addEventListener('resize', refreshScreens);
    if ('screen' in window && 'orientation' in window.screen) {
      (window.screen as any).orientation?.addEventListener('change', refreshScreens);
    }

    // 如果支持Window Management API，监听屏幕变化事件
    if ('getScreenDetails' in window) {
      // 注意：这个API可能需要用户权限
      (window as any).screenDetails?.addEventListener('screenschange', updateScreens);
    }
  });

  onUnmounted(() => {
    window.removeEventListener('resize', refreshScreens);
    if ('screen' in window && 'orientation' in window.screen) {
      (window.screen as any).orientation?.removeEventListener('change', refreshScreens);
    }

    if ('getScreenDetails' in window) {
      (window as any).screenDetails?.removeEventListener('screenschange', updateScreens);
    }
  });

  return {
    screens,
    loading,
    permissionRequested,
    updateScreens,
    requestPermission,
    openWindowOnScreen,
  };
}
