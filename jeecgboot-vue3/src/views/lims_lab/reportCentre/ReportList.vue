<template>
  <!-- 文档查看器模式 -->
  <div v-if="isDocumentViewerMode" class="document-window">
    <DocumentViewer
      :documentServerUrl="documentServerUrl"
      :documentUrl="documentUrl"
      :documentTitle="documentTitle"
      :documentType="documentType"
      :screenId="activeScreenId"
      :editorConfig="editorConfig"
      :floatButtons="[
        {
          tooltip: '发送邮件',
          icon: 'MailOutlined',
          type: 'primary',
          action: 'email',
        },
        {
          tooltip: '签发',
          icon: 'EditTwoTone',
          type: 'primary',
          action: 'sign',
        },
        {
          tooltip: '审批',
          icon: 'CheckOutlined',
          type: 'primary',
          action: 'approve',
        },
      ]"
    />
  </div>
  <div v-else>
    <!-- 模拟浏览器地址栏的权限请求 -->
    <transition name="slide-down">
      <div class="browser-bar" v-if="showPermissionUI && !permissionRequested && isWindowManagementSupported">
        <div class="address-bar">
          <div class="site-info">
            <span class="secure-icon">🔒</span>
            <span class="url">{{ currentUrl }}</span>
          </div>
          <div class="permission-prompt">
            <span class="permission-icon">🖥️</span>
            <span class="permission-text">此网站请求访问您的显示器信息</span>
            <button @click="requestScreenPermission" class="permission-allow">允许</button>
            <button @click="denyPermission" class="permission-deny">拒绝</button>
          </div>
        </div>
      </div>
    </transition>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" :expandedRowKeys="expandedRowKeys" @expand="handleExpand">
      <!-- 内嵌table区域 begin -->
      <template #expandedRowRender="{ record }">
        <a-tabs tabPosition="top" v-model:activeKey="record.activeTabKey" animated>
          <a-tab-pane tab="测试项目结果" key="TestResultTable" :forceRender="true">
            <TestResultSubTable :id="record.id" :key="record.id" />`
          </a-tab-pane>
          <a-tab-pane tab="进度查询" key="progress" :forceRender="true">
            <TestProgressList v-if="record.activeTabKey === 'progress'" :sampleId="record.sampleId" />
          </a-tab-pane>
        </a-tabs>
      </template>
      <!-- 内嵌table区域 end -->
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'lims_lab:report:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" v-auth="'lims_lab:report:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>

        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="handleBatchGenerate1" :disabled="batchGenerating">
                <Icon icon="ant-design:container-outlined" />
                <span>生成报告（单窗口）</span>
                <a-spin v-if="batchGenerating && batchGenerateType === 'single'" size="small" style="margin-left: 8px" />
              </a-menu-item>
              <a-menu-item key="2" @click="handleBatchGenerate" :disabled="batchGenerating">
                <Icon icon="ant-design:container-outlined" />
                <span>生成报告（多窗口）</span>
                <a-spin v-if="batchGenerating && batchGenerateType === 'multi'" size="small" style="margin-left: 8px" />
              </a-menu-item>
              <a-menu-item key="3" @click="handleBatchDownload" :disabled="batchGenerating">
                <Icon icon="ant-design:container-outlined" />
                <span>批量下载报告</span>
                <a-spin v-if="batchGenerating && batchGenerateType === 'multi'" size="small" style="margin-left: 8px" />
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
        <div class="multi-screen-demo">
          <div class="screen-info">
            <div class="screen-title">检测到的显示器: <span v-if="loading" class="loading-indicator">检测中...</span></div>
            <div v-if="screens.length === 0 && loading" class="screen-status">正在检测显示器...</div>
            <div v-else-if="screens.length === 0" class="screen-status">未检测到显示器</div>

            <div v-if="screens.length > 0" class="screen-list">
              <div
                v-for="screen in screens"
                :key="screen.id"
                class="screen-item"
                :class="{ active: screen.id === activeScreenId, primary: screen.isPrimary }"
                @click="setActiveScreen(screen.id)"
              >
                <div class="screen-preview" :style="getScreenStyle(screen)">
                  <div class="screen-id">{{ screen.label || screen.id }}</div>
                  <span v-if="screen.isPrimary" class="primary-indicator">主</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex === 'url'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <div v-else>
            <a-button
              :ghost="true"
              type="primary"
              preIcon="ant-design:download-outlined"
              size="small"
              @click="downloadFile(text)"
              style="margin-right: 8px"
              >下载</a-button
            >
            <a-button :ghost="true" type="primary" preIcon="ant-design:read-outlined" size="small" @click="handleRead(record)">阅读</a-button>
          </div>
        </template>
        <template v-if="column.dataIndex === 'finalUrl'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px; font-style: italic">无文件</span>
          <div v-else>
            <a-button
              :ghost="true"
              type="primary"
              preIcon="ant-design:download-outlined"
              size="small"
              @click="downloadFile(text)"
              style="margin-right: 8px"
              >下载</a-button
            >
            <a-button :ghost="true" type="primary" preIcon="ant-design:read-outlined" size="small" @click="handleReadPdf(record)">阅读</a-button>
          </div>
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <ReportModal @register="registerModal" @success="handleSuccess" />
    <TabDocEditor
      @register="registerModalDocEditor"
      @button-click="handleDocEditorButtonClick"
      :floatButtons="[
        {
          tooltip: '发送邮件',
          icon: 'MailOutlined',
          type: 'primary',
          action: 'email',
        },
        {
          tooltip: '签发',
          icon: 'EditTwoTone',
          type: 'primary',
          action: 'sign',
        },
        {
          tooltip: '审批',
          icon: 'CheckOutlined',
          type: 'primary',
          action: 'approve',
        },
      ]"
    />
    <SampleModal @register="regsampleModal" @success="handleSuccess" />
    <TestTaskTableModal @register="regTestTaskTableModal" @success="handleSuccess" />
  </div>
</template>

<script lang="tsx" name="lims_lab-report" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { useScreenDetection } from '@/utils/screenUtils';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMessage } from '/@/hooks/web/useMessage';
  import ReportModal from './components/ReportModal.vue';
  import DocumentViewer from '@/components/oo/DocumentViewer.vue';
  import TabDocEditor from '@/views/dcs/components/TabDocEditor.vue';
  import { columns, searchFormSchema, superQuerySchema } from './Report.data';
  import { list, getExportUrl, generate, batchGenerate, getOOEditorConfig, sign } from './Report.api';
  import { getOnlyOfficeGlobalConfig } from '@/utils/onlyOfficeGlobalConfig';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import TestResultSubTable from './subtable/TestResultSubTable.vue';
  import TestProgressList from './components/TestProgressList.vue';
  import SampleModal from '@/views/lims_order/sample/components/SampleModal.vue';
  import { list as listSample } from '@/views/lims_order/sample/Sample.api';
  import { message } from 'ant-design-vue';
  import { useDrawer } from '@/components/Drawer';
  import TestTaskTableModal from '@/views/lims_order/TestTaskTableModal.vue';

  const [regTestTaskTableModal, { openModal: openTestTaskTableModal }] = useModal();
  const isWindowManagementSupported = ref(false);
  const expandedRowKeys = ref<any[]>([]);
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const { createMessage } = useMessage();
  const documentServerUrl = ref(getOnlyOfficeGlobalConfig().onlyOffice.documentServerUrl);

  // 定义类型
  type GenerateType = 'single' | 'multi' | 'normal';

  // 批量生成报告的状态
  const batchGenerating = ref(false);
  const batchGenerateType = ref<GenerateType>('normal');
  const currentGeneratingId = ref<string | number>('');

  // 使用屏幕检测钩子，设置不添加模拟显示器
  const { screens, loading, permissionRequested, requestPermission } = useScreenDetection({ addMockScreens: false });
  // 检查URL参数，判断是否是文档查看器模式
  const isDocumentViewerMode = ref(false);
  const documentTitle = ref('');
  const documentUrl = ref('');
  const documentType = ref('');
  const editorConfig = ref<Record<string, any> | undefined>(undefined);
  const activeScreenId = ref('primary');

  // 控制权限请求UI的显示
  const showPermissionUI = ref(false);

  // 获取当前URL用于地址栏显示
  const currentUrl = ref(window.location.host + window.location.pathname);
  const [regsampleModal, { openDrawer: openSampleModal }] = useDrawer();

  columns.forEach((item) => {
    if (item.dataIndex == 'sampleId_dictText') {
      item.customRender = ({ record }) => {
        return (
          <a-button type="link" onClick={() => handleSampleNoClick(record)}>
            {record.sampleId_dictText}
          </a-button>
        );
      };
    }
  });

  function handleSampleNoClick(record) {
    listSample({ id: record.sampleId }).then((res) => {
      console.log('res', res);
      if (res.records) {
        openSampleModal(true, {
          record: res.records[0],
          isUpdate: true,
          hideFooter: true,
        });
      } else {
        message.error(res.msg);
      }
    });
  }
  /**
   * 展开事件
   * */
  function handleExpand(expanded, record) {
    if (expanded === true) {
      expandedRowKeys.value = [record.id]; // 只允许单行展开
    } else {
      expandedRowKeys.value = [];
    }
  }

  // 请求屏幕权限的方法（在用户点击允许按钮时调用）
  const requestScreenPermission = async () => {
    try {
      console.log('用户请求屏幕权限');
      await requestPermission();
      console.log('屏幕权限请求完成，检测到的显示器:', screens.value);
    } catch (error) {
      console.error('请求屏幕权限时出错:', error);
      alert('无法获取屏幕权限，将使用基本显示器信息');
    }
  };

  // 拒绝权限的方法（在用户点击拒绝按钮时调用）
  const denyPermission = () => {
    console.log('用户拒绝屏幕权限');
    // 标记为已请求，这样权限提示就不会再显示
    permissionRequested.value = true;

    // 确保至少有主显示器
    if (screens.value.length === 0) {
      console.log('用户拒绝权限，添加主显示器');

      // 添加模拟的主显示器
      screens.value.push({
        id: 'primary',
        left: 0,
        top: 0,
        width: window.screen.width || 1920,
        height: window.screen.height || 1080,
        isPrimary: true,
        label: '主显示器',
      });

      // 设置活动显示器为主显示器
      activeScreenId.value = 'primary';
    }
  };

  const setActiveScreen = (screenId: string) => {
    activeScreenId.value = screenId;
  };

  const getScreenStyle = (screen: any) => {
    // 根据屏幕尺寸和位置创建一个缩略图样式
    // 使用更小的缩放比例，确保扩展显示器不会超出容器
    const scale = 20; // 更大的缩放因子，使预览更小

    // 计算宽度和高度，确保最小尺寸
    const width = Math.max(50, screen.width / scale);
    const height = Math.max(30, screen.height / scale);

    return {
      width: `${width}px`,
      height: `${height}px`,
      margin: '0 auto', // 居中显示
    };
  };

  // 保存打开的窗口引用
  const documentWindows = reactive(new Map());

  // 修改打开文档窗口的方法
  const openDocumentOnActiveScreen = async (bizId: string) => {
    const ooConfig = await getOOEditorConfig(bizId);
    ooConfig.editorConfig.callbackUrl = getOnlyOfficeGlobalConfig().onlyOffice.editorConfig.callbackUrl;
    console.log('ooConfig', ooConfig);

    // 确保我们有显示器信息
    if (screens.value.length === 0) {
      alert('未检测到任何显示器，请先点击"允许"按钮授予显示器访问权限');
      return;
    }

    // 如果只有一个显示器，自动选择它
    if (screens.value.length === 1) {
      activeScreenId.value = screens.value[0].id;
    }

    // 确保activeScreenId不是undefined
    const screenId = activeScreenId.value || screens.value.find((s) => s.isPrimary)?.id || 'primary';

    // 获取目标显示器信息
    const targetScreen = screens.value.find((s) => s.id === screenId);
    if (!targetScreen) {
      alert(`找不到ID为 ${screenId} 的显示器，请先点击"允许"按钮授予显示器访问权限。`);
      return;
    }

    // 构建文档预览URL
    const documentViewerUrl = `/document-preview?id=${bizId}&screen=${screenId}&source=report`;
    console.log('打开文档URL:', documentViewerUrl);

    // 计算窗口位置
    const width = 800;
    const height = 600;
    const screenLeft = typeof targetScreen.left === 'number' ? targetScreen.left : 0;
    const screenTop = typeof targetScreen.top === 'number' ? targetScreen.top : 0;
    const screenWidth = typeof targetScreen.width === 'number' ? targetScreen.width : window.screen.width;
    const screenHeight = typeof targetScreen.height === 'number' ? targetScreen.height : window.screen.height;
    const left = screenLeft + Math.floor(screenWidth / 2) - Math.floor(width / 2);
    const top = screenTop + Math.floor(screenHeight / 2) - Math.floor(height / 2);

    try {
      const fullUrl = `${window.location.origin}${documentViewerUrl}`;
      const windowName = `doc_${bizId}_${Date.now()}`;
      const features = `width=${width},height=${height},left=${left},top=${top},menubar=no,toolbar=no,location=no,status=no,resizable=yes`;

      const docWindow = window.open(fullUrl, windowName, features);

      if (!docWindow) {
        alert('无法打开文档窗口，请检查浏览器是否阻止弹出窗口');
        return;
      }

      // 保存窗口引用到 Map 中
      documentWindows.set(windowName, {
        window: docWindow,
        bizId: bizId,
        config: ooConfig,
      });

      // 监听窗口关闭事件
      const checkClosed = setInterval(() => {
        if (docWindow.closed) {
          clearInterval(checkClosed);
          documentWindows.delete(windowName);
        }
      }, 500);
    } catch (error: any) {
      alert(`无法在显示器 ${screenId} 上打开文档窗口: ${error.message || '未知错误'}`);
    }
  };
  // 向所有打开的文档窗口广播更新消息
  const broadcastUpdateToWindows = (configs: any[]) => {
    if (!configs || configs.length === 0) return;

    for (const [windowName, windowInfo] of documentWindows.entries()) {
      if (!windowInfo.window.closed) {
        const config = configs.find((c) => c.document?.key === windowInfo.bizId);
        if (config) {
          try {
            // 将日志广播到所有子窗口
            broadcastLog('log', '准备向文档窗口推送更新配置', config);
            windowInfo.window.postMessage(
              {
                type: 'updateConfig',
                data: config,
              },
              '*'
            );
          } catch (error) {
            // 将错误日志广播到所有窗口
            broadcastLog('error', '向文档窗口推送更新配置时出错', error);
            console.error(`向窗口 ${windowName} 发送更新消息失败:`, error);
          }
        }
      }
    }
  };

  // 注册model
  const [registerModal, { openModal }] = useModal();
  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls } = useListPage({
    tableProps: {
      title: '报告',
      api: list,
      columns: [
        {
          title: '操作',
          dataIndex: 'action',
          resizable: true,
          fixed: 'left',
          width: 230,
          slots: { customRender: 'action' },
        },
        ...columns,
      ],
      canResize: false,
      clickToRowSelect: true,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [['deadLine', ['deadLine_begin', 'deadLine_end'], 'YYYY-MM-DD']],
      },
      scroll: {
        y: 'calc(100vh - 380px)',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '报告',
      url: getExportUrl,
      params: queryParam,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 生成报告事件
   */
  async function handleGenerate(record: Recordable) {
    try {
      // 设置生成状态
      batchGenerating.value = true;
      batchGenerateType.value = 'normal';
      currentGeneratingId.value = record.id;

      // 显示消息提示
      createMessage.loading({
        content: '正在生成报告，请稍候...',
        duration: 0,
        key: 'generateReport',
      });

      // 调用生成API
      await generate({ id: record.id }, () => {});

      // 更新消息
      createMessage.success({
        content: '报告生成成功，正在打开预览...',
        duration: 2,
        key: 'generateReport',
      });

      // 打开文档预览
      await openDocumentOnActiveScreen(record.id);

      // 完成后刷新列表
      handleSuccess();
    } catch (error: any) {
      createMessage.error({
        content: `报告生成失败: ${error?.message || '未知错误'}`,
        duration: 3,
        key: 'generateReport',
      });
      console.error('生成报告失败:', error);
    } finally {
      // 重置状态
      batchGenerating.value = false;
      currentGeneratingId.value = '';
    }
  }

  /**
   * 阅读报告事件
   */
  async function handleRead(record: Recordable) {
    try {
      // 显示消息提示
      createMessage.loading({
        content: '正在打开报告，请稍候...',
        duration: 0,
        key: 'readReport',
      });

      // 打开文档预览
      await openDocumentOnActiveScreen(record.id);

      // 更新消息
      createMessage.success({
        content: '报告打开成功',
        duration: 2,
        key: 'readReport',
      });
    } catch (error: any) {
      createMessage.error({
        content: `打开报告失败: ${error?.message || '未知错误'}`,
        duration: 3,
        key: 'readReport',
      });
      console.error('打开报告失败:', error);
    }
  }

  /**
   * 批量生成报告事件（多窗口）
   */
  async function handleBatchGenerate() {
    const ids = selectedRowKeys.value;
    if (ids.length === 0) {
      return;
    }

    try {
      // 设置生成状态
      batchGenerating.value = true;
      batchGenerateType.value = 'multi';

      // 显示消息提示
      createMessage.loading({
        content: `正在批量生成报告，请稍候...`,
        duration: 0,
        key: 'batchGenerateReport',
      });

      // 先批量生成报告
      await batchGenerate({ ids: ids });

      // 更新消息
      createMessage.success({
        content: '报告生成成功，正在打开预览窗口...',
        duration: 2,
        key: 'batchGenerateReport',
      });

      // 逐个打开文档预览
      for (const id of ids) {
        // 更新当前处理的ID
        currentGeneratingId.value = id;

        // 打开文档预览
        await openDocumentOnActiveScreen(id);
      }

      // 完成后刷新列表
      handleSuccess();
    } catch (error: any) {
      createMessage.error({
        content: `批量生成报告失败: ${error?.message || '未知错误'}`,
        duration: 3,
        key: 'batchGenerateReport',
      });
      console.error('批量生成报告失败:', error);
    } finally {
      // 重置状态
      batchGenerating.value = false;
      currentGeneratingId.value = '';
    }
  }

  /**
   * 批量生成报告事件（单窗口）
   * 使用TabDocEditor组件在一个窗口中显示多个标签页
   */
  async function handleBatchGenerate1() {
    const ids = selectedRowKeys.value;
    if (ids.length === 0) {
      return;
    }

    try {
      // 设置生成状态
      batchGenerating.value = true;
      batchGenerateType.value = 'single';

      // 显示消息提示
      createMessage.loading({
        content: `正在批量生成报告 (0/${ids.length})，请稍候...`,
        duration: 0,
        key: 'batchGenerateTabReport',
      });

      // 先生成报告
      await batchGenerate({ ids: ids });

      // 更新消息
      createMessage.loading({
        content: '报告生成成功，正在准备预览...',
        duration: 0,
        key: 'batchGenerateTabReport',
      });

      // 收集所有文档配置
      const configs: any[] = [];
      for (let i = 0; i < ids.length; i++) {
        const id = ids[i];

        // 更新当前处理的ID
        currentGeneratingId.value = id;

        // 更新消息
        createMessage.loading({
          content: `正在准备预览 (${i + 1}/${ids.length})...`,
          duration: 0,
          key: 'batchGenerateTabReport',
        });

        try {
          const ooConfig = await getOOEditorConfig(id);
          ooConfig.editorConfig.callbackUrl = getOnlyOfficeGlobalConfig().onlyOffice.editorConfig.callbackUrl;
          configs.push(ooConfig);
        } catch (error) {
          console.error(`获取文档配置失败，ID: ${id}`, error);
        }
      }

      // 如果有配置，打开TabDocEditor
      if (configs.length > 0) {
        // 更新消息
        createMessage.success({
          content: `已准备 ${configs.length} 个报告，正在打开预览...`,
          duration: 2,
          key: 'batchGenerateTabReport',
        });

        // 使用TabDocEditor组件，传递configs参数
        openDocEditorModal(true, {
          configs: configs,
          title: '批量报告审批',
        });
      } else {
        createMessage.warning({
          content: '没有可预览的报告',
          duration: 3,
          key: 'batchGenerateTabReport',
        });
      }

      // 完成后刷新列表
      handleSuccess();
    } catch (error: any) {
      createMessage.error({
        content: `批量生成报告失败: ${error?.message || '未知错误'}`,
        duration: 3,
        key: 'batchGenerateTabReport',
      });
      console.error('批量生成报告失败:', error);
    } finally {
      // 重置状态
      batchGenerating.value = false;
      currentGeneratingId.value = '';
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record: Recordable) {
    const actions = [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_lab:report:edit',
      },
      {
        label: '生成报告',
        onClick: handleGenerate.bind(null, record),
        auth: 'lims_lab:report:generate',
        loading: batchGenerating.value && batchGenerateType.value === 'normal' && record.id === currentGeneratingId.value,
      },
      {
        label: '节点退回',
        onClick: handleRevert.bind(null, record),
      },
    ];

    // 调试日志，查看url字段的值
    console.log('报告记录:', record.reportNo, '电子报告URL:', record.url, '类型:', typeof record.url);

    // 只有当电子报告有文件时才显示阅读按钮
    // 使用最简单的判断逻辑：如果url字段不是假值（null、undefined、空字符串、0、NaN、false），则显示阅读按钮
    if (record.url !== null && record.url !== undefined && record.url !== '') {
      actions.push({
        label: '阅读',
        onClick: handleRead.bind(null, record),
        auth: 'lims_lab:report:read',
      });
    }

    return actions;
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record: Recordable) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  // 定义按钮点击事件类型
  interface ButtonClickEvent {
    action: string;
    documentId?: string;
    editorInstance?: any;
    [key: string]: any;
  }

  // 处理TabDocEditor按钮点击事件
  const handleDocEditorButtonClick = (event: ButtonClickEvent) => {
    console.log('文档编辑器按钮点击:', event);

    // 根据按钮动作执行不同操作
    if (event.action === 'close') {
      // 关闭编辑器 - 传递false关闭模态框
      openDocEditorModal(false);
      // 重置选中行，刷新表格
      handleSuccess();
    } else if (event.action === 'email') {
      // 发送邮件逻辑
      console.log('发送邮件:', event.documentId);
    } else if (event.action === 'approve') {
      // 审批逻辑
      console.log('审批文档 开始处理:', event.allKeys);
      handleApprove(event.allKeys || [], 'approve').catch((err) => {
        console.error('审批处理失败:', err);
      });
    } else if (event.action === 'sign') {
      // 签发逻辑
      console.log('签发文档 开始处理:', event.allKeys);
      handleApprove(event.allKeys || [], 'sign').catch((err) => {
        console.error('签发处理失败:', err);
      });
    }
  };
  // 日志工具函数
  const broadcastLog = (type: 'log' | 'error' | 'info', message: string, data?: any) => {
    // 确保数据是可序列化的
    const safeData = data ? JSON.parse(JSON.stringify(data)) : undefined;

    const logData = {
      type,
      message,
      data: safeData,
      timestamp: new Date().toISOString(),
      source: window.name || 'main',
    };

    // 在当前窗口显示日志
    console[type](message, data);

    try {
      // 广播日志到其他窗口
      if (window.opener) {
        // 如果是子窗口，发送到父窗口
        window.opener.postMessage({ type: 'LOG_MESSAGE', payload: logData }, '*');
      } else {
        // 如果是父窗口，发送到所有子窗口
        const childWindows = (window as any).__docPreviewWindows || [];
        childWindows.forEach((win) => {
          if (!win.closed) {
            win.postMessage({ type: 'LOG_MESSAGE', payload: logData }, '*');
          }
        });
      }
    } catch (error) {
      console.error('发送日志消息失败:', error);
    }
  };

  // 日志处理器
  onMounted(() => {
    // 存储文档预览窗口引用
    (window as any).__docPreviewWindows = (window as any).__docPreviewWindows || [];

    // 监听日志消息
    window.addEventListener('message', (event) => {
      if (event.data.type === 'LOG_MESSAGE') {
        const { type, message, data, source } = event.data.payload;
        console[type](`[${source}] ${message}`, data);
      }
    });
  });

  // 改进的 handleApprove 函数
  const handleApprove = async (reportIds: string[], status: string) => {
    broadcastLog('log', 'handleApprove 开始执行', { reportIds, status });
    try {
      createMessage.loading({ content: '正在处理...', key: 'approve' });
      broadcastLog('log', '调用签发/审批API开始');
      const res = await sign({ ids: reportIds, status: status });
      broadcastLog('log', '签发/审批API返回结果', res);

      if (res.success) {
        createMessage.success({ content: '处理成功', key: 'approve' });
        reload();
      } else {
        createMessage.error({ content: res.message || '处理失败', key: 'approve' });
        broadcastLog('error', '处理失败', res.message);
      }
    } catch (error) {
      createMessage.error({ content: '处理出错', key: 'approve' });
      broadcastLog('error', '处理出错', error);
    }
  };

  // 改进的预览窗口打开函数
  const openPreviewWindow = (url: string, title: string) => {
    const previewWindow = window.open(url, title, 'width=1200,height=800');
    if (previewWindow) {
      // 存储预览窗口引用
      const docPreviewWindows = (window as any).__docPreviewWindows || [];
      docPreviewWindows.push(previewWindow);
      (window as any).__docPreviewWindows = docPreviewWindows;

      // 当子窗口关闭时清理引用
      previewWindow.onbeforeunload = () => {
        const index = docPreviewWindows.indexOf(previewWindow);
        if (index > -1) {
          docPreviewWindows.splice(index, 1);
        }
      };
    }
    return previewWindow;
  };

  function handleReadPdf(record: Recordable) {
    let text = record.finalUrl;
    window.open('/pdfjs/web/viewer.html?file='+encodeURIComponent(text), '', `width=800,height=600,left=0,top=0,menubar=no,toolbar=no,location=no,status=no,resizable=yes`);

    // const documentViewerUrl = `/document-preview?id=${record.id}&source=signreport`;
    // window.open(documentViewerUrl, '', `width=800,height=600,left=0,top=0,menubar=no,toolbar=no,location=no,status=no,resizable=yes`);

  }

  // 添加消息监听器
  onMounted(() => {
    // 检查并设置权限UI显示状态
    showPermissionUI.value = !permissionRequested.value && isWindowManagementSupported.value;

    // 添加消息监听
    window.addEventListener('message', (event) => {
      console.log('收到消息:', event.data);

      // 处理配置更新消息
      if (event.data && event.data.type === 'updateConfig') {
        const newConfig = event.data.data;
        console.log('收到配置更新:', newConfig);

        // 如果是文档查看器模式，更新配置
        if (isDocumentViewerMode.value) {
          // 更新文档配置
          editorConfig.value = newConfig;

          // 更新其他相关属性
          if (newConfig.document) {
            documentUrl.value = newConfig.document.url;
            documentTitle.value = newConfig.document.title;
          }

          // 通知用户配置已更新
          createMessage.success({
            content: '文档已更新',
            duration: 2,
          });
        } else if (window === window.parent) {
          // 如果是主窗口，则广播到所有打开的窗口
          broadcastUpdateToWindows([newConfig]);
        }
      }
    });

    // 清理函数
    return () => {
      window.removeEventListener('message', () => {
        console.log('移除消息监听器');
      });
    };
  });

  function handleRevert(record){
    openTestTaskTableModal(true, {
      sampleId: record.sampleId,
      disabled: true,
      isRevert: true,
    });

  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
  .multi-screen-demo {
    display: inline-block;
    margin-left: 16px;
    vertical-align: middle;
    /* 移除宽度限制，让内容自然展开 */
  }

  .screen-info {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex-wrap: nowrap;
    width: auto; /* 自适应宽度 */
    min-width: 400px; /* 最小宽度 */
  }

  .screen-title-container {
    width: 150px;
    flex-shrink: 0;
  }

  .screen-content {
    flex: 1;
  }
  /* 文档窗口样式 */
  .document-window {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-color: #f5f5f5;
  }
  /* 过渡动画 */
  .slide-down-enter-active,
  .slide-down-leave-active {
    transition: all 0.3s ease;
  }
  .slide-down-enter-from {
    transform: translateY(-100%);
    opacity: 0;
  }
  .slide-down-leave-to {
    transform: translateY(-100%);
    opacity: 0;
  }
  /* 模拟浏览器地址栏样式 */
  .browser-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
    z-index: 9999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }
  .address-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px;
    background-color: #fff;
    border-radius: 4px;
    margin: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  .site-info {
    display: flex;
    align-items: center;
    color: #333;
  }
  .secure-icon {
    margin-right: 8px;
    color: #0f9d58;
  }
  .url {
    font-size: 14px;
    color: #333;
  }
  .permission-prompt {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 6px 12px;
    border-radius: 4px;
    border-left: 4px solid #4285f4;
    margin-left: 16px;
  }
  .permission-icon {
    margin-right: 8px;
  }
  .permission-text {
    font-size: 13px;
    color: #333;
    margin-right: 16px;
  }
  .permission-allow,
  .permission-deny {
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    margin-left: 8px;
    transition: all 0.2s;
  }
  .permission-allow {
    background-color: #4285f4;
    color: white;
  }
  .permission-allow:hover {
    background-color: #3b78e7;
  }
  .permission-deny {
    background-color: #f1f3f4;
    color: #5f6368;
  }
  .permission-deny:hover {
    background-color: #e8eaed;
  }
  .loading-indicator {
    display: inline-block;
    font-size: 0.8em;
    color: #666;
    background-color: #f5f5f5;
    padding: 2px 8px;
    border-radius: 10px;
    margin-left: 5px;
    animation: pulse 1.5s infinite;
    vertical-align: middle;
  }
  /* 屏幕信息头部样式 */
  .screen-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }

  .screen-title {
    font-weight: bold;
    font-size: 14px;
    margin-right: 5px;
    white-space: nowrap;
    margin-bottom: 0;
    display: inline-flex;
    align-items: center;
    height: 32px; /* 与按钮高度一致 */
    flex-shrink: 0;
  }

  .screen-status {
    color: #666;
    font-size: 14px;
    height: 32px; /* 与按钮高度一致 */
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    flex-shrink: 0;
  }

  .screen-list {
    display: flex;
    flex-wrap: nowrap;
    gap: 0; /* 移除间隙，因为我们已经在 screen-item 中添加了右边距 */
    margin-top: 0;
    margin-left: 15px;
    align-items: center;
    flex: 1;
    /* 添加内边距，为按钮预留空间 */
    padding: 2px;
  }
  .screen-item {
    border: 1px solid #ddd;
    padding: 3px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
    width: 120px; /* 适当的宽度 */
    height: 32px; /* 与按钮高度一致 */
    box-sizing: border-box; /* 确保边框和内边距包含在宽度内 */
    display: flex;
    flex-direction: column;
    align-items: center; /* 居中显示内容 */
    justify-content: center;
    flex-shrink: 0;
    margin-right: 10px; /* 适当的右侧间距 */
    overflow: hidden; /* 确保内容不超出 */
  }
  .screen-item:hover {
    border-color: #42b983;
    background-color: #f0f9f4;
    /* 移除向上移动效果，改用边框高亮 */
    border-width: 2px;
    box-shadow: 0 0 5px rgba(66, 185, 131, 0.5);
    /* 调整内边距，保持整体大小不变 */
    padding: 2px;
  }
  .screen-item.active {
    border-color: #42b983;
    background-color: #f0f9f4;
    border-width: 2px;
    padding: 2px;
    box-shadow: 0 0 5px rgba(66, 185, 131, 0.5);
  }
  .screen-item.primary {
    border-color: #4285f4;
  }

  .screen-item.primary:hover {
    border-color: #4285f4;
    background-color: #f0f4f9;
    border-width: 2px;
    padding: 2px;
    box-shadow: 0 0 5px rgba(66, 133, 244, 0.5);
  }
  .screen-item.primary.active {
    border-color: #4285f4;
    background-color: #f0f4f9;
    border-width: 2px;
    padding: 2px;
    box-shadow: 0 0 5px rgba(66, 133, 244, 0.5);
  }
  .screen-preview {
    position: relative;
    background-color: #e0e0e0;
    border: 1px solid #999;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0;
    width: calc(100% - 2px); /* 减去边框宽度 */
    height: calc(100% - 2px); /* 减去边框宽度 */
    overflow: hidden;
    box-sizing: border-box; /* 确保边框包含在宽高内 */
    border-radius: 3px; /* 添加圆角 */
  }
  .screen-id {
    font-weight: bold;
    font-size: 0.8em;
    color: #333;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 90%;
    padding: 0 2px;
    line-height: 1.2;
  }
  .primary-indicator {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #4285f4;
    color: white;
    font-size: 0.6em;
    padding: 1px 2px;
    line-height: 1;
    border-bottom-left-radius: 2px;
  }
</style>
