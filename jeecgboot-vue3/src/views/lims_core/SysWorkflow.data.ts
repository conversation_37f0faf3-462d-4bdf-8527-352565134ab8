import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
  },
  {
    title: '更新人',
    align: 'center',
    dataIndex: 'updateBy_dictText',
  },
  {
    title: '更新日期',
    align: 'center',
    dataIndex: 'updateTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表表格配置
export const sysWorkflowStepColumns: JVxeColumn[] = [
  {
    title: '顺序',
    key: 'sortNum',
    type: JVxeTypes.inputNumber,
    width: '80px',
    disabled: true,
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '环节',
    key: 'name',
    type: JVxeTypes.select,
    options:[],
    dictCode:"flow_step",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
    validateRules: [
      { required: true, message: '${title}不能为空' },
    ],
  },
  {
    title: '标准工时（%）',
    key: 'stdTaskTime',
    type: JVxeTypes.inputNumber,
    width: '150px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '阶段',
    key: 'stage',
    type: JVxeTypes.selectSearch,
    dictCode: 'testing_workflow_stage',
    width: '100px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '流入条件表达式',
    key: 'expr',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  createBy: { title: '创建人', order: 1, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 2, view: 'datetime', type: 'string' },
  updateBy: { title: '更新人', order: 3, view: 'text', type: 'string' },
  updateTime: { title: '更新日期', order: 4, view: 'datetime', type: 'string' },
  //子表高级查询
  sysWorkflowStep: {
    title: '流程环节',
    view: 'table',
    fields: {
      sortNum: { title: '顺序', order: 0, view: 'number', type: 'number' },
      name: { title: '环节', order: 1, view: 'text', type: 'string' },
      stdTaskTime: { title: '标准工时（%）', order: 2, view: 'number', type: 'number' },
      stage: { title: '阶段', order: 3, view: 'sel_search', type: 'string', dictCode: 'testing_workflow_stage' },
      expr: { title: '流入条件表达式', order: 4, view: 'text', type: 'string' },
      createBy: { title: '创建人', order: 5, view: 'text', type: 'string' },
      createTime: { title: '创建日期', order: 6, view: 'datetime', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
