# Apache 配置文件，用于解决 SPA 路由和 MIME 类型问题

# 启用重写引擎
RewriteEngine On

# 设置正确的 MIME 类型
<IfModule mod_mime.c>
  # JavaScript 文件
  AddType application/javascript .js
  AddType application/javascript .mjs
  AddType text/javascript .js
  
  # CSS 文件
  AddType text/css .css
  
  # 字体文件
  AddType font/woff .woff
  AddType font/woff2 .woff2
  AddType application/font-woff .woff
  AddType application/font-woff2 .woff2
  
  # 图片文件
  AddType image/svg+xml .svg
  AddType image/webp .webp
  
  # JSON 文件
  AddType application/json .json
  AddType application/manifest+json .webmanifest
</IfModule>

# 处理 SPA 路由 - 将所有请求重定向到 index.html（除了实际存在的文件）
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/js/
RewriteCond %{REQUEST_URI} !^/css/
RewriteCond %{REQUEST_URI} !^/img/
RewriteCond %{REQUEST_URI} !^/fonts/
RewriteCond %{REQUEST_URI} !^/media/
RewriteRule ^.*$ /index.html [L]

# 缓存控制
<IfModule mod_expires.c>
  ExpiresActive on
  
  # JavaScript 和 CSS 文件缓存 1 年
  ExpiresByType application/javascript "access plus 1 year"
  ExpiresByType text/javascript "access plus 1 year"
  ExpiresByType text/css "access plus 1 year"
  
  # 图片文件缓存 1 个月
  ExpiresByType image/png "access plus 1 month"
  ExpiresByType image/jpg "access plus 1 month"
  ExpiresByType image/jpeg "access plus 1 month"
  ExpiresByType image/gif "access plus 1 month"
  ExpiresByType image/svg+xml "access plus 1 month"
  ExpiresByType image/webp "access plus 1 month"
  
  # 字体文件缓存 1 年
  ExpiresByType font/woff "access plus 1 year"
  ExpiresByType font/woff2 "access plus 1 year"
  ExpiresByType application/font-woff "access plus 1 year"
  ExpiresByType application/font-woff2 "access plus 1 year"
  
  # HTML 文件不缓存
  ExpiresByType text/html "access plus 0 seconds"
</IfModule>

# 启用 Gzip 压缩
<IfModule mod_deflate.c>
  AddOutputFilterByType DEFLATE text/plain
  AddOutputFilterByType DEFLATE text/html
  AddOutputFilterByType DEFLATE text/xml
  AddOutputFilterByType DEFLATE text/css
  AddOutputFilterByType DEFLATE application/xml
  AddOutputFilterByType DEFLATE application/xhtml+xml
  AddOutputFilterByType DEFLATE application/rss+xml
  AddOutputFilterByType DEFLATE application/javascript
  AddOutputFilterByType DEFLATE application/x-javascript
  AddOutputFilterByType DEFLATE text/javascript
</IfModule>

# 安全头部
<IfModule mod_headers.c>
  # 防止 MIME 类型嗅探
  Header always set X-Content-Type-Options nosniff
  
  # 防止点击劫持
  Header always set X-Frame-Options DENY
  
  # XSS 保护
  Header always set X-XSS-Protection "1; mode=block"
  
  # 强制 HTTPS（如果使用 HTTPS）
  # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>
