import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '合同编号',
    align: 'center',
    dataIndex: 'orderId_dictText',
  },
  {
    title: '关联报告',
    align: 'center',
    dataIndex: 'reportIds_dictText',
  },
  {
    title: '回款类型',
    align: 'center',
    dataIndex: 'typeId_dictText',
  },
  {
    title: '计划回款日期',
    align: 'center',
    dataIndex: 'planDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '计划回款金额',
    align: 'center',
    dataIndex: 'planAmount',
  },
  {
    title: '已回款金额',
    align: 'center',
    dataIndex: 'actualAmount',
  },
  {
    title: '回款是否已核销',
    align: 'center',
    dataIndex: 'checked',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '是否完成工单',
    align: 'center',
    dataIndex: 'finished',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
  },
  {
    title: '负责人',
    align: 'center',
    dataIndex: 'responsibleBy_dictText',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '合同编号',
    field: 'orderId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'biz_order,contract_no,id',
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '合同编号',
    field: 'orderId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'biz_order,contract_no,id',
    },
    dynamicDisabled: true,
  },
  {
    label: '关联报告',
    field: 'reportIds',
    component: 'JSelectMultiple',
    componentProps: ({ formModel }) => {
      if (formModel.orderId) {
        return {
          dictCode: `report where order_id = ${formModel.orderId}  ,report_no,id`,
        };
      }
      return {};
    },
  },
  {
    label: '回款类型',
    field: 'typeId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'payment_collection_type',
    },
  },
  {
    label: '计划回款日期',
    field: 'planDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '计划回款金额',
    field: 'planAmount',
    component: 'InputNumber',
  },
  {
    label: '已回款金额',
    field: 'actualAmount',
    component: 'InputNumber',
  },
  {
    label: '回款是否已核销',
    field: 'checked',
    component: 'JSwitch',
    componentProps: {
      options: ['Y', 'N'],
    },
  },
  {
    label: '是否完成工单',
    field: 'finished',
    component: 'JSwitch',
    componentProps: {
      options: ['Y', 'N'],
    },
  },
  {
    label: '负责人',
    field: 'responsibleBy',
    component: 'JSelectUser',
    componentProps: {},
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  orderId: { title: '合同编号', order: 0, view: 'list', type: 'string', dictTable: 'biz_order', dictCode: 'id', dictText: 'name' },
  reportIds: { title: '关联报告', order: 1, view: 'checkbox', type: 'string', dictTable: 'report', dictCode: 'id', dictText: 'report_no' },
  typeId: { title: '回款类型', order: 2, view: 'list', type: 'string', dictCode: 'payment_collection_type' },
  planDate: { title: '计划回款日期', order: 3, view: 'date', type: 'string' },
  planAmount: { title: '计划回款金额', order: 4, view: 'number', type: 'number' },
  actualAmount: { title: '已回款金额', order: 5, view: 'number', type: 'number' },
  checked: { title: '回款是否已核销', order: 6, view: 'switch', type: 'string' },
  finished: { title: '是否完成工单', order: 7, view: 'switch', type: 'string' },
  responsibleBy: { title: '负责人', order: 8, view: 'sel_user', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
