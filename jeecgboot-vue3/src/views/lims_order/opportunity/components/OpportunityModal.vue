<template>
  <BasicDrawer v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" width="100%" @ok="handleSubmit" showFooter :isDetail="true">
    <template #titleToolbar>
      <a-space>
        <a-button size="small" v-auth="'lims_order:sample:add'" @click="handleAddQuotation">转化报价</a-button>
      </a-space>
    </template>
    <BasicForm @register="registerForm" name="OpportunityForm" />
    <!-- 子表单区域 -->
    <a-tabs activeKey="sample1" animated>
      <a-tab-pane tab="药品信息" key="sample1" :forceRender="true">
        <!--引用表格-->
        <SampleTable
          :isLocked = "isLocked"
          :opportunityId="opportunity?.id || 'New'"
          ref="sampleTableRef"
          :bizTypeId="opportunity.bizTypeId"
          @sample-modal-callback="handleSampleModalCallback"
          :customerId="opportunity.customerId"
          :customerContactId="opportunity.customerContactId"
        />
      </a-tab-pane>
    </a-tabs>
    <QuotationModal @register="regQuotationModal" @success="handleQuotationSuccess" />
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicDrawer, useDrawer, useDrawerInner } from "/@/components/Drawer";
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../Opportunity.data';
  import { saveOrUpdate } from '../Opportunity.api';
  import SampleTable from '@/views/lims_order/sample/SampleTable.vue';
  import { initDictOptions } from '@/utils/dict';
  import QuotationModal from "@/views/lims_order/components/QuotationModal.vue";
  import { useLocaleStore } from "@/store/modules/locale";
  import { useGo } from "@/hooks/web/usePage";
  import { saveVo } from "@/views/lims_order/Quotation.api";
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const opportunity = ref({});
  const localeStore = useLocaleStore();
  const go = useGo();
  const sampleTableRef = ref();
  const isLocked = ref(false);
  //表单配置
  const [registerForm, { updateSchema, setProps, resetFields, setFieldsValue, validate, scrollToField }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  //表单赋值
  const [regQuotationModal, { openDrawer: openQuotationDrawer }] = useDrawer();
  const [registerModal, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    //重置表单
    await resetFields();
    setDrawerProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    if (unref(isUpdate)) {
      if (!data.record.responsiblePerson) data.record.responsiblePerson = data.record.createBy;
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      opportunity.value = data.record;
    }
    setProps({ disabled: !data?.showFooter });
    if(data.record.opportunityStage != "DISCOVERY"){
      setProps({ disabled: true });
      isLocked.value = true;
    }
    // 隐藏底部时禁用整个表单

    //初始化字典数据
    const dictData = await initDictOptions(`sys_customer_contact,name,id`);
    updateSchema([
      {
        field: 'customerContactId',
        componentProps: {
          options: dictData,
        },
      },
      {
        field: 'bizTypeId',
        component: 'JTreeDict',
        componentProps: {
          parentCode: 'C09', //TODO back和事件未添加，暂时有问题
        },
      },
    ]);
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      setDrawerProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success');
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
  const handleSampleModalCallback = async (data) => {
    // 处理子表单回调
    console.log('子表单回调数据:', data);
    const values = await validate();
    setFieldsValue({
      amount: data.standardPrice,
      estimatedAmount: data.standardPrice * values.winRate * 0.01,
    });
    await saveVo(values, isUpdate.value);
    emit('success');
  };
  const handleAddQuotation = async () => {
    const values = await validate();
    let record = { ...opportunity.value, ...values };

    openQuotationDrawer(true, {
      record,
      samples: sampleTableRef.value.getData(),
      isUpdate: false,
      showFooter: true,
    });
  };

  function handleQuotationSuccess() {
    console.log(11111112222);

    localeStore.setPathTitle('/lims_order/quotationList', '报价单');
    go('/lims_order/quotationList');
    closeDrawer();
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
