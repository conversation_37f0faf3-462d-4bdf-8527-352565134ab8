import { nextTick, unref } from 'vue';
import type { Ref } from 'vue';
import type { Options } from 'sortablejs';

export function useSortable(el: HTMLElement | Ref<HTMLElement>, options?: Options) {
  function initSortable() {
    nextTick(async () => {
      if (!el) {
        console.warn('useSortable: el 参数为空，无法初始化 Sortable');
        return;
      }

      const element = unref(el);
      if (!element) {
        console.warn('useSortable: 解析后的元素为空，无法初始化 Sortable');
        return;
      }

      try {
        const Sortable = (await import('sortablejs')).default;
        Sortable.create(element, {
          animation: 500,
          delay: 400,
          delayOnTouchOnly: true,
          ...options,
        });
      } catch (error) {
        console.error('useSortable: 初始化 Sortable 失败:', error);
      }
    });
  }

  return { initSortable };
}
