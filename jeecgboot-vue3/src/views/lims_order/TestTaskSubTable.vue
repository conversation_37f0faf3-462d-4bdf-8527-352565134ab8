<template>
  <div>
    <!--引用表格-->
    <BasicTable
      bordered
      size="middle"
      :loading="loading"
      rowKey="id"
      :canResize="false"
      :columns="TColumns"
      :dataSource="dataSource"
      :pagination="false"
    >
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watchEffect } from 'vue';
  import { BasicTable } from '/@/components/Table';
  import { TColumns } from './TestTask.data.tsx';
  import { listVo } from './TestTask.api';

  const props = defineProps({
    quotationId: {
      type: String,
      default: '',
    },
    bizOrderId: {
      type: String,
      default: '',
    },
  });

  const loading = ref(false);
  const dataSource = ref([]);
  watchEffect(() => {
    loadData(props.quotationId, props.bizOrderId);
  });

  function loadData(quotationId: string, bizOrderId: string) {
    dataSource.value = [];
    loading.value = true;
    const params: { quotationId?: string; bizOrderId?: string } = {};
    if (quotationId) {
      params.quotationId = quotationId;
    }
    if (bizOrderId) {
      params.bizOrderId = bizOrderId;
    }
    listVo(params)
      .then((res) => {
        dataSource.value = res.records;
      })
      .finally(() => {
        loading.value = false;
      });
  }
</script>
