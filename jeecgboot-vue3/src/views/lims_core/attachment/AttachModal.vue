<template>
<BasicModal v-bind="$attrs" @register="regModal" destroyOnClose :title="title" :width="800">
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" ref="tableRef">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'lims_core:attachment:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" v-auth="'lims_core:attachment:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button type="primary" v-auth="'lims_core:attachment:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_core:attachment:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <AttachmentModal @register="registerModal" @success="handleSuccess" />
  </BasicModal>
</template>

<script lang="tsx" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { BasicTable, TableAction } from '@/components/Table';
  import AttachmentModal from '@/views/lims_core/attachment/components/AttachmentModal.vue';
  import { useListPage } from '@/hooks/system/useListPage';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from '@/views/lims_core/attachment/Attachment.api';
  import { columns, searchFormSchema, superQuerySchema } from '/@/views/lims_core/attachment/Attachment.data';
  import { useUserStore } from '@/store/modules/user';
import { custom } from 'vue-types';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const tableRef = ref();
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();

  const sourceOptions = ref([]);
  const sourceIds = ref([]);
  const sourceTable = ref('');
  //表单赋值
  const [regModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: false, showCancelBtn: false, showOkBtn: false });
    console.log(data.selectedRows);
    sourceOptions.value = data.selectedRows.map((item) => ({
      label: item.sampleNo + ' - ' + item.name,
      value: item.id
    }));
    sourceIds.value = data.selectedRows.map((item) => item.id);
    sourceTable.value = data.sourceTable;
    console.log(sourceIds.value);
    queryParam.sourceId = sourceIds.value;
    queryParam.sourceTable = sourceTable.value;


    //替换columns的sourceId
    const new_columns =  columns.map((item) => {
      if (item.dataIndex === 'sourceId') {
        item.customRender = ({ text, record }) => {
          return sourceOptions.value.find((item) => item.value === text)?.label;
        };
      }
      return item;
    });

    tableRef.value.setColumns(new_columns);
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));

  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '附件表',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '附件表',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      record: {
        sourceTable: sourceTable.value,
      },
      sourceOptions: sourceOptions.value,
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      sourceOptions: sourceOptions.value,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      sourceOptions: sourceOptions.value,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:attachment:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_core:attachment:delete',
      },
    ];
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
