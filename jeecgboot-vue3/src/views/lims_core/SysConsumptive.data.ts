import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '编号（或批号）',
    align:"center",
    dataIndex: 'code'
   },
   {
    title: '名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '类型',
    align:"center",
    dataIndex: 'consumptiveTypeId_dictText'
   },
   {
    title: '纯度（或含量）',
    align:"center",
    dataIndex: 'purity'
   },
   {
    title: '规格',
    align:"center",
    dataIndex: 'spec'
   },
   {
    title: '供应商',
    align:"center",
    dataIndex: 'suppilerId_dictText'
   },
   {
    title: '购入日期',
    align:"center",
    dataIndex: 'purchaseDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "编号（或批号）",
      field: 'code',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "名称",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "类型",
      field: 'consumptiveTypeId',
      component: 'JPopupDict',
      componentProps: {
        placeholder: '请选择类型',
        dictCode: 'sys_consumptive_type_selector,name,id,,',
        multi: false
      },
      //colProps: {span: 6},
 	},
	{
      label: "供应商",
      field: 'suppilerId',
      component: 'JPopupDict',
      componentProps: {
        placeholder: '请选择供应商',
        dictCode: 'sys_supplier_selector,name,id,,',
        multi: false
      },
      //colProps: {span: 6},
 	},
     {
      label: "购入日期",
      field: "purchaseDate",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
	{
      label: "创建人",
      field: 'createBy',
      component: 'Input',
      //colProps: {span: 6},
 	},
     {
      label: "创建日期",
      field: "createTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '编号（或批号）',
    field: 'code',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入编号（或批号）!'},
          ];
     },
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入名称!'},
          ];
     },
  },
  {
    label: '类型',
    field: 'consumptiveTypeId',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择类型',
      dictCode: 'sys_consumptive_type_selector,name,id,,',
      multi: false
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类型!'},
          ];
     },
  },
  {
    label: '纯度（或含量）',
    field: 'purity',
    component: 'Input',
  },
  {
    label: '规格',
    field: 'spec',
    component: 'Input',
  },
  {
    label: '供应商',
    field: 'suppilerId',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择供应商',
      dictCode: 'sys_supplier_selector,name,id,,',
      multi: false
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入供应商!'},
          ];
     },
  },
  {
    label: '购入日期',
    field: 'purchaseDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  code: {title: '编号（或批号）',order: 0,view: 'text', type: 'string',},
  name: {title: '名称',order: 1,view: 'text', type: 'string',},
  consumptiveTypeId: {title: '类型',order: 2,view: 'popup_dict', type: 'string',},
  purity: {title: '纯度（或含量）',order: 3,view: 'text', type: 'string',},
  spec: {title: '规格',order: 4,view: 'text', type: 'string',},
  suppilerId: {title: '供应商',order: 5,view: 'popup_dict', type: 'string',},
  purchaseDate: {title: '购入日期',order: 6,view: 'date', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}