<template>
  <div>
    <vxe-table
      ref="tableRef"
      border
      :loading="loading"
      :data="tableData"
      :edit-config="editConfig"
      :row-config="{ keyField: 'id', isHover: true }"
      keep-source
      show-overflow
      show-active-row
      @edit-closed="handleEditClosed"
      @mousedown="handleTableMousedown"
      @cell-click="handleCellClick"
    >
      <vxe-column type="seq" width="60" />

      <!-- 数据列 -->
      <vxe-column
        v-for="col in sysMethodAnalyteRoudingColumns"
        :key="col.key"
        :field="col.key"
        :title="col.title"
        :width="col.width"
        :edit-render="getEditRender(col)"
      />
      <!-- 操作列 -->
      <vxe-column title="操作" width="150">
        <template #default="{ row }">
          <a-space>
            <a @click.stop.prevent="saveRow(row)">保存</a>
            <a-divider type="vertical" />
            <a-popconfirm title="是否确认删除" @confirm="handleDelete(row)" placement="topLeft">
              <a>删除</a>
            </a-popconfirm>
          </a-space>
        </template>
      </vxe-column>

      <template #empty>
        <div class="empty-block">
          <p>暂无数据</p>
          <a-button type="link" @click="handleAddEmpty"> 新增 </a-button>
        </div>
      </template>
    </vxe-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, watchEffect, onMounted } from 'vue';
  import { sysMethodAnalyteRoudingColumns } from '../SysMethodAnalyte.data';
  import { sysMethodAnalyteRoudingList, sysMethodAnalyteRoudingDelete, saveOrUpdateSysMethodAnalyteRouding } from '../SysMethodAnalyte.api';
  import { message } from 'ant-design-vue';
  import { VxeTablePropTypes } from 'vxe-table';
  import { JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
  import { initDictOptions } from '/@/utils/dict';

  const props = defineProps({
    id: {
      type: String,
      default: '',
    },
  });

  const loading = ref(false);
  const tableData = ref([]);
  // 表格引用
  const tableRef = ref();

  watchEffect(() => {
    props.id && loadData(props.id);
  });

  function loadData(id) {
    tableData.value = [];
    loading.value = true;
    sysMethodAnalyteRoudingList({ id })
      .then((res) => {
        if (res.success) {
          tableData.value = res.result.records;
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  // 修改编辑配置
  const editConfig = ref<VxeTablePropTypes.EditConfig>({
    trigger: 'click',
    mode: 'cell',
    showStatus: true,
    autoClear: true,
  });

  const insertRecords = [];

  // 处理编辑完成
  function handleEditClosed(event) {
    console.log('编辑完成:', event);
    tableRef.value.clearEdit(); // 清除编辑状态
  }

  // 处理删除
  async function handleDelete(row) {
    if (!row.id) {
      message.warning('缺少ID，无法删除');
      return;
    }
    await sysMethodAnalyteRoudingDelete(row.id);
  }

  // 修改addRow方法，添加API调用和事件发送
  async function addRow(record) {
    try {
      loading.value = true;
      // 确保record有初始值
      const newRecord = {
        ...record,
        // 确保有默认值
        roundingAlgorithm: record.roundingAlgorithm || 1,
        roundingWay: record.roundingWay || 1,
        roudingPrecision: record.roudingPrecision || '',
      };
      const { row: newRow } = await tableRef.value.insertAt(newRecord, -1);
      tableRef.value.setEditCell(newRow, 'roudingPrecision');
      insertRecords.push(newRow.id);
      // 将新行添加到数据源
      tableData.value.push(newRow);
    } catch (error) {
      console.error('添加行错误:', error);
      message.error('添加失败: ' + (error.message || '未知错误'));
    } finally {
      loading.value = false;
    }
  }

  // 暴露方法和属性给父组件
  defineExpose({
    addRow,
    loadData,
  });

  // 加载字典数据
  async function loadDictData() {
    try {
      // 遍历列配置
      for (const col of sysMethodAnalyteRoudingColumns) {
        if (col.dictCode && col.type === JVxeTypes.select) {
          // 使用getDictItems加载字典数据
          const dictData = await initDictOptions(col.dictCode);
          if (dictData && dictData.length > 0) {
            // 转换为options格式
            console.log('dictData', dictData);
            col.options = dictData.map((item) => ({
              value: item.value,
              label: item.text || item.label,
              title: item.text || item.label,
              text: item.text || item.label,
            }));
          }
        }
      }
    } catch (error) {
      message.error('加载字典数据失败');
    }
  }

  onMounted(async () => {
    await loadDictData();
  });

  // 处理编辑值变化 - 使用解构简化参数
  function handleValueChange(row, selectedValue, fieldName) {
    // 更新行数据
    if (fieldName) {
      row[fieldName] = selectedValue;
      console.log(`已更新 ${fieldName} = ${selectedValue}`, row);
    }
  }

  // 处理失去焦点事件
  function handleBlur(params) {
    // 延迟执行，避免与其他事件冲突
    setTimeout(() => {
      if (tableRef.value && typeof tableRef.value.clearEdit === 'function') {
        tableRef.value.clearEdit();
      }
    }, 100);
  }

  // 根据列类型获取编辑渲染器配置
  function getEditRender(col) {
    // 确保col有type属性
    const type = col.type || JVxeTypes.input;
    if (type === JVxeTypes.select) {
      return {
        name: 'select',
        options: [{ value: 0, label: `请选择${col.title}` }, ...(col.options || [])],
        props: {
          placeholder: `请选择${col.title}`,
          transfer: true,
          type: 'select',
          stopPropagation: true,
        },
        events: {
          change: (value, $event) => handleValueChange(value.row, $event.target.value, col.key),
          blur: handleBlur,
        },
      };
    } else if (type === JVxeTypes.input) {
      return {
        name: 'input',
        props: {
          placeholder: `请输入${col.title}`,
          type: 'text',
          stopPropagation: true,
        },
        events: {
          input: (value, $event) => handleValueChange(value.row, $event.target.value, col.key),
          blur: handleBlur,
        },
      };
    }

    // 默认配置也需要修改
    return {
      name: 'input',
      props: {
        placeholder: `请输入`,
        type: 'text',
        stopPropagation: true,
      },
      events: {
        input: (value, $event) => handleValueChange(value, $event, col),
        blur: handleBlur,
      },
    };
  }

  // 保存行数据
  async function saveRow(row) {
    try {
      if (insertRecords.includes(row.id)) {
        await saveOrUpdateSysMethodAnalyteRouding(row, false).then((res) => {
          const index = insertRecords.indexOf(row.id);
          if (index > -1) {
            insertRecords.splice(index, 1);
          }
        });
      } else {
        await saveOrUpdateSysMethodAnalyteRouding(row, true);
      }
      tableRef.value.reloadRow(row);
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败: ' + (error.message || '未知错误'));
    }
  }

  // 处理表格鼠标按下事件
  function handleTableMousedown(event) {
    event.stopPropagation();
  }

  // 处理单元格点击
  function handleCellClick({ row, column }) {
    // 只有在列有editRender属性时才设置编辑状态
    if (column.property && row && column.editRender) {
      tableRef.value.setEditCell(row, column.property);
    }
  }

  // 处理空表格时的新建按钮点击
  async function handleAddEmpty() {
    if (!props.id) {
      message.warning('请先选择一个检测指标');
      return;
    }

    // 调用已有的addRow方法，传入必要的参数
    await addRow({
      maId: props.id,
      roundingAlgorithm: 1,
      roundingWay: 1,
      roudingPrecision: '',
    });
  }
</script>
