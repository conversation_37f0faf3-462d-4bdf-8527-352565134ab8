<template>

  <BasicModal
    ref="modalRef"
    destroyOnClose
    wrapClassName="j-cgform-tab-modal"
    v-bind="$attrs"
    @register="registerModal"
    :width="896"
    @ok="handleSubmit"
  >
    <!-- 标题区域 -->
    <template #title>
      <div class="titleArea">
        <div class="title">{{ title }}</div>
        <div class="right">
          <a-radio-group v-model:value="activeKey">
            <template v-for="(item, index) in tabNav" :key="index">
              <a-radio-button :value="item.tableName">{{ item.tableTxt }}</a-radio-button>
            </template>
          </a-radio-group>
        </div>
      </div>
    </template>
    <!--表单区域 -->
    <div class="contentArea">
      <a-space style="margin-bottom: 5px">
        <a-button preIcon="ant-design:printer-outlined" type="primary" @click="handleTranslate">处理翻译</a-button>
      </a-space>
      <!--主表区域 -->
      <BasicForm @register="registerForm" ref="formRef" v-show="activeKey == refKeys[0]" name="SysMethodForm" />
      <!--子表区域 -->
      <JVxeTable
        v-show="activeKey == 'sysMethodConsumptive'"
        keep-source
        resizable
        ref="sysMethodConsumptive"
        :loading="sysMethodConsumptiveTable.loading"
        :columns="sysMethodConsumptiveTable.columns"
        :dataSource="sysMethodConsumptiveTable.dataSource"
        :height="340"
        :disabled="formDisabled"
        :rowNumber="true"
        :rowSelection="true"
        :toolbar="true"
      />
      <JVxeTable
        v-show="activeKey == 'sysMethodStdMaterial'"
        keep-source
        resizable
        ref="sysMethodStdMaterial"
        :loading="sysMethodStdMaterialTable.loading"
        :columns="sysMethodStdMaterialTable.columns"
        :dataSource="sysMethodStdMaterialTable.dataSource"
        :height="340"
        :disabled="formDisabled"
        :rowNumber="true"
        :rowSelection="true"
        :toolbar="true"
      />
      <JVxeTable
        v-show="activeKey == 'sysMethodInstrumentType'"
        keep-source
        resizable
        ref="sysMethodInstrumentType"
        :loading="sysMethodInstrumentTypeTable.loading"
        :columns="sysMethodInstrumentTypeTable.columns"
        :dataSource="sysMethodInstrumentTypeTable.dataSource"
        :height="340"
        :disabled="formDisabled"
        :rowNumber="true"
        :rowSelection="true"
        :toolbar="true"
      />
      <JVxeTable
        v-show="activeKey == 'sysMethodTestingPara'"
        keep-source
        resizable
        ref="sysMethodTestingPara"
        :loading="sysMethodTestingParaTable.loading"
        :columns="sysMethodTestingParaTable.columns"
        :dataSource="sysMethodTestingParaTable.dataSource"
        :height="340"
        :disabled="formDisabled"
        :rowNumber="true"
        :rowSelection="true"
        :toolbar="true"
      />
      <JVxeTable
        dragSort
        sortKey="sortNum"
        :sortBegin="1"
        dragSortFixed="none"
        rowKey="id"
        v-show="activeKey == 'sysMethodRepeatType'"
        keep-source
        resizable
        ref="sysMethodRepeatType"
        :loading="sysMethodRepeatTypeTable.loading"
        :columns="sysMethodRepeatTypeTable.columns"
        :dataSource="sysMethodRepeatTypeTable.dataSource"
        :height="340"
        :rowSelection="true"
        :toolbar="true"
        @value-change="handleMethodRepeatTypeChange"
      />
      <JVxeTable
        v-show="activeKey == 'sysMethodWorkflow'"
        keep-source
        resizable
        ref="sysMethodWorkflow"
        :loading="sysMethodWorkflowTable.loading"
        :columns="sysMethodWorkflowTable.columns"
        :dataSource="sysMethodWorkflowTable.dataSource"
        :height="340"
        :disabled="formDisabled"
        :rowNumber="true"
        :rowSelection="true"
        :toolbar="true"
      />
      <JVxeTable
        v-show="activeKey == 'sysMethodAnalyte'"
        keep-source
        rowKey="analyteId"
        resizable
        ref="sysMethodAnalyte"
        :loading="sysMethodAnalyteTable.loading"
        :columns="sysMethodAnalyteTable.columns"
        :dataSource="sysMethodAnalyteTable.dataSource"
        :height="340"
        :disabled="formDisabled"
        :rowNumber="true"
        :rowSelection="true"
        :toolbar="true"
        :toolbar-config="{ btn: ['remove'] }"
      >
        <template #toolbarSuffix>
          <a-button @click="handleOpen">+ 批量添加</a-button>
        </template>
      </JVxeTable>
    </div>
    <template #insertFooter>
      <a-button key="preview" type="primary" @click="preview">预览</a-button>
    </template>
  </BasicModal>
  <DocEditor @register="registerModalDocEditor" />
  <JPopupOnlReportModal
    @register="regModal"
    code="sys_analyte_selector"
    valueFiled="id"
    :multi="true"
    :groupId="''"
    :selectKeys="selectKeys"
    @ok="callBack"
  />
  <TranslationModal @register="registerTranslationModal" />
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { list, selectOptions } from '../SysStandard.api';
  import {
    formSchema,
    sysMethodConsumptiveColumns,
    sysMethodStdMaterialColumns,
    sysMethodInstrumentTypeColumns,
    sysMethodTestingParaColumns,
    sysMethodRepeatTypeColumns,
    sysMethodWorkflowColumns,
    sysMethodAnalyteColumns,
  } from '../SysMethod.data';
  import {
    saveOrUpdate,
    sysMethodConsumptiveList,
    sysMethodStdMaterialList,
    sysMethodInstrumentTypeList,
    sysMethodTestingParaList,
    sysMethodRepeatTypeList,
    sysMethodWorkflowList,
    sysMethodAnalyteList,
    generateRecord,
    getOOEditorConfig,
  } from '../SysMethod.api';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  import DocEditor from '@/views/dcs/components/DocEditor.vue';
  import JPopupOnlReportModal from '@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';
  import { initDictOptions } from '@/utils/dict';
  import TranslationModal from '@/views/lims_core/components/TranslationModal.vue';
  import { message } from 'ant-design-vue';
  // Emits声明

  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const modalRef = ref();
  const refKeys = ref([
    'sysMethod',
    'sysMethodConsumptive',
    'sysMethodStdMaterial',
    'sysMethodInstrumentType',
    'sysMethodTestingPara',
    'sysMethodRepeatType',
    'sysMethodWorkflow',
    'sysMethodAnalyte',
  ]);
  const tabNav = ref<any>([
    { tableName: 'sysMethod', tableTxt: '方法' },
    { tableName: 'sysMethodConsumptive', tableTxt: '试剂耗材' },
    { tableName: 'sysMethodStdMaterial', tableTxt: '标品' },
    { tableName: 'sysMethodInstrumentType', tableTxt: '设备类别' },
    { tableName: 'sysMethodTestingPara', tableTxt: '实验过程参数' },
    { tableName: 'sysMethodRepeatType', tableTxt: '重复性' },
    { tableName: 'sysMethodWorkflow', tableTxt: '检测流程' },
    // { tableName: 'sysMethodAnalyte', tableTxt: '检测指标' },
  ]);
  const activeKey = ref('sysMethod');
  const sysMethodConsumptive = ref();
  const sysMethodStdMaterial = ref();
  const sysMethodInstrumentType = ref();
  const sysMethodTestingPara = ref();
  const sysMethodRepeatType = ref();
  const sysMethodWorkflow = ref();
  const sysMethodAnalyte = ref();
  const tableRefs = {
    sysMethodConsumptive,
    sysMethodStdMaterial,
    sysMethodInstrumentType,
    sysMethodTestingPara,
    sysMethodRepeatType,
    sysMethodWorkflow,
    sysMethodAnalyte,
  };
  const sysMethodConsumptiveTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodConsumptiveColumns,
  });
  const sysMethodStdMaterialTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodStdMaterialColumns,
  });
  const sysMethodInstrumentTypeTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodInstrumentTypeColumns,
  });
  const sysMethodTestingParaTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodTestingParaColumns,
  });
  const sysMethodRepeatTypeTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodRepeatTypeColumns,
  });
  const sysMethodWorkflowTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodWorkflowColumns,
  });
  const sysMethodAnalyteTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysMethodAnalyteColumns,
  });
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  let methodId;
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    let standardOptions = await selectOptions();
    await updateSchema({
      field: 'standardId',
      component: 'Select',
      componentProps: {
        options: standardOptions,
      },
    });

    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;
    methodId = data?.record?.id;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      requestSubTableData(sysMethodConsumptiveList, { id: data?.record?.id }, sysMethodConsumptiveTable);
      requestSubTableData(sysMethodStdMaterialList, { id: data?.record?.id }, sysMethodStdMaterialTable);
      requestSubTableData(sysMethodInstrumentTypeList, { id: data?.record?.id }, sysMethodInstrumentTypeTable);
      requestSubTableData(sysMethodTestingParaList, { id: data?.record?.id }, sysMethodTestingParaTable);
      requestSubTableData(sysMethodRepeatTypeList, { id: data?.record?.id }, sysMethodRepeatTypeTable, fetchMethodRepeatTypeSuccess);
      requestSubTableData(sysMethodWorkflowList, { id: data?.record?.id }, sysMethodWorkflowTable);
      requestSubTableData(sysMethodAnalyteList, { id: data?.record?.id }, sysMethodAnalyteTable);
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );
  // 弹窗tabs滚动区域的高度
  const tabsStyle = computed(() => {
    let height: Nullable<string> = null;
    let minHeight = '100px';
    let maxHeight: Nullable<string> = '500px';
    // 弹窗wrapper
    let modalWrapperRef = modalRef.value?.modalWrapperRef;
    if (modalWrapperRef) {
      if (modalWrapperRef.fullScreen) {
        height = 'calc(' + modalWrapperRef.spinStyle.height + ' - 50px)';
        maxHeight = null;
      }
    }
    let overflow = 'auto';
    return { height, minHeight, maxHeight, overflow };
  });
  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));
  //重置
  async function reset() {
    await resetFields();
    activeKey.value = 'sysMethod';
    sysMethodConsumptiveTable.dataSource = [];
    sysMethodStdMaterialTable.dataSource = [];
    sysMethodInstrumentTypeTable.dataSource = [];
    sysMethodTestingParaTable.dataSource = [];
    sysMethodRepeatTypeTable.dataSource = [];
    sysMethodWorkflowTable.dataSource = [];
    sysMethodAnalyteTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);
    return {
      ...main, // 展开
      sysMethodConsumptiveList: allValues.tablesValue[0].tableData,
      sysMethodStdMaterialList: allValues.tablesValue[1].tableData,
      sysMethodInstrumentTypeList: allValues.tablesValue[2].tableData,
      sysMethodTestingParaList: allValues.tablesValue[3].tableData,
      sysMethodRepeatTypeList: allValues.tablesValue[4].tableData,
      sysMethodWorkflowList: allValues.tablesValue[5].tableData,
      sysMethodAnalyteList: allValues.tablesValue[6].tableData,
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    try {
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  async function preview() {
    await generateRecord({ methodId: methodId }).then((res) => {
      if (res === 'success') {
        previewFile();
      }
    });
  }
  /**
   * 预览
   */
  async function previewFile() {
    const ooConfig = await getOOEditorConfig(methodId);
    openDocEditorModal(true, ooConfig);
  }

  /**
   * 项目选择弹窗
   */
  //注册model
  const [regModal, { openModal: openAnalyteSelectModal }] = useModal();
  const selectKeys = ref('');
  function handleOpen() {
    selectKeys.value = '';
    console.log(sysMethodAnalyte.value.getTableData());
    sysMethodAnalyte.value.getTableData().forEach((row) => {
      selectKeys.value += row.analyteId + ',';
    });
    // 如果最后一个字符是逗号就去掉
    if (selectKeys.value.charAt(selectKeys.value.length - 1) === ',') {
      selectKeys.value = selectKeys.value.substring(0, selectKeys.value.length - 1);
    }
    console.log(selectKeys.value);
    openAnalyteSelectModal(true);
  }
  function callBack(data) {
    const tableData = sysMethodAnalyte.value.getTableData();
    console.log(data);
    //增
    data.forEach((row) => {
      const index = tableData.findIndex((item) => item.analyteId === row.id);
      if (index === -1) {
        sysMethodAnalyte.value.addRows([{ analyteId: row.id, id: tableData.length + 1 }], {
          setActive: false,
        });
      }
    });
    //删
    tableData.forEach((row, _) => {
      //removeRowsById
      const index = data.findIndex((item) => item.id === row.analyteId);
      console.log(index);
      if (index === -1) {
        console.log(row);
        sysMethodAnalyte.value.removeRows(row);
      }
    });
  }

  const handleMethodRepeatTypeChange = (data) => {
    console.log(data);
    if (data.col.key == 'repeatType') {
      console.log(data.target);

      initDictOptions(data.value).then((res) => {
        console.log(res);
        data.target.columns[2].type = 'select-multiple';
        data.target.columns[2].options = res;
        data.row.repeatName = null;
      });
    }
  };
  const fetchMethodRepeatTypeSuccess = async (res) => {
    console.log('fetchMethodRepeatTypeSuccess', res);
    await initDictOptions(res.result[0].repeatType).then((res) => {
      console.log(res);
      sysMethodRepeatTypeTable.columns[2].type = 'select-multiple';
      sysMethodRepeatTypeTable.columns[2].options = res;
      sysMethodRepeatTypeTable.dataSource = [];
    });
    setTimeout(() => {
      requestSubTableData(sysMethodRepeatTypeList, { id: res.result[0].methodId }, sysMethodRepeatTypeTable);
    }, 500);
  };

  const [registerTranslationModal, { openModal: openTranslationModal }] = useModal();

  const handleTranslate = async () => {
    let values = await validate();
    try{
      await saveOrUpdate(values, isUpdate.value);
      openTranslationModal(true, {
        baseTable: 'sys_method',
        baseId: values.id,
        recordvalue: { ...values },
        formSchema: formSchema,
      });
    }catch (e) {
      if (e.code === VALIDATE_FAILED) {
        message.error('请填入必填信息');
      } else {
        message.error(e.message);
      }
    }

  };
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }

  .titleArea {
    display: flex;
    align-content: center;
    padding-right: 70px;
    .title {
      margin-right: 16px;
      line-height: 32px;
    }
    .right {
      overflow-x: auto;
      overflow-y: hidden;
      flex: 1;
      white-space: nowrap;
      .ant-radio-group {
        font-weight: normal;
      }
    }
  }

  html[data-theme='light'] {
    .right {
      .ant-radio-group {
        :deep(.ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked)) {
          color: #555;
        }
      }
    }
  }
</style>

<style lang="less">
  // Online表单Tab风格专属样式
  .j-cgform-tab-modal {
    .contentArea {
      padding: 20px 1.5% 0;
    }

    //.ant-modal-header {
    //  padding-top: 8px;
    //  padding-bottom: 8px;
    //  border-bottom: none !important;
    //}

    .ant-modal .ant-modal-body > .scrollbar,
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-tabs-top-bar {
      width: calc(100% - 55px);
      position: relative;
      left: -14px;
    }

    .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
      overflow: hidden auto;
    }
  }
</style>
