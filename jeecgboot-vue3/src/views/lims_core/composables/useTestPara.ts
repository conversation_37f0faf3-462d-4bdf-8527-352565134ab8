import { ref, reactive } from 'vue';
import { useListPage } from '/@/hooks/system/useListPage';
import { columns, searchFormSchema, superQuerySchema } from '../TestPara.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from '../TestPara.api';
import { useModal } from '/@/components/Modal';
import TestParaModal from '../components/TestParaModal.vue';
import type { BasicColumn } from '/@/components/Table';
import type { ActionItem } from '/@/components/Table/src/types/table';

export function useTestPara() {
  const queryParam = reactive<any>({});
  const loading = ref(false);
  const [registerModal, { openModal }] = useModal();

  // 注册表格数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '检测过程参数',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
      },
      actionColumn: {
        title: '操作',
        width: 120,
        fixed: 'right'
      } as BasicColumn,
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: "检测过程参数",
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  // 处理高级查询
  function handleSuperQuery(params) {
    Object.keys(params).forEach((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  // 新增
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  // 编辑
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  // 详情
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  // 删除
  async function handleDelete(record) {
    loading.value = true;
    try {
      await deleteOne({ id: record.id });
      handleSuccess();
    } finally {
      loading.value = false;
    }
  }

  // 批量删除
  async function batchHandleDelete() {
    loading.value = true;
    try {
      await batchDelete({ ids: selectedRowKeys.value });
      handleSuccess();
    } finally {
      loading.value = false;
    }
  }

  // 成功回调
  function handleSuccess() {
    selectedRowKeys.value = [];
    reload();
  }

  // 获取表格操作
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:test_para:edit'
      }
    ];
  }

  // 获取下拉操作
  function getDropDownAction(record): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
          getPopupContainer: () => document.body,
        },
        auth: 'lims_core:test_para:delete'
      }
    ];
  }

  return {
    loading,
    registerTable,
    registerModal,
    rowSelection,
    selectedRowKeys,
    superQueryConfig,
    handleSuperQuery,
    handleAdd,
    onExportXls,
    onImportXls,
    batchHandleDelete,
    getTableAction,
    getDropDownAction,
    handleSuccess,
  };
} 