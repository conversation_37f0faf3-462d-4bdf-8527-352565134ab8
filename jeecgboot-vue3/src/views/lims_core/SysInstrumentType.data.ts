import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '类别简称',
    align:"center",
    dataIndex: 'abbrName'
   },
   {
    title: '类别全称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '性质类别',
    align:"center",
    dataIndex: 'natureTypeId_dictText'
   },
   {
    title: '校准周期（天）',
    align:"center",
    dataIndex: 'calibrationCycle'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "类别简称",
      field: 'abbrName',
      component: 'Input',
      //colProps: {span: 6},
 	},
     {
      label: "创建日期",
      field: "createTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '类别简称',
    field: 'abbrName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类别简称!'},
          ];
     },
  },
  {
    label: '类别全称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类别全称!'},
          ];
     },
  },
  {
    label: '性质类别',
    field: 'natureTypeId',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"instrument_nature_type"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入性质类别!'},
          ];
     },
  },
  {
    label: '校准周期（天）',
    field: 'calibrationCycle',
    component: 'InputNumber',
  },
  {
    label: '父级节点',
    field: 'pid',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  abbrName: {title: '类别简称',order: 0,view: 'text', type: 'string',},
  name: {title: '类别全称',order: 1,view: 'text', type: 'string',},
  natureTypeId: {title: '性质类别',order: 2,view: 'list', type: 'string',dictCode: 'instrument_nature_type',},
  calibrationCycle: {title: '校准周期（天）',order: 3,view: 'number', type: 'number',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}