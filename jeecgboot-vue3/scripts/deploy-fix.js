/**
 * 部署修复脚本
 * 解决 SPA 路由和 MIME 类型问题
 */

const fs = require('fs');
const path = require('path');

const distDir = path.resolve(__dirname, '../dist');

console.log('开始执行部署修复...');

// 1. 检查 dist 目录是否存在
if (!fs.existsSync(distDir)) {
  console.error('错误: dist 目录不存在，请先执行 npm run build');
  process.exit(1);
}

// 2. 创建 web.config 文件（用于 IIS）
const webConfigContent = `<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <staticContent>
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <mimeMap fileExtension=".mjs" mimeType="application/javascript" />
      <mimeMap fileExtension=".css" mimeType="text/css" />
      <mimeMap fileExtension=".woff" mimeType="font/woff" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
    </staticContent>
    <rewrite>
      <rules>
        <rule name="SPA Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>`;

const webConfigPath = path.join(distDir, 'web.config');
fs.writeFileSync(webConfigPath, webConfigContent);
console.log('✓ 已创建 web.config 文件');

// 3. 复制 .htaccess 文件
const htaccessSource = path.resolve(__dirname, '../public/.htaccess');
const htaccessDest = path.join(distDir, '.htaccess');

if (fs.existsSync(htaccessSource)) {
  fs.copyFileSync(htaccessSource, htaccessDest);
  console.log('✓ 已复制 .htaccess 文件');
}

console.log('部署修复完成！');
