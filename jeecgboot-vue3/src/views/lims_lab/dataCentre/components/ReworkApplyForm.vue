<script setup lang="ts">
import { ref, reactive, PropType, watch, onMounted } from 'vue';
import { BasicForm, useForm } from '/@/components/Form';
import { reworkApplySchema } from '../dataCentre.data';
import { useUserStore } from '/@/store/modules/user';

const props = defineProps({
  testIds: { type: String, required: true },
});

const emit = defineEmits(['register', 'success']);
const userStore = useUserStore();

// 表单值
const formModel = reactive({
  reworkReason: '',
  reworkBy: '',
  testIds: '',
});

// 初始化表单
const [registerForm, { resetFields, validate, setFieldsValue, getFieldsValue }] = useForm({
  labelWidth: 120,
  schemas: reworkApplySchema,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
  actionColOptions: { span: 24 },
  model: formModel,
  rowProps: { gutter: 24 },
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
});

// 设置默认值
onMounted(() => {
  // 设置复测人为当前用户
  const currentUser = userStore.getUserInfo;
  if (currentUser && currentUser.id) {
    // 单选模式下，直接设置ID值
    setFieldsValue({
      reworkBy: currentUser.id,
    });
  }
});

// 暴露方法给父组件
defineExpose({
  validate,
  resetFields,
  setFieldsValue,
  getFieldsValue,
});

// 监听props变化，更新表单值
watch(
  () => props.testIds,
  (val) => {
    if (val) {
      formModel.testIds = val;
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="p-4">
    <BasicForm @register="registerForm" />
  </div>
</template>

<style scoped>
.p-4 {
  padding: 1rem;
}
</style>