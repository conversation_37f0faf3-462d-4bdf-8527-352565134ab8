<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" @row-click="onRowClick" >
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" :disabled="!canEdit" v-auth="'lims_core:standard_material:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>

    <AttachTable :sourceId="selectedRecord.id || 'nonono'" :sourceName="selectedRecord.code" sourceTable="standard_material" />

    <!-- 表单区域 -->
    <StandardMaterialTableModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="lims_core-standardMaterial" setup>
  import { ref, reactive, computed, unref, defineProps } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, searchFormSchema, superQuerySchema } from './StandardMaterialTable.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from '../StandardMaterial.api';
  import { useUserStore } from '/@/store/modules/user';

  import { message } from 'ant-design-vue';
  import StandardMaterialTableModal from '@/views/lims_core/sm_table/StandardMaterialTableModal.vue';
  import AttachTable from '@/views/lims_core/attachment/AttachTable.vue';
  /**
   * props声明
   */
  const props = defineProps({
    sampleId: {
      type: [String, Number],
    },
    customerId: {
      type: [String, Number],
    },
    canEdit: {
      type: Boolean,
      default: false,
    },
  });

  const selectedRecord = ref({
  });
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'StandardMaterialTable',
      },
      title: '标准物质台账',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [
          ['purchaseDate', ['purchaseDate_begin', 'purchaseDate_end'], 'YYYY-MM-DD'],
          ['createTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD HH:mm:ss'],
        ],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.sampleId = props.sampleId;
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '标准物质台账',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      record: {
        sampleId: props.sampleId,
        customerId: props.customerId,
      },
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:standard_material:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_core:standard_material:delete',
      },
    ];
  }

  function onRowClick(record){
    selectedRecord.value = record;
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
