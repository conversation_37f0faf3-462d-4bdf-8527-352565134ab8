<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" ref="tableRef" :expandedRowRender="handleExpandedRowRender">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:printer-outlined" @click="print">打印</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <TestModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="tsx" setup>
  import { hiprint } from 'vue-plugin-hiprint';
  import { ref, reactive, computed, unref, watch } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import TestModal from './TestModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './TestTable.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './Test.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { defineProps, defineEmits } from 'vue';
  import barcode from '@/templates/barcode';
  import TestResultInfoTab from '@/views/lims_lab/dataCentre/components/TestResultInfoTab.vue';
  /**
   * props声明
   */
  const props = defineProps({
    taskId: {
      type: [String, Number],
      default: 'New',
    },
  });

  // Define emits
  const emit = defineEmits();

  //表格实例
  const tableRef = ref();

  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '测试',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.taskId = props.taskId;
        queryParam.column = 'trialNo';
        queryParam.order = 'asc';
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '测试',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_order:sample:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  function getData() {
    return tableRef.value.getDataSource();
  }

  //监听props.quotationId
  watch(
    () => props.taskId,
    (value) => {
      queryParam.taskId = value;
      reload();
    }
  );

  /**
   * 打印
   */
  function print() {
    if (selectedRows.value.length == 0) {
      message.error('请选择要打印的数据');
      return;
    }
    let hiprintTemplate = new hiprint.PrintTemplate({ template: barcode });
    let printDatas = [];
    selectedRows.value.forEach((item) => {
      console.log(item);

      let printData = { barcode: item.id, title: item.methodId_dictText };
      printDatas.push(printData);
    });
    // 打印
    hiprintTemplate.print(printDatas);
  }

  defineExpose({
    getData: getData,
  });

  function handleExpandedRowRender(data) {
    console.log('record', data.record);
    return <TestResultInfoTab curTestIds={data.record.id}></TestResultInfoTab>;
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
