import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_core/sysStandard/list',
  save='/lims_core/sysStandard/add',
  edit='/lims_core/sysStandard/edit',
  deleteOne = '/lims_core/sysStandard/delete',
  deleteBatch = '/lims_core/sysStandard/deleteBatch',
  importExcel = '/lims_core/sysStandard/importExcel',
  exportXls = '/lims_core/sysStandard/exportXls',
  sysStandardEvaluationLimtList = '/lims_core/sysStandard/querySysStandardEvaluationLimtByMainId',
  fetch = '/lims_core/sysStandard/fetch',
  querySysStandardByMethodId = '/lims_core/sysStandard/querySysStandardByMethodId',
  addYaoDian = '/lims_core/sysStandard/addYaoDian',
  selectOptions = '/lims_core/sysStandard/selectOptions',
  clonestandard = '/lims_core/sysStandard/clonestandard',
  sysEvaluation = '/lims_core/sysStandard/querySysStandardEvaluationLimtByResultId',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;
/**
 * 查询子表数据
 * @param params
 */
export const sysStandardEvaluationLimtList = Api.sysStandardEvaluationLimtList;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}

/**
 * 级联查询
 * @param params
 */
export const fetch = (params) =>
  defHttp.get({url: Api.fetch, params});

/**
 * 查询方法下的标准
 */
export const querySysStandardByMethodId = (params) =>
  defHttp.get({url: Api.querySysStandardByMethodId, params});

/**
 * 添加药典
 */
export const addYaoDian = (params) =>
  defHttp.post({url: Api.addYaoDian, params});
/*
  * 查询下拉列表
 */
export const selectOptions = () =>
  defHttp.get({url: Api.selectOptions});

/**
 * 复制标准
 */
export const clonestandard = (params, isUpdate) => {
  return defHttp.post({url: Api.clonestandard, params});
};
/**
 * 查询标准
 * @param params
 */
export const sysEvaluationlist = (params) =>
  defHttp.get({url: Api.sysEvaluation, params});
