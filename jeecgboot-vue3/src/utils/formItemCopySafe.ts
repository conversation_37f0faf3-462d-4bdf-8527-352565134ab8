/**
 * 安全版本的表单项复制功能
 * 避免影响表单的正常显示和验证
 */

/**
 * 初始化安全的表单项复制功能
 */
export function initSafeFormItemCopy() {
  console.log('初始化安全的表单项复制功能');
  
  // 只添加事件监听，不添加任何样式或DOM修改
  addSafeEventListeners();
}

/**
 * 添加安全的事件监听
 */
function addSafeEventListeners() {
  // 只监听双击事件，不添加悬停提示
  document.addEventListener('dblclick', (e) => {
    try {
      const target = e.target as HTMLElement;
      if (!target) return;

      // 严格检查，确保不是表单验证相关元素
      if (isFormValidationElement(target)) {
        return;
      }

      const formItem = getFormItemFromTarget(target);
      
      if (formItem && isSafeFormControl(target)) {
        e.preventDefault();
        e.stopPropagation();
        
        const value = extractValueFromFormItem(formItem);
        if (value) {
          copyToClipboard(value);
          showSimpleNotification(value);
        }
      }
    } catch (error) {
      console.warn('Error in safe dblclick event:', error);
    }
  }, true);
}

/**
 * 检查是否是表单验证相关元素
 */
function isFormValidationElement(element: HTMLElement): boolean {
  const validationSelectors = [
    '.ant-form-item-label',
    '.ant-form-item-explain',
    '.ant-form-item-extra',
    '.ant-form-item-explain-error',
    '.ant-form-item-explain-warning',
    '.ant-form-item-explain-success',
    '.ant-form-item-required',
    '.ant-form-item-optional'
  ];

  for (const selector of validationSelectors) {
    if (element.matches(selector) || element.closest(selector)) {
      return true;
    }
  }

  return false;
}

/**
 * 从目标元素获取表单项
 */
function getFormItemFromTarget(element: HTMLElement): HTMLElement | null {
  if (!element) return null;

  let current: HTMLElement | null = element;
  let depth = 0;

  while (current && depth < 10) {
    if (current.classList && current.classList.contains('ant-form-item')) {
      // 确保这个表单项包含实际的表单控件
      const hasControl = current.querySelector('.ant-form-item-control-input-content');
      if (hasControl) {
        return current;
      }
    }
    current = current.parentElement;
    depth++;
  }

  return null;
}

/**
 * 检查是否是安全的表单控件
 */
function isSafeFormControl(element: HTMLElement): boolean {
  if (!element || !element.matches || !element.closest) return false;

  // 只处理明确的表单控件
  const safeSelectors = [
    '.ant-input',
    '.ant-input-number-input', 
    '.ant-textarea',
    '.ant-select-selection-item',
    '.ant-picker-input input'
  ];

  try {
    for (const selector of safeSelectors) {
      if (element.matches(selector) || element.closest(selector)) {
        return true;
      }
    }
  } catch (error) {
    return false;
  }

  return false;
}

/**
 * 从表单项提取值
 */
function extractValueFromFormItem(formItem: HTMLElement): string {
  // 1. 输入框
  const input = formItem.querySelector('input:not([type="hidden"]), textarea') as HTMLInputElement;
  if (input && input.value && input.value.trim()) {
    return input.value.trim();
  }

  // 2. 选择器显示值
  const selectionItem = formItem.querySelector('.ant-select-selection-item');
  if (selectionItem && selectionItem.textContent) {
    return selectionItem.textContent.trim();
  }

  // 3. 多选值
  const selectionItems = formItem.querySelectorAll('.ant-select-selection-item');
  if (selectionItems.length > 0) {
    const values = Array.from(selectionItems)
      .map(item => (item.textContent || '').trim())
      .filter(text => text);
    if (values.length > 0) {
      return values.join(', ');
    }
  }

  // 4. 日期选择器
  const dateInput = formItem.querySelector('.ant-picker-input input') as HTMLInputElement;
  if (dateInput && dateInput.value && dateInput.value.trim()) {
    return dateInput.value.trim();
  }

  return '';
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text: string): Promise<void> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';
      textarea.style.pointerEvents = 'none';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
  }
}

/**
 * 显示简单通知
 */
function showSimpleNotification(copiedText: string) {
  // 检查是否已有通知，避免重复
  const existingNotification = document.querySelector('.safe-copy-notification');
  if (existingNotification) {
    existingNotification.remove();
  }

  const notification = document.createElement('div');
  notification.className = 'safe-copy-notification';
  
  const displayText = copiedText.length > 20 ? 
    copiedText.substring(0, 20) + '...' : 
    copiedText;
  
  notification.textContent = `已复制: ${displayText}`;
  
  // 内联样式，避免CSS冲突
  notification.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    pointer-events: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  `;
  
  document.body.appendChild(notification);
  
  // 3秒后移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}
