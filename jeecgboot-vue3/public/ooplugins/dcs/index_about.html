<!--
 (c) Copyright Ascensio System SIA 2020

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
 -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>About</title>
    <style>
        p, a{
            font-size: 12px;
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
        }


        a:link {
            color: darkgrey;
        }

        /* visited link */
        a:visited {
            color: darkgrey;
        }

        /* mouse over link */
        a:hover {
            color: #8d8d8d;
        }

        /* selected link */
        a:active {
            color: darkgrey;
        }
    </style>
        <script type="text/javascript" src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.js"></script>
        <script type="text/javascript" src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins-ui.js"></script>
        <link rel="stylesheet" href="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.css">
    <script type="text/javascript">
        window.Asc.plugin.init = function()
        {
            this.resizeWindow(392, 147, 392, 147, 392, 147);
            // none
        };

        window.Asc.plugin.button = function(id)
        {
            this.executeCommand("close", "");
        };

    </script>
</head>
<body style="padding-left: 20px; padding-right: 20px">
<p style="font-size: 15px">Hello World! Add-on</p>
<p style="font-size: 12px">This simple plugin is designed to show the basic functionality of ONLYOFFICE Document Editor plugins. It inserts the 'Hello World!' phrase when you press the button.</p>
<p style="font-size: 12px"><a href="https://github.com/ONLYOFFICE/sdkjs-plugins/tree/master/helloworld" target="_blank">Source code.</a></p>
</body>
</html>