<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection" ref="tableRef">
      <!--操作栏-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 增删/修改比例</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"  />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <PaymentCollectionModal @register="registerModal" @success="handleSuccess" />
    <PaymentCollectionAddModal @register="registerAddModal" @success="handleSuccess" />
    <PaymentModal @register="registerPaymentModal" @success="handleSuccess" ref="paymentModal" />
  </div>
</template>

<script lang="tsx" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import PaymentCollectionModal from './components/PaymentCollectionModal.vue';
  import PaymentCollectionAddModal from './components/PaymentCollectionAddModal.vue';
  import { columns } from './PaymentCollectionTable.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './PaymentCollection.api';
  import { useUserStore } from '/@/store/modules/user';
  import PaymentTable from '@/views/lims_order/payment/PaymentTable.vue';
  import PaymentModal from "@/views/lims_order/payment/components/PaymentModal.vue";
  /**
   * props声明
   */
  const props = defineProps({
    order: {
      type: Object,
      default: null,
    },
  });

  // Define emits
  const emit = defineEmits();

  const paymentModal = ref();

  //表格实例
  const tableRef = ref();

  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerAddModal, { openModal: openAddModal }] = useModal();
  const [registerPaymentModal, { openModal: openPaymentModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'PaymentCollectionTable',
      },
      title: '测试',
      api: list,
      columns,
      expandedRowRender: (record) => {
        console.log('record', record);
        return <PaymentTable paymentCollection={record.record} onSuccess={handleSuccess} />;
      },
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 150,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.orderId = props.order.id;
        queryParam.column = 'sortNum';
        queryParam.order = 'asc';
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '测试',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {

    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openAddModal(true, {
      datas: getData(),
      order: props.order,
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
    paymentModal.value.doReload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_order:sample:edit',
      },
      {
        label: '添加回款',
        onClick: handlePaymentAdd.bind(null, record),
        auth: 'lims_order:sample:edit',
      },
    ];
  }

  function handlePaymentAdd(record: Recordable) {
    let obj = {
      paymentCollectionId: record.id,
      planAmount: record.planAmount,
      actualAmount: record.actualAmount,
    };
    openPaymentModal(true, {
      record:obj,
      isUpdate: false,
      showFooter: true,
    });
  }

  function getData() {
    return tableRef.value.getDataSource();
  }

  defineExpose({
    getData: getData,
  });
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
