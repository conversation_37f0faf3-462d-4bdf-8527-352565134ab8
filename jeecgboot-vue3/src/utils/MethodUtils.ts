/**
 * 计算方差工具类
 */

/**
 * 计算数组总和
 * @param values 数值数组
 * @returns 总和
 */
const sum = (values: number[]): number => {
  return values.reduce((total, value) => total + value, 0);
};

/**
 * 计算数组平均值
 * @param values 数值数组
 * @returns 平均值
 */
const average = (values: number[]): number => {
  return sum(values) / values.length;
};

/**
 * 计算相对方差
 * @param values 数值数组
 * @returns 计算结果
 */
const calcRelativeVariance = (values: number[]): number => {
  if (values.length === 2) {
    return Math.abs(values[1] - values[0]) / average(values);
  } else {
    const max = Math.max(...values);
    const min = Math.min(...values);
    return Math.abs(max - min) / average([max, min]);
  }
};

/**
 * 计算相对方差2
 * @param values 数值数组
 * @returns 计算结果
 */
const calcRelativeVariance2 = (values: number[]): number => {
  const avg = average(values);
  return Math.abs(values[0] - avg) / avg;
};

/**
 * 计算绝对方差
 * @param values 数值数组
 * @returns 计算结果
 */
const calcAbsoluteVariance = (values: number[]): number => {
  let result = -1;
  if (values.length === 2) {
    result = Math.abs(values[0] - values[1]);
  }
  return result;
};

/**
 * 计算平均方差
 * @param values 数值数组
 * @returns 计算结果
 */
const calcAverageVariance = (values: number[]): number => {
  let result = -1;
  if (values.length > 0) {
    const v: number[] = new Array(values.length);
    const avg = average(values);
    for (let i = 0; i < values.length; i++) {
      v[i] = Math.abs(values[i] - avg);
    }
    result = average(v);
  }
  return result;
};

/**
 * 计算相对平均方差
 * @param values 数值数组
 * @returns 计算结果
 */
const calcRelativeAverageVariance = (values: number[]): number => {
  return calcAverageVariance(values) / average(values);
};

/**
 * 计算标准方差
 * @param values 数值数组
 * @returns 计算结果
 */
const calcStandardVariance = (values: number[]): number => {
  let result = -1;
  if (values.length > 1) {
    const avg = average(values);
    const v: number[] = new Array(values.length);
    for (let i = 0; i < values.length; i++) {
      v[i] = Math.pow(values[i] - avg, 2);
    }
    result = Math.sqrt(sum(v) / (values.length - 1));
  }
  return result;
};

/**
 * 计算相对标准方差
 * @param values 数值数组
 * @returns 计算结果
 */
const calcRelativeStandardVariance = (values: number[]): number => {
  return calcStandardVariance(values) / average(values);
};

/**
 * 应用不同的修约方法
 * @param value 需要修约的值
 * @param digits 小数位数
 * @param methodID 修约方法ID
 * @returns 修约后的值
 */
const applyRoundingMethod = (value: number, digits: number, methodID: number): number => {
  const factor = Math.pow(10, digits);
  
  switch (methodID) {
    case 1: // 四舍五入
      return Math.round(value * factor) / factor;
    
    case 2: // 向上舍入
      return Math.ceil(value * factor) / factor;
    
    case 3: // 向下舍入
      return Math.floor(value * factor) / factor;
    
    case 4: // 四舍六入五成双
      const temp = value * factor;
      const intPart = Math.floor(temp);
      const fraction = temp - intPart;
      
      if (fraction < 0.5) {
        return intPart / factor; // 小于0.5，舍去
      } else if (fraction > 0.5) {
        return (intPart + 1) / factor; // 大于0.5，进位
      } else {
        // 等于0.5，奇进偶舍
        return (intPart % 2 === 0) ? intPart / factor : (intPart + 1) / factor;
      }
    
    default:
      return Math.round(value * factor) / factor; // 默认四舍五入
  }
};

/**
 * 计算方差
 * @param values 数值数组
 * @param varianceTypeId 方差类型ID
 * @returns 计算结果
 */
export const calcVariance = (values: number[], varianceTypeId: number): number => {
  let result = -1;
  switch (varianceTypeId) {
    case 1:
      result = calcRelativeVariance(values); // 相对相差(误差）
      break;
    case 2:
      result = calcRelativeVariance(values); // 相对偏差
      break;
    case 3:
    case 8:
      result = calcAbsoluteVariance(values); // 绝对误差/绝对差值
      break;
    case 4:
      result = calcAverageVariance(values); // 平均偏差
      break;
    case 5:
      result = calcRelativeAverageVariance(values); // 相对平均偏差
      break;
    case 6:
      result = calcStandardVariance(values); // 标准偏差
      break;
    case 7:
      result = calcRelativeStandardVariance(values); // 相对标准偏差
      break;
  }
  
  if (result !== -1 && varianceTypeId !== 3 && varianceTypeId !== 8) {
    result = Math.round(result * 100 * 100) / 100; // 保留两位小数
  }
  
  return result;
};

/**
 * 获取抽样方式结论
 * @param values 数值数组
 * @param n 样本数
 * @param c 允许超出m的样本数
 * @param m 下限值
 * @param M 上限值
 * @returns 结论
 */
export const getSamplingWayConclusion = (values: number[], n: number, c: number, m: number, M: number): string => {
  let conclusion = "";
  if (values.length === n) {
    const lstm: number[] = [];
    const lstmM: number[] = [];
    const lstM: number[] = [];
    
    for (const v of values) {
      if (v <= m) {
        lstm.push(v);
      }
      if (M > 0) {
        if (v >= m && v <= M) {
          lstmM.push(v);
        }
        if (v > M) {
          lstM.push(v);
        }
      }
    }
    
    if (lstm.length === n) {
      conclusion = "合格";
    } else {
      if (M > 0) {
        if (lstM.length > 0) {
          conclusion = "不合格";
        } else {
          if (lstmM.length <= c) {
            conclusion = "合格";
          } else {
            conclusion = "不合格";
          }
        }
      }
    }
  }
  return conclusion;
};

/**
 * 修约方法
 * @param value 需要修约的值
 * @param numOfDigit 小数位数
 * @param roundingMethodID 修约方法ID
 * @param isScientific 是否使用科学计数法
 * @param significantFigure 有效数字位数
 * @returns 修约后的值
 */
export const rounding = (value: string, numOfDigit: number, roundingMethodID: number, isScientific: boolean, significantFigure: number = -1): string | number => {
  // 如果值为空，直接返回
  if (!value || value.trim() === '') {
    return '';
  }

  // 尝试将值转换为数字
  let numValue: number;
  try {
    numValue = parseFloat(value);
    if (isNaN(numValue)) {
      return value;
    }
  } catch {
    return value;
  }

  // 如果是0，直接返回格式化后的0
  if (numValue === 0) {
    return isScientific ? '0.0E+0' : numOfDigit > 0 ? '0.' + '0'.repeat(numOfDigit) : '0';
  }

  // 处理科学计数法
  if (isScientific) {
    // 科学计数法的实现
    const absValue = Math.abs(numValue);
    const exponent = Math.floor(Math.log10(absValue));
    const mantissa = absValue / Math.pow(10, exponent);
    
    // 根据有效数字位数修约尾数
    let roundedMantissa: number;
    if (significantFigure > 0) {
      const factor = Math.pow(10, significantFigure - 1);
      roundedMantissa = Math.round(mantissa * factor) / factor;
    } else {
      roundedMantissa = applyRoundingMethod(mantissa, numOfDigit, roundingMethodID);
    }
    
    // 格式化科学计数法
    return (numValue < 0 ? '-' : '') + roundedMantissa.toFixed(numOfDigit) + 'E' + (exponent >= 0 ? '+' : '') + exponent;
  }

  // 非科学计数法的修约
  if (significantFigure > 0) {
    // 有效数字修约
    const absValue = Math.abs(numValue);
    const exponent = absValue === 0 ? 0 : Math.floor(Math.log10(absValue));
    const digitsToRound = significantFigure - exponent - 1;
    
    // 应用修约方法
    const roundedValue = applyRoundingMethod(numValue, digitsToRound, roundingMethodID);
    return (numValue < 0 ? '-' : '') + Math.abs(roundedValue).toFixed(Math.max(0, digitsToRound));
  } else {
    // 小数位数修约
    return applyRoundingMethod(numValue, numOfDigit, roundingMethodID).toFixed(numOfDigit);
  }
};

// 为了兼容性，导出一个MethodUtils对象
export const MethodUtils = {
  calcVariance,
  rounding,
  getSamplingWayConclusion,
};