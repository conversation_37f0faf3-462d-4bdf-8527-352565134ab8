<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    destroyOnClose
    @close="handleSuccess"
    width="70%"
    :title="getTitle"
    @ok="handleSubmit"
    showFooter
    :isDetail="true"
    :maskClosable="false"
    :mask="false"
  >
    <a-tabs v-if="!isDetail" type="card" v-model:activeKey="activeKey" @change="onTabChange">
      <a-tab-pane key="info" tab="基本信息">
        <a-space style="margin: 5px">
          <a-button
            :disabled="!userStore.getUserInfo.roles.includes('register')"
            preIcon="ant-design:printer-outlined"
            type="primary"
            @click="handleCopySample"
            >按批号复制</a-button
          >
          <a-button
            :disabled="!userStore.getUserInfo.roles.includes('register')"
            preIcon="ant-design:printer-outlined"
            type="primary"
            @click="handleTranslate"
            >处理翻译</a-button
          >
        </a-space>
        <BasicForm @register="registerForm" name="SampleForm" />
      </a-tab-pane>
      <a-tab-pane key="test" tab="检验项目">
        <TestTaskTable :sampleId="model?.id" :disabled="model?.orderId != null" :sampleNo="model?.sampleNo" />
      </a-tab-pane>
      <a-tab-pane key="attach" tab="附件">
        <AttachTable
          :canEdit="userStore.getUserInfo.roles.includes('register')"
          parentField="quotation_id"
          :parentFieldId="model?.quotationId"
          :sourceId="model?.id"
          sourceTable="sample"
          displayText="sampleNo,name"
        />
      </a-tab-pane>
      <a-tab-pane key="outerSM" tab="外来标准品">
        <StandardMaterialTable :sampleId="model?.id" :customerId="model?.customerId" :canEdit="userStore.getUserInfo.roles.includes('register')" />
      </a-tab-pane>
      <a-tab-pane key="outerCC" tab="外来色谱柱">
        <ColumnConsumTable :sampleId="model?.id" :customerId="model?.customerId" :canEdit="userStore.getUserInfo.roles.includes('register')" />
      </a-tab-pane>
      <a-tab-pane tab="进度查询" key="progress">
        <ProgressList :progress="progress" />
      </a-tab-pane>
      <a-tab-pane key="outer12346" tab="样品领用记录">
        <warehouseInOutSubTable :articleNo="model?.sampleNo" :operationTypeId="out" />
      </a-tab-pane>
    </a-tabs>
    <TranslationModal @register="registerTranslationModal" @success="handleSuccess" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, FormSchema, useForm } from '/@/components/Form';
  import { formSchema } from '../Sample.data';
  import { copySampleByLotNo, loadTreeData, saveOrUpdateDict } from '../Sample.api';
  import { listForAutoComplete } from '/@/api/sys/custom';
  import TestTaskTable from '@/views/lims_order/TestTaskTable.vue';
  import warehouseInOutSubTable from '@/views/warehouse/Subtable/warehouseInOutSubTable.vue';
  import AttachTable from '@/views/lims_core/attachment/AttachTable.vue';
  import dayjs from 'dayjs';
  import { useUserStore } from '@/store/modules/user';
  import { message } from 'ant-design-vue';
  import StandardMaterialTable from '@/views/lims_core/sm_table/StandardMaterialTable.vue';
  import ColumnConsumTable from '@/views/lims_core/column_consum/ColumnConsumTable.vue';
  import ProgressList from '@/views/lims_order/components/ProgressList.vue';
  import { listProgress } from '@/views/lims_order/TestTaskFlow.api';
  import TranslationModal from '@/views/lims_core/components/TranslationModal.vue';
  import { useModal } from '@/components/Modal';
  // 获取emit
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const expandedRowKeys = ref([]);
  const treeData = ref([]);
  const activeKey = ref('info');
  const userStore = useUserStore();
  const progress = ref([]);
  // 当前编辑的数据
  let model: Nullable<Recordable> = null;
  const sample = ref({});
  const out = ref('out');

  const [registerTranslationModal, { openModal }] = useModal();

  const handleTranslate = async () => {
    if (sample.value.id) {
      let values = await validate();
      await saveOrUpdateDict(values, isUpdate.value);

      openModal(true, {
        baseTable: 'sample',
        baseId: sample.value.id,
        recordvalue: { ...values },
        formSchema: formSchema,
      });
    } else {
      message.warning('请先保存样品信息');
    }
  };

  const isDisabled = ref(false);
  const subFormSchema: FormSchema[] = [
    {
      label: '报价单号',
      field: 'quotationId',
      component: 'JDictSelectTag',
      componentProps: {
        dictCode: 'quotation,quotation_no,id',
      },
      dynamicRules: ({ model, schema }) => {
        return [{ required: true, message: '请输入报价单号!' }];
      },
      show: false,
    },
    {
      label: '合同单号',
      field: 'orderId',
      component: 'JDictSelectTag',
      componentProps: {
        dictCode: 'biz_order,contract_no,id',
      },
      show: false,
    },
    {
      label: '编号',
      field: 'sampleNo',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      label: '产品名称',
      field: 'name',
      component: 'Input',
      dynamicRules: ({ model, schema }) => {
        return [{ required: true, message: '请输入产品名称!' }];
      },
    },
    {
      label: '批号',
      field: 'lotNo',
      component: 'Input',
    },
    {
      label: '规格',
      field: 'specification',
      component: 'AutoComplete',
    },
    {
      label: '生产厂家',
      field: 'manufacturer',
      component: 'AutoComplete',
    },
    {
      label: '贮藏',
      field: 'storage',
      component: 'Input',
    },
    {
      label: '仓库库位',
      field: 'warehouseLocation',
      component: 'Input',
    },
    {
      label: '父级节点',
      field: 'pid',
      component: 'JTreeSelect',
      componentProps: {
        dict: 'sample,name,id',
        pidField: 'pid',
        pidValue: '0',
        hasChildField: 'has_child',
      },
      show: false,
    },
    // TODO 主键隐藏字段，目前写死为ID
    {
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
  ];
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, updateSchema, resetSchema, scrollToField }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    labelCol: {
      xs: { span: 24 },
      sm: { span: 8 },
    },
    wrapperCol: {
      xs: { span: 24 },
      sm: { span: 16 },
    },
  });

  async function autoComplete(key) {
    updateSchema({
      field: key,
      component: 'AutoComplete',
      componentProps: {
        onSearch: async (value) => {
          await listForAutoComplete({ table: 'sample', key: key, value: value }).then((result) => {
            console.log(result);
            updateSchema({
              field: key,
              componentProps: {
                options: result.slice(0, 3),
              },
            });
          });
        },
      },
    });
  }

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    //重置表单
    await resetFields();

    setProps({ disabled: !userStore.getUserInfo.roles.includes('register') });
    isDisabled.value = !!data?.hideFooter;
    activeKey.value = 'info';
    expandedRowKeys.value = [];
    setDrawerProps({ confirmLoading: false, minHeight: 80, showOkBtn: userStore.getUserInfo.roles.includes('register') });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    if (data?.record) {
      if (data?.record.entrustDate == null) {
        data.record.entrustDate = dayjs().format('YYYY-MM-DD');
      }
      if (data?.record.receiveDate == null) {
        data.record.receiveDate = dayjs().format('YYYY-MM-DD');
      }
      if (data?.record.acceptor == null) {
        data.record.acceptor = userStore.getUserInfo.username;
      }
      model = data.record;
      sample.value = data.record;
      console.log(data.record);
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      console.log('表单数据', data.record);
      if (data.record.pid != '0') {
        resetSchema(subFormSchema);
        setProps({ baseColProps: 24 });
      }

      updateSchema({
        label: '研发项目编码',
        field: 'rdId',
        component: 'JDictSelectTag',
        componentProps: {
          dictCode: 'rd_project where quotation_id=' + data.record.quotationId + ',rd_no,id',
        },
      });
    } else {
      model = null;
    }
    console.log(data?.record.receiveDate);
    console.log(dayjs().format('YYYY-MM-DD'));

    //updateSchema
    if (!data?.hideFooter) {
      await autoComplete('specification');
      await autoComplete('manufacturer');
      await autoComplete('composition');
      await autoComplete('properties');
      await autoComplete('storage');
      await autoComplete('expiry');
      await autoComplete('maxDailyIntake');
      await autoComplete('treatmentCycle');
      await autoComplete('phValue');
      await autoComplete('warehouseLocation');
      await autoComplete('packMedicineType');
      await autoComplete('lotNo');
      await autoComplete('origin');
      await autoComplete('certificateNo');
      await autoComplete('authorizeNo');
    }

    //父级节点树信息
    treeData.value = await loadTreeData({ async: false, pcode: '' });
    // 隐藏底部时禁用整个表单
  });
  //设置标题
  const getTitle = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));

  /**
   * 根据pid获取展开的节点
   * @param pid
   * @param arr
   */
  function getExpandKeysByPid(pid, arr) {
    if (pid && arr && arr.length > 0) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].key == pid && unref(expandedRowKeys).indexOf(pid) < 0) {
          expandedRowKeys.value.push(arr[i].key);
          getExpandKeysByPid(arr[i]['parentId'], unref(treeData));
        } else {
          getExpandKeysByPid(pid, arr[i].children);
        }
      }
    }
  }
  //表单提交事件
  async function handleSubmit() {
    activeKey.value = 'info';
    try {
      let values = await validate();
      setDrawerProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdateDict(values, isUpdate.value);
      //关闭弹窗
      closeDrawer();
      //展开的节点信息
      await getExpandKeysByPid(values['pid'], unref(treeData));
      //刷新列表(isUpdate:是否编辑;values:表单信息;expandedArr:展开的节点信息)
      emit('success', {
        isUpdate: unref(isUpdate),
        values: { ...values },
        expandedArr: unref(expandedRowKeys).reverse(),
        // 是否更改了父级节点
        changeParent: model != null && model['pid'] != values['pid'],
      });
    } catch ({ errorFields }) {
      console.log(111122222);
      message.error('表单验证失败，请检查输入的内容');
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
  async function handleCopySample() {
    try {
      let values = await validate();
      setDrawerProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdateDict(values, isUpdate.value);
      //复制表单
      await copySampleByLotNo({ id: values.id }, handleSuccess);
    } catch (e) {
      message.error('表单验证失败，请检查输入的内容');
    }
  }
  function handleSuccess() {
    activeKey.value = 'info';
    closeDrawer();
    emit('success');
  }
  //tab切换事件
  async function onTabChange(key) {
    if (key != 'info' && !isDisabled.value) {
      try {
        let values = await validate();
        //sample.value 跟 values 对比,values中的值跟sample.value中的值不一致时,sample的值可能多一些
        let needUpdate = false;
        for (let k in values) {
          if (values[k] != sample.value[k]) {
            needUpdate = true;
            break;
          }
        }
        if (needUpdate) {
          await saveOrUpdateDict(values, isUpdate.value);
          for (let k in values) {
            if (values[k] != sample.value[k]) {
              sample.value[k] = values[k];
            }
          }
        }
      } catch (e) {
        console.log(e);
        activeKey.value = 'info';
        message.error('请完善必填内容再切换!!!');
      }
    }
    if (key == 'progress') {
      setDrawerProps({ loading: true });
      await listProgress({ sampleId: model.id })
        .then((res) => {
          progress.value = res;
        })
        .finally(() => {
          setDrawerProps({ loading: false });
        });
    }
  }
</script>
<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
