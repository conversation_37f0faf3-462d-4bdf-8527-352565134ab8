<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <BasicForm @register="registerForm" ref="formRef" name="SysWorkflowForm" />
    <!-- 子表单区域 -->
    <a-card v-if="stepList.length > 0">
      <a-tabs activeKey="display" animated @change="handleChangeTabs">
        <a-tab-pane tab="环节展示" key="display" :forceRender="true">
          <a-steps v-model:current="current" :items="stepList">
            <template #progressDot="{ index, status, prefixCls }">
              <a-popover>
                <template #content>
                  <span>step {{ index }} status: {{ status }}</span>
                </template>
                <span :class="`${prefixCls}-icon-dot`"></span>
              </a-popover>
            </template>
          </a-steps>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <a-tab-pane tab="流程环节" key="sysWorkflowStep" :forceRender="true">
        <JVxeTable
          dragSort
          sortKey="sortNum"
          :sortBegin="1"
          dragSortFixed="none"
          keep-source
          resizable
          ref="sysWorkflowStep"
          :loading="sysWorkflowStepTable.loading"
          :columns="sysWorkflowStepTable.columns"
          :dataSource="sysWorkflowStepTable.dataSource"
          :height="340"
          :rowSelection="true"
          :disabled="formDisabled"
          :toolbar="true"
          @dragged="onDragged"
          @value-change="onValueChange"
        />
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { formSchema, sysWorkflowStepColumns } from '../SysWorkflow.data';
  import { saveOrUpdate, sysWorkflowStepList } from '../SysWorkflow.api';
  import { useUserStore } from '@/store/modules/user';
  import { initDictOptions } from '@/utils/dict';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const current = ref('1');
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const refKeys = ref(['sysWorkflowStep']);
  const activeKey = ref('sysWorkflowStep');
  const userStore = useUserStore();
  const sysWorkflowStep = ref();
  const tableRefs = { sysWorkflowStep };
  const stepItemList = ref([]);
  const sysWorkflowStepTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysWorkflowStepColumns,
  });
  const stepList = ref([]);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;

    await initDictOptions('flow_step').then((res) => {
      console.log(res);
      stepItemList.value = res;
    });

    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      requestSubTableData(sysWorkflowStepList, { id: data?.record?.id }, sysWorkflowStepTable, fetchSuccess);
    }

    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );

  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

  async function reset() {
    await resetFields();
    activeKey.value = 'sysWorkflowStep';
    sysWorkflowStepTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);
    return {
      ...main, // 展开
      sysWorkflowStepList: allValues.tablesValue[0].tableData,
    };
  }
  //表单提交事件
  async function requestAddOrEdit(values) {
    try {
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  function onDragged({ oldIndex, newIndex }) {
    console.log('onValueChange', oldIndex, newIndex);
    stepList.value = sysWorkflowStep.value.getTableData().map((item) => {
      return { title: stepItemList.value.find((step) => step.value === item.name)?.text, description: item.description };
    });
    current.value = (sysWorkflowStep.value.getTableData().length - 1).toString();
  }
  function fetchSuccess(res) {
    if (res.result.length === 0) {
      stepList.value = [];
    }

    stepList.value = res.result.map((item) => {
      return { title: stepItemList.value.find((step) => step.value === item.name)?.text, description: item.description };
    });
    current.value = (res.result.length - 1).toString();
  }
  function onValueChange(data) {
    console.log('onValueChange', data);
    stepList.value = sysWorkflowStep.value.getTableData().map((item) => {
      return { title: stepItemList.value.find((step) => step.value === item.name)?.text, description: item.description };
    });
    current.value = (sysWorkflowStep.value.getTableData().length - 1).toString();
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
