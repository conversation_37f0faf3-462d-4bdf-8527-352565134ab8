import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import {useUserStore} from "@/store/modules/user";
//列表数据
const userStore = useUserStore();
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '编号',
    align:"center",
    dataIndex: 'code'
   },
   {
    title: '溶液类型',
    align:"center",
    dataIndex: 'solutionTypeId_dictText'
   },
   {
    title: '状态',
    align:"center",
    dataIndex: 'status_dictText'
   },
   {
    title: '配置记录',
    align:"center",
    dataIndex: 'record'
   },
   {
    title: '溶液浓度',
    align:"center",
    dataIndex: 'conc'
   },
   {
    title: '称样量',
    align:"center",
    dataIndex: 'weighing'
   },
   {
    title: '含量',
    align:"center",
    dataIndex: 'content'
   },
   {
    title: '定容体积',
    align:"center",
    dataIndex: 'constantVolume'
   },
   {
    title: '移取体积',
    align:"center",
    dataIndex: 'removeVolume'
   },
   {
    title: '配置人',
    align:"center",
    dataIndex: 'createBy_dictText'
   },
   {
    title: '配置日期',
    align:"center",
    dataIndex: 'createTime'
   },
   {
    title: '有效期',
    align:"center",
    dataIndex: 'effectiveTo'
   },
   {
    title: '保存条件',
    align:"center",
    dataIndex: 'storeCondition'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "编号",
      field: 'code',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "溶液类型",
      field: 'solutionTypeId',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"solution_type"
      },
      //colProps: {span: 6},
 	},
	{
      label: "状态",
      field: 'status',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"solution_status_type"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '溶液类型',
    field: 'solutionTypeId',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"solution_type"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入溶液类型!'},
          ];
     },
  },
  {
    label: '配置记录',
    field: 'record',
    component: 'Input',
  },
  {
    label: '溶液浓度',
    field: 'conc',
    component: 'Input',
  },
  {
    label: '称样量',
    field: 'weighing',
    component: 'Input',
  },
  {
    label: '含量',
    field: 'content',
    component: 'Input',
  },
  {
    label: '定容体积',
    field: 'constantVolume',
    component: 'Input',
  },
  {
    label: '移取体积',
    field: 'removeVolume',
    component: 'Input',
  },
  {
    label: '配置人',
    field: 'createBy',
    component: 'JSelectUser',
    defaultValue: userStore.getUserInfo.username,
    componentProps:{
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入配置人!'},
          ];
     },
  },
  {
    label: '配置日期',
    field: 'createTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入配置日期!'},
          ];
     },
  },
  {
    label: '有效期',
    field: 'effectiveTo',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入有效期!'},
          ];
     },
  },
  {
    label: '保存条件',
    field: 'storeCondition',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  code: {title: '编号',order: 0,view: 'text', type: 'string',},
  solutionTypeId: {title: '溶液类型',order: 1,view: 'list', type: 'string',dictCode: 'solution_type',},
  status: {title: '状态',order: 2,view: 'list', type: 'string',dictCode: 'solution_status_type',},
  record: {title: '配置记录',order: 3,view: 'text', type: 'string',},
  conc: {title: '溶液浓度',order: 4,view: 'text', type: 'string',},
  weighing: {title: '称样量',order: 5,view: 'text', type: 'string',},
  content: {title: '含量',order: 6,view: 'text', type: 'string',},
  constantVolume: {title: '定容体积',order: 7,view: 'text', type: 'string',},
  removeVolume: {title: '移取体积',order: 8,view: 'text', type: 'string',},
  createBy: {title: '配置人',order: 9,view: 'sel_user', type: 'string',},
  createTime: {title: '配置日期',order: 10,view: 'datetime', type: 'string',},
  effectiveTo: {title: '有效期',order: 11,view: 'datetime', type: 'string',},
  storeCondition: {title: '保存条件',order: 12,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
