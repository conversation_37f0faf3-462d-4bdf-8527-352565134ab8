import { reactive, ref, Ref, unref, nextTick, h } from 'vue';
import { merge } from 'lodash-es';
import type { BasicTableProps, TableActionType, BasicColumn } from '/@/components/Table';
import { useTable } from '/@/components/Table';
import type { DynamicProps } from '/#/utils';
import type { FormActionType } from '/@/components/Form/src/types/form';
import { useMessage } from '/@/hooks/web/useMessage';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMethods } from '/@/hooks/system/useMethods';
import { filterObj } from '/@/utils/common/compUtils';
const { handleExportXls, handleImportXls } = useMethods();

// 定义 useListPage 方法所需参数
interface ListPageOptions {
  // 样式作用域范围
  designScope?: string;
  // 【必填】表格参数配置
  tableProps: TableProps;
  // 是否分页
  pagination?: boolean;
  // 导出配置
  exportConfig?: {
    url: string | (() => string);
    // 导出文件名
    name?: string | (() => string);
    //导出参数
    params?: object;
  };
  // 导入配置
  importConfig?: {
    //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
    url: string | (() => string);
    //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
    // 导出成功后的回调
    success?: (fileInfo?: any) => void;
  };
}

interface IDoRequestOptions {
  // 是否显示确认对话框，默认 true
  confirm?: boolean;
  // 是否自动刷新表格，默认 true
  reload?: boolean;
  // 是否自动清空选择，默认 true
  clearSelection?: boolean;
}

/**
 * listPage页面公共方法
 *
 * @param options
 */
export function useListPage(options: ListPageOptions) {
  const $message = useMessage();
  let $design = {} as ReturnType<typeof useDesign>;
  if (options.designScope) {
    $design = useDesign(options.designScope);
  }
  const tableContext = useListTable(options.tableProps);

  const [, { getForm, reload, setLoading, setColumns }, { selectedRowKeys }] = tableContext;

  // 导出 excel
  async function onExportXls() {
    //update-begin---author:wangshuai ---date:20220411  for：导出新增自定义参数------------
    const { url, name, params } = options?.exportConfig ?? {};
    const realUrl = typeof url === 'function' ? url() : url;
    if (realUrl) {
      const title = typeof name === 'function' ? name() : name;
      //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导出报错，原因未知-
      let paramsForm: any = {};
      try {
        paramsForm = await getForm().validate();
      } catch (e) {
        console.error(e);
      }
      //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导出报错，原因未知-

      //update-begin-author:liusq date:20230410 for:[/issues/409]导出功能没有按排序结果导出,设置导出默认排序，创建时间倒序
      if (!paramsForm?.column) {
        Object.assign(paramsForm, { column: 'createTime', order: 'desc' });
      }
      //update-begin-author:liusq date:20230410 for: [/issues/409]导出功能没有按排序结果导出,设置导出默认排序，创建时间倒序

      //如果参数不为空，则整合到一起
      //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导出动态设置mainId
      if (params) {
        Object.keys(params).map((k) => {
          const temp = (params as object)[k];
          if (temp) {
            paramsForm[k] = unref(temp);
          }
        });
      }
      //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导出动态设置mainId
      if (selectedRowKeys.value && selectedRowKeys.value.length > 0) {
        paramsForm['selections'] = selectedRowKeys.value.join(',');
      }
      console.log();
      return handleExportXls(title as string, realUrl, filterObj(paramsForm));
      //update-end---author:wangshuai ---date:20220411  for：导出新增自定义参数--------------
    } else {
      $message.createMessage.warn('没有传递 exportConfig.url 参数');
      return Promise.reject();
    }
  }

  // 导入 excel
  function onImportXls(file) {
    const { url, success } = options?.importConfig ?? {};
    //update-begin-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
    const realUrl = typeof url === 'function' ? url() : url;
    if (realUrl) {
      return handleImportXls(file, realUrl, success || reload);
      //update-end-author:taoyan date:20220507 for: erp代码生成 子表 导入地址是动态的
    } else {
      $message.createMessage.warn('没有传递 importConfig.url 参数');
      return Promise.reject();
    }
  }

  /**
   * 通用请求处理方法，可自动刷新表格，自动清空选择
   * @param api 请求api
   * @param options 是否显示确认框
   */
  function doRequest(api: () => Promise<any>, options?: IDoRequestOptions) {
    return new Promise((resolve, reject) => {
      const execute = async () => {
        try {
          setLoading(true);
          const res = await api();
          if (options?.reload ?? true) {
            reload();
          }
          if (options?.clearSelection ?? true) {
            selectedRowKeys.value = [];
          }
          resolve(res);
        } catch (e) {
          reject(e);
        } finally {
          setLoading(false);
        }
      };
      if (options?.confirm ?? true) {
        $message.createConfirm({
          iconType: 'warning',
          title: '删除',
          content: '确定要删除吗？',
          onOk: () => execute(),
          onCancel: () => reject(),
        });
      } else {
        execute();
      }
    });
  }

  /** 执行单个删除操作 */
  function doDeleteRecord(api: () => Promise<any>) {
    return doRequest(api, { confirm: false, clearSelection: false });
  }

  return {
    ...$design,
    ...$message,
    onExportXls,
    onImportXls,
    doRequest,
    doDeleteRecord,
    tableContext,
  };
}

// 定义表格所需参数
type TableProps = Partial<DynamicProps<BasicTableProps>>;
type UseTableMethod = TableActionType & {
  getForm: () => FormActionType;
};

/**
 * useListTable 列表页面标准表格参数
 *
 * @param tableProps 表格参数
 */
export function useListTable(tableProps: TableProps): [
  (instance: TableActionType, formInstance: UseTableMethod) => void,
  TableActionType & {
    getForm: () => FormActionType;
  },
  {
    rowSelection: any;
    selectedRows: Ref<Recordable[]>;
    selectedRowKeys: Ref<any[]>;
  },
] {
  let tableInstance: TableActionType;
  // 生成列的过滤选项
  function generateFilters(data: Recordable[], dataIndex: string): { text: string; value: string }[] {
    if (!data?.length || !dataIndex) {
      console.log('[Filter Debug] No data or dataIndex, returning empty array');
      return [];
    }

    const uniqueValues = new Map<string, string>();

    data.forEach((item, index) => {
      if (item) {
        if (dataIndex in item) {
          const value = item[dataIndex];
          if (value !== null && value !== undefined && value !== '') {
            const strValue = String(value).trim();
            uniqueValues.set(strValue, strValue);
          }
        } else {
          // 尝试通过点号分割的路径访问嵌套属性
          const keys = dataIndex.split('.');
          let nestedValue = item;
          for (const key of keys) {
            if (nestedValue && typeof nestedValue === 'object') {
              nestedValue = nestedValue[key];
            } else {
              nestedValue = undefined;
              break;
            }
          }
          if (nestedValue !== null && nestedValue !== undefined && nestedValue !== '') {
            const strValue = String(nestedValue).trim();
            uniqueValues.set(strValue, strValue);
          }
        }
      }
    });

    const filters = Array.from(uniqueValues.values())
      .map((value) => ({
        text: value,
        value: value,
      }))
      .sort((a, b) => a.text.localeCompare(b.text, 'zh-CN'));
    return filters;
  }

  // 判断是否需要为该列生成过滤器
  function shouldGenerateFilter(dataIndex: string | undefined, column: BasicColumn): boolean {
    const excludeColumns = ['id', 'action']; // 默认排除的列

    if (!dataIndex || excludeColumns.includes(dataIndex)) {
      return false;
    }

    // 如果该列已经配置了 filters,则不自动生成
    return !column.filters || column.filters.length === 0;
  } // 基础配置
  interface ExtendedBasicTableProps extends Partial<BasicTableProps> {
    needUpdateFilters?: boolean;
    onDataSourceChange?: (dataSource: any[]) => void;
  }

  // 初始化本地表格属性
  const localTableProps = tableProps || {};

  const defaultTableProps: ExtendedBasicTableProps = {
    rowKey: 'id',
    // 触发数据源改变
    onDataSourceChange: (dataSource) => {
      console.log('[Filter] DataSource changed:', dataSource);
      if (defaultTableProps.columns) {
        const columns = defaultTableProps.columns;
        columns.forEach((column: BasicColumn) => {
          if (column.dataIndex && shouldGenerateFilter(column.dataIndex.toString(), column)) {
            const filters = generateFilters(dataSource, column.dataIndex.toString());
            if (filters.length > 0) {
              column.filters = filters;
            }
          }
        });
      }
    },
    // 使用查询条件区域
    useSearchForm: true,
    // 查询条件区域配置
    formConfig: {
      // 紧凑模式
      compact: true,
      // label默认宽度
      // labelWidth: 120,
      // 按下回车后自动提交
      autoSubmitOnEnter: true,
      // 默认 row 配置
      rowProps: { gutter: 8 },
      // 默认 col 配置
      baseColProps: {
        xs: 24, // <576px
        sm: 12, // ≥576px
        md: 12, // ≥768px
        lg: 8, // ≥992px
        xl: 8, // ≥1200px
        xxl: 6, // ≥1600px
      },
      labelCol: {
        xs: 24,
        sm: 8,
        md: 6,
        lg: 8,
        xl: 6,
        xxl: 6,
      },
      wrapperCol: {},
      // 是否显示 展开/收起 按钮
      showAdvancedButton: true,
      // 超过指定列数默认折叠
      autoAdvancedCol: 3,
      // 操作按钮配置
      actionColOptions: {
        xs: 24, // <576px
        sm: 12, // ≥576px
        md: 12, // ≥768px
        lg: 8, // ≥992px
        xl: 8, // ≥1200px
        xxl: 6, // ≥1600px
        style: { textAlign: 'left' },
      },
    },
    // 斑马纹
    striped: true,
    // 是否可以自适应高度
    canResize: true,
    // 表格最小高度
    // update-begin--author:liaozhiyang---date:20240603---for【TV360X-861】列表查询区域不可往上滚动
    minHeight: 300,
    // update-end--author:liaozhiyang---date:20240603---for【TV360X-861】列表查询区域不可往上滚动
    // 点击行选中
    clickToRowSelect: false,
    // 是否显示边框
    bordered: true,
    // 是否显示序号列
    showIndexColumn: false,
    // 显示表格设置
    showTableSetting: true,
    // 表格全屏设置
    tableSetting: {
      fullScreen: false,
    },
    // 是否显示操作列
    showActionColumn: true,
    // 操作列
    actionColumn: {
      width: 120,
      title: '操作',
      //是否锁定操作列取值 right ,left,false
      fixed: false,
      dataIndex: 'action',
      slots: { customRender: 'action' },
    },
  };

  // 合并用户个性化配置
  if (tableProps) {
    if (tableProps.formConfig) {
      setTableProps(tableProps.formConfig);
    }
    merge(defaultTableProps, tableProps); // 处理列的过滤配置
    const columns = defaultTableProps.columns;
    if (Array.isArray(columns)) {
      defaultTableProps.columns = columns.map((column) => {
        if (column.dataIndex && shouldGenerateFilter(column.dataIndex.toString(), column)) {
          // 添加过滤相关配置
          const columnWithFilter: BasicColumn = {
            ...column,
            filterMultiple: true,
            filters: [], // 初始为空数组,数据加载后会更新
            filterMode: 'menu',
            filterSearch: true,
            onFilter: (value: string, record: Recordable) => {
              // 过滤数据行时使用字符串包含
              const recordValue = record[column.dataIndex as string];
              return recordValue != null && String(recordValue).includes(value);
            },
          };
          return columnWithFilter;
        }
        return column;
      });
    }
  }
  // 发送请求之前调用的方法
  function beforeFetch(params) {
    // 设置需要更新过滤器的标志
    defaultTableProps.needUpdateFilters = true;
    // 默认以 createTime 降序排序
    return Object.assign({ column: 'createTime', order: 'desc' }, params);
  }

  // 请求完成后的处理
  function afterFetch(inputData: any): any {
    try {
      // 处理返回的数据，生成过滤器
      const records = Array.isArray(inputData) ? inputData : inputData?.records || [];
      if (records.length > 0 && defaultTableProps.columns) {
        const columns = [...defaultTableProps.columns];
        let hasUpdates = false;

        // 遍历每一列并尝试生成过滤器
        for (let i = 0; i < columns.length; i++) {
          const column = columns[i];
          if (column.dataIndex && shouldGenerateFilter(column.dataIndex.toString(), column)) {
            const filters = generateFilters(records, column.dataIndex.toString());
            if (filters.length > 0) {
              hasUpdates = true;
              columns[i] = {
                ...column,
                filters: filters,
                filterMultiple: true,
                filterMode: 'menu',
                filterSearch: true,
                onFilter: (value: string, record: Recordable) => {
                  // 过滤数据行时使用字符串包含
                  const recordValue = record[column.dataIndex as string];
                  return recordValue != null && String(recordValue).includes(value);
                },
              };
            }
          }
        } // 如果有更新，强制更新列的配置
        if (hasUpdates) {
          defaultTableProps.columns = [...columns];
          nextTick(async () => {
            if (tableInstance && tableInstance.setColumns) {
              await tableInstance.setColumns(columns);
            } else {
              console.log('[Filter Debug] Table instance not found');
            }
          });
        }
      }
      return inputData;
    } catch (error) {
      console.error('[Filter] Error processing filters:', error);
      return inputData;
    }
  }
  // 配置请求拦截器
  const fetchHandlers = {
    before: beforeFetch,
    after: afterFetch,
  };

  // 合并用户自定义的处理器
  if (localTableProps.beforeFetch) {
    const userBeforeFetch = unref(localTableProps.beforeFetch);
    defaultTableProps.beforeFetch = function (params: any) {
      // 先执行用户的 beforeFetch
      if (typeof userBeforeFetch === 'function') {
        const userParams = userBeforeFetch(params) || params; // 如果用户函数没有返回值，使用原始参数
        // 再执行内置的 beforeFetch
        params = fetchHandlers.before(userParams);
        return params;
      }
      return fetchHandlers.before(params);
    };
  } else {
    defaultTableProps.beforeFetch = fetchHandlers.before;
  }
  if (localTableProps.afterFetch) {
    const userAfterFetch = unref(localTableProps.afterFetch);
    defaultTableProps.afterFetch = async function (data: any) {
      // 先执行内置的 afterFetch 获取基础处理后的数据
      data = await fetchHandlers.after(data);
      // 再执行用户的 afterFetch 进行自定义处理
      if (typeof userAfterFetch === 'function') {
        data = await userAfterFetch(data);
      }
      return data;
    };
  } else {
    defaultTableProps.afterFetch = fetchHandlers.after;
  } // 当前选择的行
  const selectedRowKeys = ref<any[]>([]);
  // 选择的行记录
  const selectedRows = ref<any[]>([]);

  // 表格选择列配置
  const rowSelection = localTableProps.rowSelection ?? {};
  const defaultRowSelection = reactive({
    ...rowSelection,
    type: rowSelection.type ?? 'checkbox',
    // 选择列宽度，默认 50
    columnWidth: rowSelection.columnWidth ?? 50,
    selectedRows: selectedRows,
    selectedRowKeys: selectedRowKeys,
    onChange(...args: any[]) {
      selectedRowKeys.value = args[0];
      selectedRows.value = args[1];
      if (typeof rowSelection.onChange === 'function') {
        rowSelection.onChange(...args);
      }
    },
  });
  if (defaultTableProps.rowSelection) {
    delete defaultTableProps.rowSelection;
  }

  /**
   * 设置表格参数
   *
   * @param formConfig
   */
  function setTableProps(formConfig: any) {
    const replaceAttributeArray: string[] = ['baseColProps', 'labelCol'];
    for (const item of replaceAttributeArray) {
      if (formConfig && formConfig[item]) {
        if (defaultTableProps.formConfig) {
          const defaultFormConfig: any = defaultTableProps.formConfig;
          defaultFormConfig[item] = formConfig[item];
        }
        formConfig[item] = {};
      }
    }
  }
  // 使用 useTable
  const [tableReg, tableInst] = useTable(defaultTableProps);

  return [
    (table: TableActionType, form: UseTableMethod) => {
      tableInstance = table;
      tableReg(table, form);
    },
    tableInst,
    {
      selectedRows,
      selectedRowKeys,
      rowSelection: defaultRowSelection,
    },
  ];
}
