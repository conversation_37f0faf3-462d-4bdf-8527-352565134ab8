<template>
  <div>
    <!--引用表格-->
    <BasicTable
      @register="registerTable"
      :rowSelection="isLocked ? null : rowSelection"
      ref="tableRef"
    >
      <!--插槽:table标题-->
      <template #tableTitle v-if="!isLocked">
        <a-button type="primary" v-auth="'lims_order:sample:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_order:sample:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }" v-if="!isLocked">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <ReportModal @register="registerModal" @callback="handleSampleModalCallback" />
  </div>
</template>

<script lang="tsx" setup>
  import { ref, reactive, computed, unref, watch } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, searchFormSchema, superQuerySchema } from '../Report.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from '../Report.api';
  import { useUserStore } from '/@/store/modules/user';
  import { defineProps, defineEmits } from 'vue';
  import ReportModal from "@/views/lims_order/components/ReportModal.vue";

  /**
   * props声明
   */
  const props = defineProps({
    isLocked: {
      type: Boolean,
      required: false,
    },
    orderId: {
      type: [String, Number],
      default: 'New',
    },
    bizTypeId: {
      type: String,
    },
  });

  // Define emits
  const emit = defineEmits(['sampleModalCallback']);

  //表格实例
  const tableRef = ref();

  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const expandedSampleId = ref(null);
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'report_table',
      },
      title: '报告',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.orderId = props.orderId;
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '样品',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   * 新增事件
   */
  function handleAdd() {
    console.log('bizTypeId', props.bizTypeId);
    openModal(true, {
      bizTypeId: props.bizTypeId,
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    record.initial_productIds = record.productIds;
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    // await deleteOne({id: record.id}, handleSuccess);
    tableRef.value.deleteTableDataRecord(record.id);
    calcPrice();
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    // await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
    selectedRowKeys.value.forEach((id) => {
      tableRef.value.deleteTableDataRecord(id);
    });
    calcPrice();
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
    calcPrice();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_order:sample:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_order:sample:delete',
      },
    ];
  }

  /**
   * 处理SampleModal回调事件
   */
  const handleSampleModalCallback = (values) => {
    console.log('tableRef.value', tableRef.value);
    if (values.id) {
      tableRef.value.updateTableDataRecord(values.id, values);
    } else {
      console.log('SampleModal回调事件', values);
      values.id = (tableRef.value.getDataSource().length + 1).toString();
      values.orderId = props.orderId;
      tableRef.value.insertTableDataRecord(values);
      console.log('tableRef', tableRef.value.getDataSource());
    }
    calcPrice();
  };

  function calcPrice() {
    let pmPrice = tableRef.value.getDataSource().reduce((total, item) => total + item.pmPrice, 0);
    let applyPrice = tableRef.value.getDataSource().reduce((total, item) => total + item.applyPrice, 0);
    //拿到最大leadtime
    let leadTime = Math.max(...tableRef.value.getDataSource().map((item) => item.pmLeadTime));
    console.log('calcPrice', pmPrice, applyPrice);
    emit('sampleModalCallback', { pmPrice, applyPrice, leadTime });
  }

  function getData() {
    return tableRef.value.getDataSource();
  }

  //监听props.quotationId
  watch(
    () => props.orderId,
    (value) => {
      queryParam.quotationId = value;
      reload();
    }
  );
  watch(
    () => props.bizTypeId,
    (value) => {
      console.log('bizTypeId', value);
    }
  );

  defineExpose({
    getData: getData,
  });




</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
