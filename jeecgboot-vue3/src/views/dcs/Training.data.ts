import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '培训名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status_dictText',
  },
  {
    title: '课件',
    align: 'center',
    dataIndex: 'courseware',
  },
  {
    title: '开始时间',
    align: 'center',
    dataIndex: 'startTime',
  },
  {
    title: '培训时长(秒)',
    align: 'center',
    dataIndex: 'duration',
  },
  {
    title: '培训地点',
    align: 'center',
    dataIndex: 'location',
  },
  {
    title: '会议id',
    align: 'center',
    dataIndex: 'meetingIds',
  },
  {
    title: '对应文件',
    align: 'center',
    dataIndex: 'docIds_dictText',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '培训名称',
    field: 'name',
    component: 'Input', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '开始时间',
    field: 'startTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '培训名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入培训名称!' }];
    },
  },
  {
    label: '课件',
    field: 'courseware',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '开始时间',
    field: 'startTime',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入开始时间!' }];
    },
  },
  {
    label: '培训时长(秒)',
    field: 'duration',
    defaultValue: 3600,
    component: 'InputNumber',
    componentProps: {
      addonAfter: '秒',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入培训时长(秒)!' }];
    },
  },
  {
    label: '培训地点',
    field: 'location',
    defaultValue: '6',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'meeting_room',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入培训地点!' }];
    },
  },
  {
    label: '文控文件',
    field: 'docIds',
    component: 'JPopupDict',
    componentProps: {
      placeholder: '请选择文控文件',
      dictCode: 'dcs_doc_selector,doc_no,id,,',
      multi: true,
    },
    //colProps: {span: 6},
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表表格配置
export const trainingRecordColumns: JVxeColumn[] = [
  {
    title: '受训人',
    key: 'attendee',
    type: JVxeTypes.selectDictSearch,
    async: true, // 异步搜索，默认为 true
    // 字典表配置信息：数据库表名,显示字段名,存储字段名
    dict: 'sys_user,realname,id',
    tipsContent: '请输入查询条件',
    props: {},
    width: '100px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '签到时间',
    key: 'checkinTime',
    type: JVxeTypes.datetime,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '确认人',
    key: 'checkedBy',
    type: JVxeTypes.selectDictSearch,
    dict: 'sys_user,realname,id',
    width: '100px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '确认时间',
    key: 'checkedTime',
    type: JVxeTypes.datetime,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '培训名称', order: 0, view: 'text', type: 'string' },
  status: { title: '状态', order: 1, view: 'sel_search', type: 'string', dictCode: 'training_status' },
  courseware: { title: '课件', order: 2, view: 'file', type: 'string' },
  startTime: { title: '开始时间', order: 3, view: 'datetime', type: 'string' },
  duration: { title: '培训时长(秒)', order: 4, view: 'number', type: 'number' },
  meetingIds: { title: '会议id', order: 5, view: 'checkbox', type: 'string', dictCode: '' },
  docIds: { title: '对应文件', order: 6, view: 'popup_dict', type: 'string' },
  //子表高级查询
  trainingRecord: {
    title: '培训记录',
    view: 'table',
    fields: {
      attendee: { title: '受训人', order: 0, view: 'sel_user', type: 'string' },
      checkinTime: { title: '签到时间', order: 1, view: 'datetime', type: 'string' },
      checkedBy: { title: '确认人', order: 2, view: 'sel_user', type: 'string' },
      checkedTime: { title: '确认时间', order: 3, view: 'datetime', type: 'string' },
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
