export default {
  panels: [
    {
      index: 0,
      name: 1,
      height: 40,
      width: 60,
      paperHeader: 49.5,
      paperFooter: 105,
      printElements: [
        {
          options: {
            left: 0,
            top: 0,
            height: 102,
            width: 168,
            right: 168,
            bottom: 90,
            vCenter: 84,
            hCenter: 45,
          },
          printElementType: {
            title: '矩形',
            type: 'rect',
          },
        },
        {
          options: {
            left: 0,
            top: 12,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 24,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 36,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 48,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 60,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 72,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 32,
            top: 0,
            height: 72,
            width: 9,
          },
          printElementType: {
            title: '竖线',
            type: 'vline',
          },
        },
        {
          options: {
            left: 6,
            top: 0,
            height: 9.75,
            width: 33,
            title: '样品名称',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 0,
            width: 120,
            testData: '样品名称',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'name',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 6,
            top: 13,
            height: 9.75,
            width: 33,
            title: '样品编号',
            right: 124.5,
            bottom: 21.75,
            vCenter: 64.5,
            hCenter: 16.875,
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 13,
            width: 120,
            testData: '样品编号',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'sampleNo',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 6,
            top: 27,
            height: 9.75,
            width: 33,
            title: '样品批号',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 27,
            width: 120,
            testData: '样品批号',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'lotNo',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 39,
            height: 9.75,
            width: 33,
            title: '保存条件',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 39,
            width: 120,
            testData: '保存条件',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'name',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 51,
            height: 9.75,
            width: 33,
            title: '收样日期',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 51,
            width: 120,
            testData: '收样日期',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'createTime',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 63,
            height: 9.75,
            width: 33,
            title: '样品状态',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 55,
            top: 66,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 60,
            top: 63,
            height: 9.75,
            width: 120,
            title: '待测',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 80,
            top: 66,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 85,
            top: 63,
            height: 9.75,
            width: 120,
            title: '在检',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 100,
            top: 66,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 105,
            top: 63,
            height: 9.75,
            width: 120,
            title: '检毕',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 120,
            top: 66,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 125,
            top: 63,
            height: 9.75,
            width: 120,
            title: '留样',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 63,
            width: 120,
            testData: '样品状态',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'createBy1',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 26,
            top: 75,
            height: 30,
            width: 120,
            field: 'barcode',
            testData: 'XS888888888',
            fontSize: 12,
            lineHeight: 18,
            textAlign: 'left',
            textType: 'barcode',
            title: '条形码',
            qid: 'barcode',
          },
          printElementType: {
            type: 'text',
          },
        },
      ],
      paperNumberContinue: false,
      paperNumberLeft: -10,
      paperNumberTop: -10,
      watermarkOptions: {},
      panelLayoutOptions: {},
    },
  ],
};
