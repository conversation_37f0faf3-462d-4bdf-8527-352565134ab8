import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编号（或批号）',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'consumptiveTypeId_dictText',
  },
  {
    title: '级别',
    align: 'center',
    dataIndex: 'grade',
  },
  {
    title: '纯度（或含量）',
    align: 'center',
    dataIndex: 'purity',
  },
  {
    title: '规格',
    align: 'center',
    dataIndex: 'spec',
  },
  {
    title: '供应商',
    align: 'center',
    dataIndex: 'supplierId_dictText',
  },
  {
    title: '购入（配制）日期',
    align: 'center',
    dataIndex: 'purchaseDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '单价',
    align: 'center',
    dataIndex: 'price',
  },
  {
    title: '有效期（天）',
    align: 'center',
    dataIndex: 'effectiveLength',
  },
  {
    title: '库存量',
    align: 'center',
    dataIndex: 'inventoryquantity',
  },
  {
    title: '填料',
    align: 'center',
    dataIndex: 'packingMaterial',
  },
  {
    title: '柱长',
    align: 'center',
    dataIndex: 'columnLength',
  },
  {
    title: '粒径',
    align: 'center',
    dataIndex: 'particleSize',
  },
  {
    title: '最高温度',
    align: 'center',
    dataIndex: 'maxTemperature',
  },
  {
    title: '品牌',
    align: 'center',
    dataIndex: 'brand',
  },
  {
    title: '厂家',
    align: 'center',
    dataIndex: 'manufacturer',
  },
  {
    title: '出厂编号',
    align: 'center',
    dataIndex: 'serialNumber',
  },
  {
    title: '型号',
    align: 'center',
    dataIndex: 'model',
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
  {
    title: '样品',
    align: 'center',
    dataIndex: 'sampleId_dictText',
  },
  {
    title: '客户',
    align: 'center',
    dataIndex: 'customerId_dictText',
  },
  {
    title: '数量',
    align: 'center',
    dataIndex: 'qty',
  },
  {
    title: '单位',
    align: 'center',
    dataIndex: 'unit_dictText',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'status_dictText',
  },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
  },
  {
    label: '类型',
    field: 'consumptiveTypeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_consumptive_type,name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入类型!' }];
    },
  },
  {
    label: '级别',
    field: 'grade',
    component: 'Input',
  },
  {
    label: '纯度（或含量）',
    field: 'purity',
    component: 'Input',
  },
  {
    label: '规格',
    field: 'spec',
    component: 'Input',
  },
  {
    label: '供应商',
    field: 'supplierId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_supplier,name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入供应商!' }];
    },
  },
  {
    label: '购入（配制）日期',
    field: 'purchaseDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '单价',
    field: 'price',
    component: 'InputNumber',
  },
  {
    label: '有效期（天）',
    field: 'effectiveLength',
    component: 'InputNumber',
  },
  {
    label: '填料',
    field: 'packingMaterial',
    component: 'Input',
  },
  {
    label: '柱长',
    field: 'columnLength',
    component: 'Input',
  },
  {
    label: '粒径',
    field: 'particleSize',
    component: 'Input',
  },
  {
    label: '最高温度',
    field: 'maxTemperature',
    component: 'Input',
  },
  {
    label: '品牌',
    field: 'brand',
    component: 'Input',
  },
  {
    label: '厂家',
    field: 'manufacturer',
    component: 'Input',
  },
  {
    label: '出厂编号',
    field: 'serialNumber',
    component: 'Input',
  },
  {
    label: '型号',
    field: 'model',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '数量',
    field: 'qty',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入数量!' }];
    },
  },
  {
    label: '单位',
    field: 'unit',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_unit,unit_name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入单位!' }];
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sm_status',
    },
    show: false,
    defaultValue: '未入库',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  code: { title: '编号（或批号）', order: 0, view: 'text', type: 'string' },
  name: { title: '名称', order: 1, view: 'text', type: 'string' },
  consumptiveTypeId: {
    title: '类型',
    order: 2,
    view: 'sel_search',
    type: 'string',
    dictTable: 'sys_consumptive_type',
    dictCode: 'id',
    dictText: 'name',
  },
  grade: { title: '级别', order: 3, view: 'text', type: 'string' },
  purity: { title: '纯度（或含量）', order: 4, view: 'text', type: 'string' },
  spec: { title: '规格', order: 5, view: 'text', type: 'string' },
  supplierId: { title: '供应商', order: 6, view: 'sel_search', type: 'string', dictTable: 'sys_supplier', dictCode: 'id', dictText: 'name' },
  purchaseDate: { title: '购入（配制）日期', order: 7, view: 'date', type: 'string' },
  price: { title: '单价', order: 8, view: 'number', type: 'number' },
  effectiveLength: { title: '有效期（天）', order: 9, view: 'number', type: 'number' },
  packingMaterial: { title: '填料', order: 12, view: 'text', type: 'string' },
  columnLength: { title: '柱长', order: 13, view: 'text', type: 'string' },
  particleSize: { title: '粒径', order: 14, view: 'text', type: 'string' },
  maxTemperature: { title: '最高温度', order: 15, view: 'text', type: 'string' },
  brand: { title: '品牌', order: 16, view: 'text', type: 'string' },
  manufacturer: { title: '厂家', order: 17, view: 'text', type: 'string' },
  serialNumber: { title: '出厂编号', order: 18, view: 'text', type: 'string' },
  model: { title: '型号', order: 19, view: 'text', type: 'string' },
  remark: { title: '备注', order: 20, view: 'text', type: 'string' },
  sampleId: { title: '样品', order: 21, view: 'list', type: 'string', dictTable: 'sample', dictCode: 'id', dictText: 'name' },
  customerId: { title: '客户', order: 22, view: 'list', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  qty: { title: '数量', order: 23, view: 'number', type: 'number' },
  unit: { title: '单位', order: 24, view: 'list', type: 'string', dictTable: 'sys_unit', dictCode: 'id', dictText: 'unit_name' },
  status: { title: '状态', order: 25, view: 'list', type: 'string', dictCode: 'sm_status' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
