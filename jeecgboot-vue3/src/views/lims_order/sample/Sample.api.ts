import {defHttp} from "/@/utils/http/axios";
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_order/sample/rootList',
  save='/lims_order/sample/add',
  edit='/lims_order/sample/edit',
  deleteSample = '/lims_order/sample/delete',
  importExcel = '/lims_order/sample/importExcel',
  exportXls = '/lims_order/sample/exportXls',
  loadTreeData = '/lims_order/sample/loadTreeRoot',
  getChildList = '/lims_order/sample/childList',
  getChildListBatch = '/lims_order/sample/getChildListBatch',
  receive = '/lims_order/sample/receive',
  copySample = '/lims_order/sample/copySample',
  copySampleByLotNo = '/lims_order/sample/copySampleByLotNo',
  inventoryQtyAndUnit = '/lims_core/inventory/inventoryQtyAndUnit',
  exist = '/lims_core/inventory/exist',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 导入api
 * @param params
 */
export const getImportUrl = Api.importExcel;
/**
 * 列表接口
 * @param params
 */
export const list = (params) =>
  defHttp.get({url: Api.list, params});
/**
 * 删除
 */
export const deleteSample = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteSample, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}
/**
 * 批量删除
 * @param params
 */
export const batchDeleteSample = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteSample, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdateDict = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params});
}
/**
 * 查询全部树形节点数据
 * @param params
 */
export const loadTreeData = (params) =>
  defHttp.get({url: Api.loadTreeData,params});
/**
 * 查询子节点数据
 * @param params
 */
export const getChildList = (params) =>
  defHttp.get({url: Api.getChildList, params});
/**
 * 批量查询子节点数据
 * @param params
 */
export const getChildListBatch = (params) =>
  defHttp.get({url: Api.getChildListBatch, params},{isTransformResponse:false});


/**
 * 接收
 */
export const receive = (params, handleSuccess) => {
  return defHttp.get({ url: Api.receive, params }).then(() => {
    handleSuccess();
  });
};

/**
 * 复制样品
 */
export const copySample = (params, handleSuccess) => {
  return defHttp.get({ url: Api.copySample, params }).then(() => {
    handleSuccess();
  });
}
/**
 * 按批号复制
 */
export const copySampleByLotNo = (params, handleSuccess) => {
  return defHttp.get({ url: Api.copySampleByLotNo, params }).then(() => {
    handleSuccess();
  });
}
/**
 * 查询库存表中对应货物的库存
 * @param params
 */
export const inventoryQtyAndUnit = (params) => {
  return defHttp.get({ url: Api.inventoryQtyAndUnit, params });
}
/**
 * 查询库存表中是否存在相同编号的货品
 * @param params
 */
export const exist = (params) => {
  return defHttp.post({ url: Api.exist, params });
}
