import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'left',
    dataIndex: 'name'
  },
  {
    title: '检品编号前缀',
    align: 'center',
    dataIndex: 'samplePrefix_dictText'
  },
  {
    title: '研发编号前缀',
    align: 'center',
    dataIndex: 'rdPrefix_dictText'
  },
  {
    title: '描述',
    align: 'center',
    dataIndex: 'description'
  },
  {
    title: '是否生成研发编码',
    align: 'center',
    dataIndex: 'isGenerateRdNo_dictText'
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '检品编号前缀',
    field: 'samplePrefix',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"sys_key,prefix,id"
    },
  },
  {
    label: '研发编号前缀',
    field: 'rdPrefix',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"sys_key,prefix,id"
    },
  },
  {
    label: '描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '父级节点',
    field: 'pid',
    component: 'JTreeSelect',
    componentProps: {
      dict: "biz_type,name,id",
      pidField: "pid",
      pidValue: "0",
      hasChildField: "has_child",
    },
  },
  {
    label: '是否生成研发编码',
    field: 'isGenerateRdNo',
    defaultValue: "0",
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"yn"
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '名称',order: 0,view: 'text', type: 'string',},
  samplePrefix: {title: '检品编号前缀',order: 1,view: 'list', type: 'string',dictTable: "sys_key", dictCode: 'id', dictText: 'prefix',},
  rdPrefix: {title: '研发编号前缀',order: 2,view: 'list', type: 'string',dictTable: "sys_key", dictCode: 'id', dictText: 'prefix',},
  description: {title: '描述',order: 3,view: 'text', type: 'string',},
  isGenerateRdNo: {title: '是否生成研发编码',order: 5,view: 'list', type: 'string',dictCode: 'yn',},
};


/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
