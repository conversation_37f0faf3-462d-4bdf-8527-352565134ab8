<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="800" @ok="handleSubmit">
    <a-space style="margin-bottom: 5px">
      <a-button preIcon="ant-design:printer-outlined" type="primary" @click="handleTranslate">处理翻译</a-button>
    </a-space>
    <BasicForm @register="registerForm" name="SysAnalyteForm" />
  </BasicModal>
  <TranslationModal @register="registerTranslationModal" />
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../SysAnalyte.data';
  import { saveOrUpdate } from '../SysAnalyte.api';
  import { listForAutoComplete } from '/@/api/sys/custom'
  import TranslationModal from '@/views/lims_core/components/TranslationModal.vue';
  import { message } from 'ant-design-vue';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, scrollToField ,updateSchema}] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    autoComplete("groupOne");
    autoComplete("groupTwo");
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      console.log('SysAnalyteModal 提交的数据:', values);
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }


  async function autoComplete(key) {
    let result = await listForAutoComplete({ table: 'sysAnalyte',key: key, value:''});
    console.log(result);
    updateSchema({
      field: key,
      component: 'AutoComplete',
      componentProps: {
        options: result.slice(0, 5),
        onSearch: (value) => {
          let newResult = result.filter((item) => {
            return item.value.indexOf(value) > -1;
          });
          updateSchema({
            field: key,
            componentProps: {
              options: newResult.slice(0, 5),
            },
          });
        },
      },
    });
  }
  const [registerTranslationModal, { openModal: openTranslationModal }] = useModal();

  const handleTranslate = async () => {
    let values = await validate();
    try{
      await saveOrUpdate(values, isUpdate.value);
      openTranslationModal(true, {
        baseTable: 'sys_customer',
        baseId: values.id,
        recordvalue: { ...values },
        formSchema: formSchema,
      });
    }catch (e) {
      console.log(e);
      message.error('请填入必填信息');
    }

  };
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
