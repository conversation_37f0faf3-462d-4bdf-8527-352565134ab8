/**
 * 解决 Ant Design Vue a-select 组件文本无法选择和复制的问题
 * 强力版本：通过事件拦截和DOM操作来实现
 */

/**
 * 初始化文本选择修复功能
 */
export function initSelectTextFix() {
  console.log('初始化 a-select 文本选择修复功能');

  // 添加全局样式来确保文本可选择
  addGlobalStyles();

  // 添加强力事件拦截
  addEventInterception();

  // 添加双击复制功能
  addDoubleClickCopy();

  // 添加键盘快捷键支持
  addKeyboardSupport();

  // 定期检查并修复新添加的元素
  startPeriodicFix();
}

/**
 * 添加全局样式
 */
function addGlobalStyles() {
  const styleId = 'select-text-fix-styles';

  // 避免重复添加
  if (document.getElementById(styleId)) {
    return;
  }

  const style = document.createElement('style');
  style.id = styleId;
  style.textContent = `
    /* 强制所有 a-select 相关元素文本可选择 */
    .ant-select,
    .ant-select *,
    .ant-select-selector,
    .ant-select-selector *,
    .ant-select-selection-item,
    .ant-select-selection-item *,
    .ant-select-selection-item-content,
    .ant-select-selection-item-content * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      cursor: text !important;
    }

    /* 移除可能阻止文本选择的样式 */
    .ant-select-selector,
    .ant-select-selection-item {
      pointer-events: auto !important;
    }

    /* 确保文本层级正确 */
    .ant-select-selection-item {
      position: relative;
      z-index: 1;
    }

    /* 禁用可能干扰的伪元素 */
    .ant-select-selector::before,
    .ant-select-selector::after {
      pointer-events: none !important;
    }

    /* 特殊标记类 */
    .text-selectable-force {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      cursor: text !important;
    }

    .text-selectable-force * {
      user-select: text !important;
      -webkit-user-select: text !important;
      -moz-user-select: text !important;
      -ms-user-select: text !important;
      cursor: text !important;
    }
  `;

  document.head.appendChild(style);
}

/**
 * 添加强力事件拦截
 */
function addEventInterception() {
  // 拦截可能阻止文本选择的事件
  const events = ['mousedown', 'mouseup', 'click'];

  events.forEach(eventType => {
    document.addEventListener(eventType, (e) => {
      const target = e.target as HTMLElement;

      if (isSelectElement(target)) {
        // 检查是否是文本选择操作
        if (eventType === 'mousedown') {
          // 标记开始选择
          target.setAttribute('data-selecting', 'true');
        } else if (eventType === 'mouseup') {
          // 检查是否有选中的文本
          const selection = window.getSelection();
          if (selection && selection.toString().length > 0) {
            // 有选中文本，阻止后续的click事件
            setTimeout(() => {
              target.setAttribute('data-has-selection', 'true');
            }, 0);
          }
          target.removeAttribute('data-selecting');
        } else if (eventType === 'click') {
          // 如果有选中文本，阻止下拉框打开
          if (target.getAttribute('data-has-selection') === 'true') {
            e.preventDefault();
            e.stopPropagation();
            target.removeAttribute('data-has-selection');
            return false;
          }
        }
      }
    }, true);
  });
}

/**
 * 添加双击复制功能
 */
function addDoubleClickCopy() {
  document.addEventListener('dblclick', (e) => {
    const target = e.target as HTMLElement;

    if (isSelectElement(target)) {
      e.preventDefault();
      e.stopPropagation();

      const text = getSelectText(target);
      if (text) {
        copyToClipboard(text);
        showCopyMessage('文本已复制到剪贴板');
      }
    }
  });
}

/**
 * 添加键盘快捷键支持
 */
function addKeyboardSupport() {
  document.addEventListener('keydown', (e) => {
    // Ctrl+A 全选
    if (e.ctrlKey && e.key === 'a') {
      const activeElement = document.activeElement as HTMLElement;
      if (isSelectElement(activeElement)) {
        e.preventDefault();
        const text = getSelectText(activeElement);
        if (text) {
          selectText(activeElement);
        }
      }
    }

    // Ctrl+C 复制
    if (e.ctrlKey && e.key === 'c') {
      const selection = window.getSelection();
      if (selection && selection.toString().length > 0) {
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;
        const element = container.nodeType === Node.TEXT_NODE ?
          container.parentElement : container as HTMLElement;

        if (isSelectElement(element)) {
          copyToClipboard(selection.toString());
          showCopyMessage('文本已复制到剪贴板');
        }
      }
    }
  });
}

/**
 * 定期检查并修复新添加的元素
 */
function startPeriodicFix() {
  // 使用 MutationObserver 监听DOM变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as HTMLElement;
          fixSelectElements(element);
        }
      });
    });
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 初始修复现有元素
  fixSelectElements(document.body);
}

/**
 * 修复指定元素及其子元素的文本选择功能
 */
function fixSelectElements(container: HTMLElement) {
  // 查找所有 a-select 相关元素
  const selectors = [
    '.ant-select',
    '.ant-select-selector',
    '.ant-select-selection-item',
    '.ant-select-selection-item-content'
  ];

  selectors.forEach(selector => {
    const elements = container.querySelectorAll(selector);
    elements.forEach(element => {
      const htmlElement = element as HTMLElement;
      htmlElement.classList.add('text-selectable-force');

      // 移除可能阻止文本选择的事件监听器
      const newElement = htmlElement.cloneNode(true) as HTMLElement;
      htmlElement.parentNode?.replaceChild(newElement, htmlElement);
    });
  });
}

/**
 * 检查元素是否是 a-select 的选择器相关元素
 */
function isSelectElement(element: HTMLElement): boolean {
  if (!element) return false;

  // 检查元素本身或其父元素是否包含相关类名
  let current = element;
  let depth = 0;

  while (current && depth < 10) {
    const classList = current.classList;

    if (classList.contains('ant-select-selector') ||
        classList.contains('ant-select-selection-item') ||
        classList.contains('ant-select-selection-item-content')) {
      return true;
    }

    current = current.parentElement as HTMLElement;
    depth++;
  }

  return false;
}

/**
 * 获取选择器中的文本内容
 */
function getSelectText(element: HTMLElement): string {
  // 尝试从不同的元素中获取文本
  let text = '';

  // 首先尝试从选中项获取文本
  const selectionItem = element.closest('.ant-select-selection-item');
  if (selectionItem) {
    text = selectionItem.textContent || selectionItem.innerText || '';
  }

  // 如果没有找到，尝试从选择器获取文本
  if (!text) {
    const selector = element.closest('.ant-select-selector');
    if (selector) {
      text = selector.textContent || selector.innerText || '';
    }
  }

  // 如果还是没有找到，直接从当前元素获取
  if (!text) {
    text = element.textContent || element.innerText || '';
  }

  return text.trim();
}

/**
 * 复制文本到剪贴板
 */
async function copyToClipboard(text: string): Promise<void> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
    } else {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      document.execCommand('copy');
      textArea.remove();
    }
  } catch (err) {
    console.error('复制到剪贴板失败:', err);
  }
}

/**
 * 选择元素中的所有文本
 */
function selectText(element: HTMLElement) {
  const range = document.createRange();
  const selection = window.getSelection();

  if (selection) {
    selection.removeAllRanges();
    range.selectNodeContents(element);
    selection.addRange(range);
  }
}

/**
 * 显示复制成功消息
 */
function showCopyMessage(message: string) {
  // 创建一个临时的提示元素
  const toast = document.createElement('div');
  toast.textContent = message;
  toast.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #52c41a;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    z-index: 9999;
    font-size: 14px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  `;

  document.body.appendChild(toast);

  // 2秒后移除提示
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 2000);
}

/**
 * 为特定的 a-select 元素启用文本选择功能
 */
export function enableTextSelection(selector: string | HTMLElement) {
  const elements = typeof selector === 'string'
    ? document.querySelectorAll(selector)
    : [selector];

  elements.forEach(element => {
    if (element) {
      element.classList.add('text-selectable');
    }
  });
}

/**
 * 禁用特定 a-select 元素的文本选择功能
 */
export function disableTextSelection(selector: string | HTMLElement) {
  const elements = typeof selector === 'string'
    ? document.querySelectorAll(selector)
    : [selector];

  elements.forEach(element => {
    if (element) {
      element.classList.remove('text-selectable');
    }
  });
}
