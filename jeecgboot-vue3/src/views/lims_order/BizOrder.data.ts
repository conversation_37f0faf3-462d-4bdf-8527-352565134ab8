import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { useUserStore } from '@/store/modules/user';
//列表数据
const userStore = useUserStore();
export const columns: BasicColumn[] = [
  {
    title: '状态',
    align: 'center',
    dataIndex: 'statusId_dictText',
  },
  {
    title: '合同名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '客户名称',
    align: 'center',
    dataIndex: 'customerId_dictText',
    sorter:true,
    filters: [],
  },
  {
    title: '合同类型',
    align: 'center',
    dataIndex: 'contractTypeId_dictText',
  },
  {
    title: '业务类别',
    align: 'center',
    dataIndex: 'bizTypeId_dictText',
    sorter:true,
  },

  {
    title: '签订日期',
    align: 'center',
    dataIndex: 'signingDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '负责人',
    align: 'center',
    dataIndex: 'responsiblePerson_dictText',
  },
  {
    title: '项目状态',
    align: 'center',
    dataIndex: 'projectStatus_dictText',
  },
  {
    title: '协同人',
    align: 'center',
    dataIndex: 'coordinator_dictText',
  },
  {
    title: '报价申请',
    align: 'center',
    dataIndex: 'quotationId_dictText',
  },
  {
    title: '客户联系人',
    align: 'center',
    dataIndex: 'customerContactId_dictText',
  },
  {
    title: '客户联系人姓名',
    align: 'center',
    dataIndex: 'customerContactName',
  },
  {
    title: '客户联系人电话',
    align: 'center',
    dataIndex: 'customerContactPhone',
  },
  {
    title: '客户联系人邮箱',
    align: 'center',
    dataIndex: 'customerContactEmail',
  },
  {
    title: '签约主体',
    align: 'center',
    dataIndex: 'signingEntityId_dictText',
  },
  {
    title: '加急类别',
    align: 'center',
    dataIndex: 'serviceTypeId_dictText',
  },
  {
    title: '工期',
    align: 'center',
    dataIndex: 'leadTime',
  },
  {
    title: '支付方式',
    align: 'center',
    dataIndex: 'payMethod',
  },
  {
    title: '扫描件',
    align: 'center',
    dataIndex: 'scanAttach',
  },
  {
    title: '标准价',
    align: 'center',
    dataIndex: 'standardPrice',
  },
  {
    title: 'PM价',
    align: 'center',
    dataIndex: 'pmPrice',
  },
  {
    title: '合同状态',
    align: 'center',
    dataIndex: 'contractStatusId_dictText',
  },
  {
    title: '合同金额',
    align: 'center',
    dataIndex: 'contractAmount',
  },
  {
    title: '合同类别(对接)',
    align: 'center',
    dataIndex: 'contractIntegrationTypeId_dictText',
  },
  {
    title: '合同金额(人民币)',
    align: 'center',
    dataIndex: 'contractAmountRmb',
  },
  {
    title: '计划回款金额',
    align: 'center',
    dataIndex: 'plannedPaymentAmount',
  },
  {
    title: '发票已作废金额',
    align: 'center',
    dataIndex: 'invoiceCancelAmount',
  },
  {
    title: '实际开票金额',
    align: 'center',
    dataIndex: 'actualInvoicedAmount',
  },
  {
    title: '待开票金额',
    align: 'center',
    dataIndex: 'pendingInvoicedAmount',
  },
  {
    title: '备注',
    align: 'center',
    dataIndex: 'remark',
  },
  {
    title: '结算币别',
    align: 'center',
    dataIndex: 'currencyId_dictText',
  },
  {
    title: '汇率',
    align: 'center',
    dataIndex: 'exchangeRate',
  },
  {
    title: '未下单原因',
    align: 'center',
    dataIndex: 'notOrderingReason',
  },
  {
    title: '预计到样日期',
    align: 'center',
    dataIndex: 'etaDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '确认生成回款计划',
    align: 'center',
    dataIndex: 'isGeneratePaymentPlan_dictText',
  },
  {
    title: '已送检批次',
    align: 'center',
    dataIndex: 'lotNo',
  },
  {
    title: '签收单',
    align: 'center',
    dataIndex: 'receiptAttach',
  },
  {
    title: '出报告客户',
    align: 'center',
    dataIndex: 'reportClientName',
  },
  {
    title: '实际回款金额',
    align: 'center',
    dataIndex: 'actualPaymentAmount',
  },
  {
    title: '退款金额',
    align: 'center',
    dataIndex: 'refundAmount',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '业务员',
    field: 'responsiblePerson',
    component: 'JSelectUser',
    componentProps: ({ model }) => ({
      disabled: userStore.getUserInfo.roles.length === 2 &&
        userStore.getUserInfo.roles.includes('saler') &&
        userStore.getUserInfo.roles.includes('default_permisson'),
    }),
    defaultValue: userStore.getUserInfo.roles.length === 2 &&
    userStore.getUserInfo.roles.includes('saler') &&
    userStore.getUserInfo.roles.includes('default_permisson')
      ? userStore.getUserInfo.realname
      : undefined,
    colProps: { span: 4 },
  },
  {
    label: '订单编号',
    field: 'contractNo',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '创建人',
    field: 'createBy',
    component: 'JInput',
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
  {
    label: '客户名称',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_customer,name,id',
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '合同名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入合同名称!' }];
    },
  },
  {
    label: '客户名称',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: ({ schema, formModel, formActionType }) => {
      return {
        dict: 'sys_customer,name,id',
        onChange: (val) => {
          console.log('schema', schema);
          console.log('formActionType', formActionType);
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户名称!' }];
    },
  },
  {
    label: '合同类型',
    field: 'contractTypeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'contract_type',
    },
    defaultValue: '检测委托书（W）',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入合同类型!' }];
    },
  },
  {
    label: '业务类别',
    field: 'bizTypeId',
    component: 'JSearchSelect',
    componentProps:{
      dict:"biz_type,name,id",
      getPopupContainer: () => document.body,
    },
    dynamicRules: ({model,schema}) => {
      return [
        { required: true, message: '请输入业务类别!'},
      ];
    },
  },
  {
    label: '合同编号',
    field: 'contractNo',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入合同编号!' }];
    },
  },
  {
    label: '签订日期',
    field: 'signingDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    defaultValue: new Date().toISOString().split('T')[0],
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入签订日期!' }];
    },
  },
  {
    label: '负责人',
    field: 'responsiblePerson',
    component: 'JSelectUser',
    componentProps: {},
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入负责人!' }];
    },
  },
  {
    label: '项目状态',
    field: 'projectStatus',
    defaultValue: '0',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'project_type',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入项目状态!' }];
    },
  },
  {
    label: '协同人',
    field: 'coordinator',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '报价申请',
    field: 'quotationId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'quotation,quotation_no,id',
    },
  },
  {
    label: '客户联系人',
    field: 'customerContactId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_customer_contact,name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户联系人!' }];
    },
  },
  {
    label: '客户联系人姓名',
    field: 'customerContactName',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '客户联系人电话',
    field: 'customerContactPhone',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '客户联系人邮箱',
    field: 'customerContactEmail',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '签约主体',
    field: 'signingEntityId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'signing_entity',
    },
    defaultValue:'1',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入签约主体!' }];
    },
  },
  {
    label: '加急类别',
    field: 'serviceTypeId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'service_type',
      type: 'radio',
    },
  },
  {
    label: '工期',
    field: 'leadTime',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入工期!' }];
    },
    slot: 'leadTime',
  },
  {
    label: '工期类型',
    field: 'dayType',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入工期类别!' }];
    },
    show: false,
  },
  {
    label: '支付方式',
    field: 'payMethod',
    component: 'InputTextArea',
    defaultValue:'转账',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入支付方式!' }];
    },
  },
  {
    label: '扫描件',
    field: 'scanAttach',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '标准价',
    field: 'standardPrice',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: 'PM价',
    field: 'pmPrice',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '合同状态',
    field: 'contractStatusId',
    defaultValue: '1',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'contract_status',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入合同状态!' }];
    },
  },
  {
    label: '合同金额',
    field: 'contractAmount',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '合同类别(对接)',
    field: 'contractIntegrationTypeId',
    defaultValue: '0',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'contract_integration_type',
      type: 'radio',
    },
  },
  {
    label: '合同金额(人民币)',
    field: 'contractAmountRmb',
    defaultValue: 0,
    component: 'InputNumber',
  },
  {
    label: '计划回款金额',
    field: 'plannedPaymentAmount',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '发票已作废金额',
    field: 'invoiceCancelAmount',
    defaultValue: 0,
    component: 'InputNumber',
  },
  {
    label: '实际开票金额',
    field: 'actualInvoicedAmount',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '待开票金额',
    field: 'pendingInvoicedAmount',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '结算币别',
    field: 'currencyId',
    component: 'JDictSelectTag',
    defaultValue: 'CNY',
    componentProps: {
      dictCode: 'currency',
      type: 'radio',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入结算币别!' }];
    },
  },
  {
    label: '汇率',
    field: 'exchangeRate',
    defaultValue: 1,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '未下单原因',
    field: 'notOrderingReason',
    component: 'Input',
  },
  {
    label: '预计到样日期',
    field: 'etaDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '确认生成回款计划',
    field: 'isGeneratePaymentPlan',
    defaultValue: '0',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'logic_code',
      type: 'radio',
    },
  },
  {
    label: '已送检批次',
    field: 'lotNo',
    component: 'Input',
  },
  {
    label: '签收单',
    field: 'receiptAttach',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '出报告客户',
    field: 'reportClientName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入出报告客户!' }];
    },
  },
  {
    label: '实际回款金额',
    field: 'actualPaymentAmount',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  {
    label: '退款金额',
    field: 'refundAmount',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicDisabled: true,
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  createBy: { title: '创建人', order: 1, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 2, view: 'datetime', type: 'string' },
  name: { title: '合同名称', order: 3, view: 'text', type: 'string' },
  customerId: { title: '客户名称', order: 4, view: 'sel_search', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  contractTypeId: { title: '合同类型', order: 5, view: 'sel_search', type: 'string', dictCode: 'contract_type' },
  bizTypeId: { title: '业务分类', order: 6, view: 'sel_search', type: 'string', dictCode: 'biz_type', pcode: 'C09' },
  contractNo: { title: '合同编号', order: 7, view: 'text', type: 'string' },
  signingDate: { title: '签订日期', order: 8, view: 'date', type: 'string' },
  responsiblePerson: { title: '负责人', order: 9, view: 'sel_user', type: 'string' },
  projectStatus: { title: '项目状态', order: 10, view: 'sel_search', type: 'string', dictCode: 'project_type' },
  coordinator: { title: '协同人', order: 11, view: 'sel_user', type: 'string' },
  quotationId: { title: '报价申请', order: 12, view: 'text', type: 'string' },
  customerContactId: {
    title: '客户联系人',
    order: 13,
    view: 'sel_search',
    type: 'string',
    dictTable: 'sys_customer_contact',
    dictCode: 'id',
    dictText: 'name',
  },
  customerContactName: { title: '客户联系人姓名', order: 14, view: 'text', type: 'string' },
  customerContactPhone: { title: '客户联系人电话', order: 15, view: 'text', type: 'string' },
  customerContactEmail: { title: '客户联系人邮箱', order: 16, view: 'text', type: 'string' },
  signingEntityId: { title: '签约主体', order: 17, view: 'text', type: 'string' },
  serviceTypeId: { title: '加急类别', order: 18, view: 'radio', type: 'string', dictCode: 'service_type' },
  leadTime: { title: '工期', order: 19, view: 'text', type: 'string' },
  payMethod: { title: '支付方式', order: 20, view: 'text', type: 'string' },
  scanAttach: { title: '扫描件', order: 21, view: 'file', type: 'string' },
  standardPrice: { title: '标准价', order: 22, view: 'number', type: 'number' },
  pmPrice: { title: 'PM价', order: 23, view: 'number', type: 'number' },
  contractStatusId: { title: '合同状态', order: 24, view: 'list', type: 'string', dictCode: 'contract_status' },
  contractAmount: { title: '合同金额', order: 25, view: 'number', type: 'number' },
  contractIntegrationTypeId: { title: '合同类别(对接)', order: 26, view: 'radio', type: 'string', dictCode: 'contract_integration_type' },
  contractAmountRmb: { title: '合同金额(人民币)', order: 27, view: 'number', type: 'number' },
  plannedPaymentAmount: { title: '计划回款金额', order: 28, view: 'number', type: 'number' },
  invoiceCancelAmount: { title: '发票已作废金额', order: 29, view: 'number', type: 'number' },
  actualInvoicedAmount: { title: '实际开票金额', order: 30, view: 'number', type: 'number' },
  pendingInvoicedAmount: { title: '待开票金额', order: 31, view: 'number', type: 'number' },
  remark: { title: '备注', order: 32, view: 'text', type: 'string' },
  currencyId: { title: '结算币别', order: 33, view: 'text', type: 'string' },
  exchangeRate: { title: '汇率', order: 34, view: 'number', type: 'number' },
  notOrderingReason: { title: '未下单原因', order: 35, view: 'text', type: 'string' },
  etaDate: { title: '预计到样日期', order: 36, view: 'date', type: 'string' },
  isGeneratePaymentPlan: { title: '确认生成回款计划', order: 37, view: 'radio', type: 'string', dictCode: 'logic_code' },
  lotNo: { title: '已送检批次', order: 38, view: 'text', type: 'string' },
  receiptAttach: { title: '签收单', order: 39, view: 'file', type: 'string' },
  reportClientName: { title: '出报告客户', order: 40, view: 'text', type: 'string' },
  actualPaymentAmount: { title: '实际回款金额', order: 41, view: 'number', type: 'number' },
  refundAmount: { title: '退款金额', order: 42, view: 'number', type: 'number' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
