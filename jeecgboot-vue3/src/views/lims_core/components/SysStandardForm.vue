<template>
  <div>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <!--主表区域 -->
      <a-tab-pane tab="标准" :key="refKeys[0]" :forceRender="true" :style="tabsStyle">
        <BasicForm @register="registerForm" ref="formRef" />
      </a-tab-pane>
      <!--子表单区域 -->
      <a-tab-pane tab="标准指标评定要求" key="sysStandardEvaluationLimt" :forceRender="true" :style="tabsStyle">
        <JVxeTable
          keep-source
          resizable
          ref="sysStandardEvaluationLimt"
          v-if="sysStandardEvaluationLimtTable.show"
          :loading="sysStandardEvaluationLimtTable.loading"
          :columns="sysStandardEvaluationLimtTable.columns"
          :dataSource="sysStandardEvaluationLimtTable.dataSource"
          :size="small"
          :height="340"
          :disabled="formDisabled"
          :rowNumber="true"
          :rowSelection="true"
          :toolbar="true"
          :row-config="{ keyField: 'id' }"
          :tree-config="{
            transform: true,
            rowField: 'id',
            parentField: 'parentId',
            childrenField: 'children',
          }"
        >
          <template #action="{ row }">
            <a-button type="link" size="small" @click="addChildRow(row)"> 添加下级 </a-button>
          </template>
        </JVxeTable>
      </a-tab-pane>
    </a-tabs>

    <div style="width: 100%; text-align: center; margin-top: 10px" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defHttp } from '/@/utils/http/axios';
  import { ref, computed, unref, reactive, onMounted, defineProps } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { formSchema, sysStandardEvaluationLimtColumns } from '../SysStandard.data';
  import { saveOrUpdate, sysStandardEvaluationLimtList } from '../SysStandard.api';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  const refKeys = ref(['sysStandard', 'sysStandardEvaluationLimt']);
  const activeKey = ref('sysStandard');
  const sysStandardEvaluationLimt = ref();
  const tableRefs = { sysStandardEvaluationLimt };
  const sysStandardEvaluationLimtTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysStandardEvaluationLimtColumns,
    show: false,
  });

  const props = defineProps({
    formData: { type: Object, default: () => {} },
    formBpm: { type: Boolean, default: true },
  });
  const formDisabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      }
    }
    return true;
  });
  // 是否显示提交按钮
  const showFlowSubmitButton = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return true;
      }
    }
    return false;
  });

  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });

  onMounted(() => {
    initFormData();

    // 监听表格数据变化，确保及时同步
    setTimeout(() => {
      if (sysStandardEvaluationLimt.value) {
        try {
          // 监听数据变化事件
          sysStandardEvaluationLimt.value.getVmInstance?.$on?.('value-change', () => {
            console.log('表格数据发生变化，同步数据');
            syncTableDataBeforeSubmit();
          });

          // 监听表单提交事件
          sysStandardEvaluationLimt.value.getVmInstance?.$on?.('submit', () => {
            console.log('表单提交前同步数据');
            syncTableDataBeforeSubmit();
          });

          console.log('已添加表格数据变化监听');
        } catch (error) {
          console.error('添加表格数据监听失败:', error);
        }
      }
    }, 500); // 延迟确保组件已挂载
  });
  //渲染流程表单数据
  const queryByIdUrl = '/lims_core/sysStandard/queryById';
  async function initFormData() {
    if (props.formBpm === true) {
      await reset();
      let params = { id: props.formData.dataId };
      const data = await defHttp.get({ url: queryByIdUrl, params });
      //表单赋值
      await setFieldsValue({
        ...data,
      });
      requestSubTableData(sysStandardEvaluationLimtList, { id: data.id }, sysStandardEvaluationLimtTable, () => {
        sysStandardEvaluationLimtTable.show = true;
      });
      // 隐藏底部时禁用整个表单
      setProps({ disabled: formDisabled.value });
    }
  }

  //方法配置
  const [handleChangeTabs, handleSubmit, requestSubTableData, formRef] = useJvxeMethod(
    requestAddOrEdit,
    classifyIntoFormData,
    tableRefs,
    activeKey,
    refKeys
  );
  // 弹窗tabs滚动区域的高度
  const tabsStyle = computed(() => {
    let height: Nullable<string> = null;
    let minHeight = '100px';
    // 弹窗wrapper
    let overflow = 'auto';
    return { height, minHeight, overflow };
  });

  async function reset() {
    await resetFields();
    activeKey.value = 'sysStandard';
    sysStandardEvaluationLimtTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);

    // 直接使用dataSource作为提交数据源，确保包含子节点
    return {
      ...main, // 展开
      sysStandardEvaluationLimtList: sysStandardEvaluationLimtTable.dataSource,
    };
  }

  // 在提交前确保数据源是最新的
  function syncTableDataBeforeSubmit() {
    try {
      const $table = sysStandardEvaluationLimt.value?.getXTable();
      if ($table) {
        // 获取表格中的全部数据（包括新添加的行）
        const fullData = $table.getTableData().fullData;
        console.log('同步前的数据源:', sysStandardEvaluationLimtTable.dataSource);
        console.log('表格完整数据:', fullData);
        // 确保数据源包含所有行
        sysStandardEvaluationLimtTable.dataSource = [...fullData];
        console.log('同步后的数据源:', sysStandardEvaluationLimtTable.dataSource);
      }
    } catch (error) {
      console.error('同步数据失败:', error);
    }
  }

  //表单提交事件
  async function requestAddOrEdit(values) {
    // 先同步数据确保最新状态
    syncTableDataBeforeSubmit();

    console.log('提交的表单数据:', values);

    //提交表单
    await saveOrUpdate(values, true);
  }

  // 添加子行
  async function addChildRow(currRow) {
    console.log('添加子行 - 当前行:', currRow);

    try {
      const $table = sysStandardEvaluationLimt.value?.getXTable();
      if (!$table) {
        console.error('表格实例不存在');
        return;
      }

      // 创建子行记录
      const record = {
        id: Date.now(), // 临时ID
        analyteId: currRow.analyteId,
        methodIds: currRow.methodIds,
        elimit: currRow.elimit || '/',
        unitId: currRow.unitId,
        parentId: currRow.id, // 明确设置父ID
      };

      console.log('准备添加的子行:', record);

      // 使用VxeTable API添加行
      const { row: newRow } = await $table.insertAt(record, -1);

      // 确保展开父节点
      await $table.setTreeExpand(currRow, true);

      // 设置新行为编辑状态
      await $table.setEditRow(newRow);

      // 将新行添加到数据源（这很关键）
      sysStandardEvaluationLimtTable.dataSource.push(newRow);

      console.log('添加后的数据源:', sysStandardEvaluationLimtTable.dataSource);

      // 同步数据确保一致性
      syncTableDataBeforeSubmit();
    } catch (error) {
      console.error('添加子行失败:', error);
    }
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>

<style lang="less">
  // Online表单Tab风格专属样式
  .j-cgform-tab-modal {
    .ant-modal-header {
      padding-top: 8px;
      padding-bottom: 8px;
      border-bottom: none !important;
    }

    .ant-modal .ant-modal-body > .scrollbar,
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-tabs-top-bar {
      width: calc(100% - 55px);
      position: relative;
      left: -14px;
    }

    .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
      overflow: hidden auto;
    }
  }
</style>
