* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    font-size: 14px;
}

#mail-sender-container {
    width: 100%;
    max-width: 640px;
    margin: 0 auto;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

.header {
    padding: 16px;
    border-bottom: 1px solid #e0e0e0;
}

.header h2 {
    font-size: 18px;
    font-weight: 500;
    color: #444;
}

.form-container {
    padding: 16px;
}

.form-group {
    margin-bottom: 16px;
}

label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #555;
}

input[type="text"],
input[type="password"],
textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="password"]:focus,
textarea:focus {
    border-color: #3d85c6;
    outline: none;
}

.attachment-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
}

.attachment-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

#additional-attachments {
    margin-top: 8px;
}

.attachment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.attachment-item:last-child {
    border-bottom: none;
}

.attachment-name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.remove-attachment {
    color: #d23f31;
    cursor: pointer;
    margin-left: 8px;
}

.smtp-settings {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
}

.setting-row {
    display: flex;
    gap: 12px;
    margin-bottom: 8px;
}

.setting-row:last-child {
    margin-bottom: 0;
    justify-content: space-between;
    align-items: center;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
}

.primary-button {
    background-color: #3d85c6;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.primary-button:hover {
    background-color: #2a6daa;
}

.secondary-button {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 16px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
}

.secondary-button:hover {
    background-color: #e8e8e8;
}

#loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex; /* 默认使用flex布局 */
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

/* 当添加hidden类时，覆盖display属性 */
#loading-overlay.hidden {
    display: none !important;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3d85c6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

.error {
    color: #d23f31;
    font-size: 12px;
    margin-top: 4px;
}

.success-message {
    background-color: #e6f4ea;
    color: #137333;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
}

.success-message::before {
    content: '✓';
    font-weight: bold;
    margin-right: 8px;
}