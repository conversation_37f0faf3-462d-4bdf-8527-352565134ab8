import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { useUserStore } from '@/store/modules/user';
const userStore = useUserStore();
const allDictItems = userStore.getAllDictItems;
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '报告编号',
    align: 'center',
    dataIndex: 'reportNo',
    fixed: 'left',
    sorter: true,
    resizable: true,
  },
  {
    title: '到期日',
    align: 'center',
    dataIndex: 'deadLine',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
    sorter: true,
    resizable: true,
  },
  {
    title: '样品',
    align: 'center',
    dataIndex: 'sampleId_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '结论',
    align: 'center',
    dataIndex: 'conclusion',
    sorter: true,
    resizable: true,
  },
  {
    title: '合同',
    align: 'center',
    dataIndex: 'orderId_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '模板',
    align: 'center',
    dataIndex: 'templateId_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '草稿',
    align: 'center',
    dataIndex: 'url',
    sorter: true,
    resizable: true,
  },
  {
    title: '电子报告',
    align: 'center',
    dataIndex: 'finalUrl',
    sorter: true,
    resizable: true,
  },
  {
    title: '测试',
    align: 'center',
    dataIndex: 'testIds_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '重复周期',
    align: 'center',
    dataIndex: 'repeatTypeId_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '审核人',
    align: 'center',
    dataIndex: 'approveBy_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '审核时间',
    align: 'center',
    dataIndex: 'approveTime',
    sorter: true,
    resizable: true,
  },
  {
    title: '签发人',
    align: 'center',
    dataIndex: 'signBy_dictText',
    sorter: true,
    resizable: true,
  },
  {
    title: '签发日期',
    align: 'center',
    dataIndex: 'signTime',
    sorter: true,
    resizable: true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '状态',
    field: 'reportstatus',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'report_status',
    },
    defaultValue: '待编制,待审核',
  },
  {
    label: '报告编号',
    field: 'reportNo',
    component: 'JInput',
    defaultValue: '*YPT*',
    //colProps: {span: 6},
  },
  {
    label: '合同',
    field: 'orderId',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'biz_order,contract_no,id',
    },
    //colProps: {span: 6},
  },
  {
    label: '到期日',
    field: 'deadLine',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '报告编号',
    field: 'reportNo',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入报告编号!' }];
    },
    dynamicDisabled: true,
  },
  {
    label: '合同',
    field: 'orderId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'biz_order,contract_no,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入合同!' }];
    },
    dynamicDisabled: true,
  },
  {
    label: '样品',
    field: 'sampleId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sample,name,id',
    },
  },
  {
    label: '测试',
    field: 'testTaskIds',
    component: 'JCheckbox',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'test_task,name,id',
    },
  },
  {
    label: '重复周期',
    field: 'repeatTypeId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_method_repeat_type,cycle_name,id',
    },
  },
  {
    label: '模板',
    field: 'templateId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_template,name,id',
    },
  },
  {
    label: '到期日',
    field: 'deadLine',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入到期日!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

//子表列表数据
export const TestResultColumns: BasicColumn[] = [
  {
    title: '方法名称',
    align: 'center',
    dataIndex: 'methodId_dictText',
  },
  {
    title: '项目名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '部门',
    align: 'center',
    dataIndex: 'departName',
  },
  {
    title: '当前节点',
    align: 'center',
    dataIndex: 'curStep',
    customRender: (opt) => {
      if (allDictItems['flow_step']) {
        const re = allDictItems['flow_step'].find((item) => {
          if (item.value === opt.text) {
            return item;
          }
        });
        return re === undefined ? opt.text : re.text;
      }
      return opt.text;
    },
  },
  {
    title: '任务状态',
    align: 'center',
    dataIndex: 'status_dictText',
  },
  {
    title: '项目结果',
    align: 'center',
    dataIndex: 'conclusion',
  },
];

// 高级查询数据
export const superQuerySchema = {
  reportNo: { title: '报告编号', order: 0, view: 'text', type: 'string' },
  orderId: { title: '合同', order: 1, view: 'list', type: 'string', dictTable: 'biz_order', dictCode: 'id', dictText: 'contract_no' },
  sampleId: { title: '样品', order: 2, view: 'list', type: 'string', dictTable: 'sample', dictCode: 'id', dictText: 'name' },
  testIds: { title: '测试', order: 3, view: 'checkbox', type: 'string', dictTable: 'test', dictCode: 'id', dictText: 'id' },
  repeatTypeId: {
    title: '重复周期',
    order: 4,
    view: 'list',
    type: 'string',
    dictTable: 'sys_method_repeat_type',
    dictCode: 'id',
    dictText: 'cycle_name',
  },
  templateId: { title: '模板', order: 5, view: 'text', type: 'string' },
  deadLine: { title: '到期日', order: 6, view: 'date', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
