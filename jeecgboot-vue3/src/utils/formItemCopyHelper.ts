/**
 * 为表单项添加复制提示的增强版本
 * 使用JavaScript动态创建提示元素，确保在所有组件上都能正常显示
 */

/**
 * 初始化表单项复制提示功能
 */
export function initFormItemCopyTooltip() {
  console.log('初始化表单项复制提示功能');

  // 添加基础样式
  addTooltipStyles();

  // 添加事件监听
  addTooltipEventListeners();

  // 监听DOM变化
  observeFormItemChanges();
}

/**
 * 添加提示相关样式
 */
function addTooltipStyles() {
  const style = document.createElement('style');
  style.id = 'form-item-copy-tooltip-styles';
  style.textContent = `
    /* 复制提示样式 */
    .copy-tooltip {
      position: absolute;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      z-index: 10000;
      pointer-events: none;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      animation: fadeIn 0.2s ease-in;
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: scale(0.8); }
      to { opacity: 1; transform: scale(1); }
    }
    
    /* 复制成功提示 */
    .copy-success-notification {
      position: fixed;
      top: 20px;
      right: 20px;
      background: #52c41a;
      color: white;
      padding: 8px 16px;
      border-radius: 4px;
      z-index: 10000;
      font-size: 14px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      animation: slideInRight 0.3s ease-out;
    }
    
    @keyframes slideInRight {
      from {
        transform: translateX(100%);
        opacity: 0;
      }
      to {
        transform: translateX(0);
        opacity: 1;
      }
    }
    
    /* 移除可能导致问题的表单项样式 */
  `;

  document.head.appendChild(style);
}

/**
 * 添加事件监听
 */
function addTooltipEventListeners() {
  let currentTooltip: HTMLElement | null = null;

  // 鼠标进入事件
  document.addEventListener(
    'mouseenter',
    (e) => {
      try {
        const target = e.target as HTMLElement;
        if (!target) return;

        const formItem = getFormItemFromTarget(target);

        if (formItem && isCopyableComponent(target)) {
          showTooltip(target, '双击可复制');
        }
      } catch (error) {
        console.warn('Error in mouseenter event:', error);
      }
    },
    true
  );

  // 鼠标离开事件
  document.addEventListener(
    'mouseleave',
    (e) => {
      try {
        const target = e.target as HTMLElement;
        if (!target) return;

        if (isCopyableComponent(target)) {
          hideTooltip();
        }
      } catch (error) {
        console.warn('Error in mouseleave event:', error);
      }
    },
    true
  );

  // 双击复制事件
  document.addEventListener(
    'dblclick',
    (e) => {
      try {
        const target = e.target as HTMLElement;
        if (!target) return;

        const formItem = getFormItemFromTarget(target);

        if (formItem) {
          e.preventDefault();
          e.stopPropagation();

          const value = extractValueFromFormItem(formItem);
          if (value) {
            copyToClipboard(value);
            showSuccessNotification(value);
            hideTooltip();
          }
        }
      } catch (error) {
        console.warn('Error in dblclick event:', error);
      }
    },
    true
  );

  /**
   * 显示提示
   */
  function showTooltip(element: HTMLElement, text: string) {
    hideTooltip(); // 先隐藏之前的提示

    const tooltip = document.createElement('div');
    tooltip.className = 'copy-tooltip';
    tooltip.textContent = text;

    // 计算位置
    const rect = element.getBoundingClientRect();
    tooltip.style.left = rect.right - 80 + 'px';
    tooltip.style.top = rect.top - 30 + 'px';

    document.body.appendChild(tooltip);
    currentTooltip = tooltip;
  }

  /**
   * 隐藏提示
   */
  function hideTooltip() {
    if (currentTooltip) {
      currentTooltip.remove();
      currentTooltip = null;
    }
  }
}

/**
 * 从目标元素获取表单项
 */
function getFormItemFromTarget(element: HTMLElement): HTMLElement | null {
  if (!element) return null;

  let current: HTMLElement | null = element;
  let depth = 0;

  while (current && depth < 10) {
    if (current.classList && current.classList.contains('ant-form-item')) {
      return current;
    }
    current = current.parentElement;
    depth++;
  }

  return null;
}

/**
 * 判断是否是可复制的组件
 */
function isCopyableComponent(element: HTMLElement): boolean {
  if (!element || !element.matches || !element.closest) return false;

  // 检查是否是表单控件
  const copyableSelectors = [
    '.ant-input',
    '.ant-input-number-input',
    '.ant-textarea',
    '.ant-select-selector',
    '.ant-select-selection-item',
    '.ant-picker-input',
    '.ant-cascader-picker',
    '.ant-tree-select-selector',
    '.ant-checkbox-wrapper',
    '.ant-radio-wrapper',
  ];

  try {
    for (const selector of copyableSelectors) {
      if (element.matches(selector) || element.closest(selector)) {
        return true;
      }
    }
  } catch (error) {
    console.warn('Error checking copyable component:', error);
    return false;
  }

  return false;
}

/**
 * 从表单项提取值
 */
function extractValueFromFormItem(formItem: HTMLElement): string {
  // 1. 尝试从输入框获取值
  const input = formItem.querySelector('input, textarea') as HTMLInputElement;
  if (input && input.value.trim()) {
    return input.value.trim();
  }

  // 2. 尝试从选择器获取显示值
  const selectionItem = formItem.querySelector('.ant-select-selection-item');
  if (selectionItem) {
    return (selectionItem.textContent || '').trim();
  }

  // 3. 尝试从多选获取值
  const selectionItems = formItem.querySelectorAll('.ant-select-selection-item');
  if (selectionItems.length > 0) {
    const values = Array.from(selectionItems)
      .map((item) => (item.textContent || '').trim())
      .filter((text) => text);
    if (values.length > 0) {
      return values.join(', ');
    }
  }

  // 4. 尝试从日期选择器获取值
  const dateInput = formItem.querySelector('.ant-picker-input input') as HTMLInputElement;
  if (dateInput && dateInput.value.trim()) {
    return dateInput.value.trim();
  }

  // 5. 尝试从其他组件获取值
  const otherValue = getOtherComponentValue(formItem);
  if (otherValue) {
    return otherValue;
  }

  return '';
}

/**
 * 获取其他组件的值
 */
function getOtherComponentValue(formItem: HTMLElement): string {
  // 开关组件
  const switchElement = formItem.querySelector('.ant-switch');
  if (switchElement) {
    const isChecked = switchElement.classList.contains('ant-switch-checked');
    return isChecked ? '开启' : '关闭';
  }

  // 复选框
  const checkbox = formItem.querySelector('.ant-checkbox-checked + span');
  if (checkbox) {
    return (checkbox.textContent || '').trim();
  }

  // 单选框
  const radio = formItem.querySelector('.ant-radio-checked + span');
  if (radio) {
    return (radio.textContent || '').trim();
  }

  // 级联选择器
  const cascader = formItem.querySelector('.ant-cascader-picker-label');
  if (cascader) {
    return (cascader.textContent || '').trim();
  }

  return '';
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text: string): Promise<void> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
    } else {
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      textarea.style.left = '-9999px';
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    }
  } catch (error) {
    console.error('复制失败:', error);
  }
}

/**
 * 显示成功通知
 */
function showSuccessNotification(copiedText: string) {
  const notification = document.createElement('div');
  notification.className = 'copy-success-notification';

  const displayText = copiedText.length > 20 ? copiedText.substring(0, 20) + '...' : copiedText;

  notification.textContent = `已复制: ${displayText}`;

  document.body.appendChild(notification);

  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

/**
 * 监听表单项变化
 */
function observeFormItemChanges() {
  // 暂时禁用 DOM 监听，避免可能的副作用
  // const observer = new MutationObserver((mutations) => {
  //   mutations.forEach((mutation) => {
  //     mutation.addedNodes.forEach((node) => {
  //       if (node.nodeType === Node.ELEMENT_NODE) {
  //         const element = node as HTMLElement;
  //         markCopyableFormItems(element);
  //       }
  //     });
  //   });
  // });

  // observer.observe(document.body, {
  //   childList: true,
  //   subtree: true,
  // });

  // 初始化现有表单项
  markCopyableFormItems(document.body);
}

/**
 * 标记可复制的表单项
 */
function markCopyableFormItems(container: HTMLElement) {
  // 暂时不添加任何类名，避免可能的样式冲突
  // const formItems = container.querySelectorAll('.ant-form-item');
  // formItems.forEach((item) => {
  //   const element = item as HTMLElement;
  //   if (!element.classList.contains('form-item-copyable')) {
  //     element.classList.add('form-item-copyable');
  //   }
  // });
}
