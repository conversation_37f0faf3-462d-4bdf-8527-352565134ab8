import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '仪器编号',
    align:"center",
    dataIndex: 'instrumentId_dictText'
   },
   {
    title: '测试id',
    align:"center",
    dataIndex: 'testId'
   },
   {
    title: '状态',
    align:"center",
    dataIndex: 'statusId_dictText'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "仪器编号",
      field: 'instrumentId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"instrument,code,id"
      },
      //colProps: {span: 6},
 	},
	{
      label: "测试id",
      field: 'testId',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "状态",
      field: 'statusId',
      component: 'JSelectMultiple',
      componentProps:{
          dictCode:"status"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '仪器编号',
    field: 'instrumentId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"instrument,code,id"
    },
  },
  {
    label: '测试id',
    field: 'testId',
    component: 'Input',
  },
  {
    label: '状态',
    field: 'statusId',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"status"
     },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  instrumentId: {title: '仪器编号',order: 0,view: 'sel_search', type: 'string',dictTable: "instrument", dictCode: 'id', dictText: 'code',},
  testId: {title: '测试id',order: 1,view: 'text', type: 'string',},
  statusId: {title: '记录状态',order: 2,view: 'list', type: 'string',dictCode: 'status',},
  remark: {title: '备注',order: 3,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
