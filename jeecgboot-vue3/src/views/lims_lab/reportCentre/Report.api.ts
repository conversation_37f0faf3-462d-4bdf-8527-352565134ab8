import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/lims_order/report/list',
  save = '/lims_order/report/add',
  edit = '/lims_order/report/edit',
  deleteOne = '/lims_order/report/delete',
  deleteBatch = '/lims_order/report/deleteBatch',
  exportXls = '/lims_order/report/exportXls',
  generate = '/oo/report/generate',
  batchGenerate = '/oo/report/batchGenerate',
  getOOEditorConfig = '/oo/editor-config',
  TestResultList = '/lims_order/report/queryTestResultByMainReportId',
  sign = '/oo/sign',
}
/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;
/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 批量删除
 * @param params
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 保存或者更新
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

export const generate = (params, handleSuccess) =>
  defHttp.get({ url: Api.generate, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });

export const batchGenerate = (params) => defHttp.post({ url: Api.batchGenerate, params }, { joinParamsToUrl: true });

// 获取OnlyOffice EditorConfig
export const getOOEditorConfig = async (reportId: String) => {
  return await defHttp.get({ url: Api.getOOEditorConfig, params: { biz: 'report', bizId: reportId, permission: null } });
};

// 获取OnlyOffice EditorConfig
export const getOOEditorConfigWithBiz = async (reportId: String,biz: String) => {
  return await defHttp.get({ url: Api.getOOEditorConfig, params: { biz: biz, bizId: reportId, permission: null } });
};

/**
 * 子表列表接口
 * @param params
 */
export const TestResultList = (params) =>
  defHttp.get({url: Api.TestResultList, params},{isTransformResponse:false});

export const sign = (params) => {
  return defHttp.post({ url: Api.sign, params }, { joinParamsToUrl: true });
};
