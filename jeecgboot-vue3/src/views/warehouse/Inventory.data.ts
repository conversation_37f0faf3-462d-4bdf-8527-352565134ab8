import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import {initDictOptions} from "@/utils/dict";
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '仓库',
    align:"center",
    dataIndex: 'warehouseId_dictText'
   },
   {
    title: '物品编号',
    align:"center",
    dataIndex: 'articleNo'
   },
  {
    title: '物品名称',
    align:"center",
    dataIndex: 'goodsname'
  },
   {
    title: '物品类型',
    align:"center",
    dataIndex: 'articleTypeId_dictText'
   },
   {
    title: '数量',
    align:"center",
    dataIndex: 'amount'
   },
   {
    title: '单位',
    align:"center",
    dataIndex: 'unitId_dictText'
   },
   {
    title: '库位',
    align:"center",
    dataIndex: 'boxId_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "仓库",
      field: 'warehouseId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"sys_warehouse,name,id"
      },
      //colProps: {span: 6},
 	},
	{
      label: "物品编号",
      field: 'articleNo',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "物品类型",
      field: 'articleTypeId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"warehouse_goods_type"
      },
      //colProps: {span: 6},
 	},
	{
      label: "库位",
      field: 'boxId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"sys_warehouse_box,code,id"
      },
      //colProps: {span: 6},
 	},
  {
    label: '研发项目编码',
    field: 'rdid',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'rd_project,rd_no,id',
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '仓库',
    field: 'warehouseId',
    component: 'JSearchSelect',
    componentProps: ({ formModel, formActionType }) => {
      return {
        dict:"sys_warehouse,name,id",
        onChange: async (val: any) => {
          const { updateSchema } = formActionType;
          const dictData = await initDictOptions(`sys_warehouse_box where warehouse_id = '${val}',code,id`);
          formModel.boxId = undefined;
          console.log(dictData);
          updateSchema([
            {
              field: 'boxId',
              componentProps: {
                options: dictData,
              },
            },
          ]);
        },
      };
    },
      dynamicRules: ({ model, schema }) => {
          return [{ required: true, message: '请选择仓库!' }];
      },
  },
  {
    label: '库位',
    field: 'boxId',
    component: 'JSearchSelect',
    componentProps: {
      replaceFields: {
        title: 'code',
        key: 'id',
        value: 'id',
      },
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请选择库位!' }];
    },
  },
  {
    label: '物品编号',
    field: 'articleNo',
    component: 'Input',
      dynamicRules: ({ model, schema }) => {
          return [{ required: true, message: '请输入编号!' }];
      },
  },
  {
    label: '物品类型',
    field: 'articleTypeId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"warehouse_goods_type"
    },
      dynamicRules: ({ model, schema }) => {
          return [{ required: true, message: '请选择类型!' }];
      },
  },
  {
    label: '数量',
    field: 'amount',
    component: 'Input',
      dynamicRules: ({ model, schema }) => {
          return [{ required: true, message: '请填写数量!' }];
      },
  },
  {
    label: '单位',
    field: 'unitId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"sys_unit,unit_name,id"
    },
      dynamicRules: ({ model, schema }) => {
          return [{ required: true, message: '请选择单位!' }];
      },
    show:false
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  warehouseId: {title: '仓库',order: 0,view: 'sel_search', type: 'string',dictTable: "sys_warehouse", dictCode: 'id', dictText: 'name',},
  articleNo: {title: '物品编号',order: 1,view: 'text', type: 'string',},
  articleTypeId: {title: '物品类型',order: 2,view: 'sel_search', type: 'string',dictCode: 'warehouse_goods_type',},
  amount: {title: '数量',order: 3,view: 'text', type: 'string',},
  unitId: {title: '单位',order: 4,view: 'sel_search', type: 'string',dictTable: "sys_unit", dictCode: 'id', dictText: 'unit_name',},
  boxId: {title: '库位',order: 5,view: 'sel_search', type: 'string',dictTable: "sys_warehouse_box", dictCode: 'id', dictText: 'code',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
