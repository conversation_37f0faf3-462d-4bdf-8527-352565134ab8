export function getOnlyOfficeGlobalConfig() {
  // 获取当前域名和端口
  const currentProtocol = window.location.protocol;
  const currentHost = window.location.hostname;
  const currentPort = window.location.port;

  return {
    onlyOffice: {
      // 使用当前域名和端口
      documentServerUrl: `${currentProtocol}//${currentHost}:${currentPort}/`,
      editorConfig: {
        callbackUrl: `${currentProtocol}//${currentHost}:3380/jeecg-boot/oo/callback`,
        lang: 'zh-CN',
      },
    },
  };
}
