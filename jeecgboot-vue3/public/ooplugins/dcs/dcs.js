(function (window, undefined) {
  window.oncontextmenu = function(e)
	{
		if (e.preventDefault)
			e.preventDefault();
		if (e.stopPropagation)
			e.stopPropagation();
		return false;
  };
  window.Asc.plugin.init = function(initData) {
    // 搜索功能
    window.searchLinks = function() {
        const name = document.getElementById('nameSearch').value;
        loadLinks(name);
    };
    
    // 加载链接列表
    function loadLinks(name) {
        if (!Asc.plugin.info.options) {
            console.error('Token not available');
            return;
        }

        const headers = {
            'X-Access-Token': Asc.plugin.info.options.token,
            'Content-Type': 'application/json'
        };

        fetch(`/jeecg-boot/dcs/dcsDoc/getRefedDocList?name=${encodeURIComponent(name)}`, {
            method: 'GET',
            headers: headers
        })
        .then(response => response.json())
        .then(data => {
            const linkList = document.getElementById('staticLinkList');
            linkList.innerHTML = '';
            
            data.result.forEach(link => {
                const li = document.createElement('li');

                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.value = link.url;
                checkbox.id = `checkbox-${link.id}`;

                const label = document.createElement('label');
                label.htmlFor = checkbox.id;
                label.textContent = link.name;

                li.appendChild(checkbox);
                li.appendChild(label);
                linkList.appendChild(li);
            });
        })
        .catch(error => console.error('Error loading links:', error));
    }

    // 在插件 iframe 之外释放鼠标按钮时调用的函数
    window.Asc.plugin.onExternalMouseUp = function () {
      var event = document.createEvent('MouseEvents');
      event.initMouseEvent('mouseup', true, true, window, 1, 0, 0, 0, 0, false, false, false, false, 0, null);
      document.dispatchEvent(event);
    };

    window.Asc.plugin.button = function(id) {
        if (id == 0) {
            const checkedLinks = document.querySelectorAll('#staticLinkList input[type="checkbox"]:checked');
            let linksHtml = '';
            let refedDocs = []; // 用于存储复选框的 id

            checkedLinks.forEach(checkbox => {
                const linkElement = checkbox.nextElementSibling;
                linksHtml += `<a href="${checkbox.value}">${linkElement.textContent}</a><br>`;
                refedDocs.push(checkbox.id); // 收集复选框的 id
            });

            // 调用后端接口 updateRefedDocs
            const docId = Asc.plugin.info.options.docId;
            const idsString = refedDocs.join(','); // 将 id 连接成字符串

            fetch(`/jeecg-boot/dcs/dcsDoc/updateRefedDocs`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Access-Token': Asc.plugin.info.options.token,
                },
                body: JSON.stringify({
                    docId: docId,
                    refedDocs: idsString, // 传递 id 字符串
                }),
            })
            .then(response => response.json())
            .then(data => {
                console.log('Update successful:', data);
                this.executeMethod("PasteHtml", [linksHtml]);
                this.executeCommand("close", "");
            })
            .catch(error => console.error('Error updating documents:', error));
        } else {
            this.executeCommand("close", "");
        }
    };
  };

  // 处理回车事件
  window.handleKeyDown = function(event) {
    if (event.key === 'Enter') { // 检查是否按下回车键
      event.preventDefault(); // 防止表单提交
      searchLinks(); // 调用 searchLinks 函数
    }
  };
})(window, undefined);