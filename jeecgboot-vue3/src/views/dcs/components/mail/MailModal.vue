<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="600" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #attachmentSlot="{ model }">
        <div v-if="documentUrl" class="attachment-container">
          <Tag color="blue" class="attachment-tag">
            <paper-clip-outlined class="attachment-icon" />
            <a :href="documentUrl" target="_blank" class="attachment-link">{{ documentName }}</a>
          </Tag>
        </div>
        <div v-else class="attachment-container">无附件</div>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form';
  import { mailFormSchema, MailFormData } from './Mail.data';
  import { sendEmail } from './Mail.api';
  import { PaperClipOutlined } from '@ant-design/icons-vue';
  import { Tag } from 'ant-design-vue';

  const emit = defineEmits(['register']);
  const title = ref('发送邮件');
  const documentUrl = ref('');
  const documentName = computed(() => {
    // 从URL中提取文件名
    if (documentUrl.value) {
      const urlParts = documentUrl.value.split('/');
      return urlParts[urlParts.length - 1];
    }
    return '文档附件';
  });

  // 注册表单
  const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
    labelWidth: 80,
    schemas: mailFormSchema,
    showActionButtonGroup: false,
  });

  // 注册弹窗
  const [registerModal, { closeModal }] = useModalInner((data) => {
    resetFields();
    if (data) {
      // 如果有文档URL，设置为附件
      if (data.url) {
        documentUrl.value = data.url;
      }

      // 设置邮件相关字段
      const formData: MailFormData = {
        to: data.to || '',
        cc: data.cc || '',
        bcc: data.bcc || '',
        subject: data.subject || '',
        body: data.body || '',
        attachments: data.url ? [data.url] : [],
      };

      // 设置表单字段值
      setFieldsValue(formData);
    }
  });

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await validate();
      // 将字符串转换为数组
      const params = {
        ...values,
        // 将分号分隔的字符串转换为数组
        to: values.to ? values.to.split(';').map((item: string) => item.trim()) : [],
        cc: values.cc ? values.cc.split(';').map((item: string) => item.trim()) : [],
        bcc: values.bcc ? values.bcc.split(';').map((item: string) => item.trim()) : [],
        // 确保附件是数组
        attachments: documentUrl.value ? [documentUrl.value] : [],
      };
      await sendEmail(params as MailFormData);
      closeModal();
    } catch (error) {
      console.error('表单验证或发送邮件失败:', error);
      return false;
    }
  };
</script>

<style lang="less" scoped>
  .attachment-container {
    padding: 8px 0;
    font-size: 14px;

    .attachment-tag {
      padding: 6px 10px;
      font-size: 14px;
      height: auto;

      .attachment-icon {
        font-size: 16px;
        margin-right: 6px;
      }

      .attachment-link {
        font-size: 14px;
        padding: 3px;
      }
    }
  }
</style>
