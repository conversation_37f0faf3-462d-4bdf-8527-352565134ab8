<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button :disabled="!canEdit" type="primary" v-auth="'lims_core:consumptive:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :disabled="!canEdit" :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <ColumnConsumTableModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="lims_core-consumptive" setup>
import { ref, reactive, computed, unref, defineProps } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import ConsumptiveModal from './components/ConsumptiveModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './ColumnConsumTable.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from '../Consumptive.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { hiprint } from 'vue-plugin-hiprint';
  import barcode from '@/templates/consumptivebarcode';
  import { message } from 'ant-design-vue';
  import ColumnConsumTableModal from '@/views/lims_core/column_consum/ColumnConsumTableModal.vue';

  /**
   * props声明
   */
  const props = defineProps({
    sampleId: {
      type: [String, Number],
    },
    customerId: {
      type: [String, Number],
    },
    canEdit: {
      type: Boolean,
      default: false,
    },
  });

  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting:{
        cacheKey: 'ColumnConsumTable',
      },
      title: '耗材台账',
      api: list,
      columns,
      canResize: false,
      clickToRowSelect: true,
      formConfig: {

      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.sampleId = props.sampleId;
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '耗材台账',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      record: {
        sampleId: props.sampleId,
        customerId: props.customerId,
      },
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:consumptive:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_core:consumptive:delete',
      },
    ];
  }
  /**
   * 打印
   */
  function print() {
    if (selectedRows.value.length == 0) {
      alert('请选择要打印的数据');
      return;
    }
    let hiprintTemplate = new hiprint.PrintTemplate({ template: barcode });
    let printDatas = [];
    selectedRows.value.forEach((item) => {
      console.log(item);

      let printData = {
        name: item.name,
        purity: item.purity,
        createTime: item.createTime,
        createBy: item.createBy,
        effectiveLength: item.effectiveLength,
      };
      printDatas.push(printData);
    });
    // 打印
    hiprintTemplate.print(printDatas);
  }

  function handleOutApply() {
    if (selectedRows.value.length == 0 || selectedRows.value.length > 1) {
      message.error('请选择一条数据');
      return;
    }
    selectedRows.value.forEach((item) => {
      const selectedRow = item;
      const record = {
        articleNo: selectedRow.code,
        articleTypeId: 'HC',
      };
      console.log(selectedRow);
      ApplyopenModal(true, {
        record,
        isUpdate: false,
        showFooter: true,
        isInitializationPart: true,
      });
    });
  }
  function handleOutInventory() {
    if (selectedRows.value.length == 0 || selectedRows.value.length > 1) {
      message.error('请选择一条数据');
      return;
    }
    selectedRows.value.forEach((item) => {
      const selectedRow = item;
      // if (selectedRow.receiveCount == null){
      //   message.error('请先填写数量');
      //   return;
      // }
      // if (selectedRow.receiveCountUnit == null){
      //   message.error('请先填写单位');
      //   return;
      // }
      const record = {
        articleNo: selectedRow.code,
        articleTypeId: 'HC',
      };
      console.log(selectedRow);
      InventoryregModal(true, {
        record,
        isUpdate: false,
        showFooter: true,
        isInitializationPart: true,
      });
    });
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
