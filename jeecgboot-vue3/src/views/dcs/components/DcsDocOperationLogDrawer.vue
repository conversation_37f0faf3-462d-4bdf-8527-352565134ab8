<template>
  <BasicDrawer destroyOnClose v-bind="$attrs" title="审计" width="40%" @register="registerDrawer" :loading="loading">
    <BasicTable @register="registerTable" />
  </BasicDrawer>
</template>

<script setup lang="ts">
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { BasicTable } from '/@/components/Table';
  import { ref } from 'vue';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { BasicColumn } from '/@/components/Table';
  import { queryByDocId } from '../DcsDocOperationLog.api';

  //
  const loading = ref(false);
  let curDocId: String;

  // 注册抽屉组件
  const [registerDrawer] = useDrawerInner((docId) => {
    console.log(`output->docId`, docId);
    curDocId = docId;
  });

  //定义表格列字段
  const columns: BasicColumn[] = [
    {
      title: '操作',
      dataIndex: 'operationType',
      key: 'operationType',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
    },
    {
      title: '操作人',
      dataIndex: 'createBy',
      key: 'createBy',
    },
    {
      title: '操作时间',
      dataIndex: 'createTime',
      key: 'createTime',
    },
  ];

  const loadData = async () => {
    loading.value = true;
    let data = await queryByDocId(curDocId);
    loading.value = false;
    return data.records;
  };

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      columns: columns,
      size: 'small',
      api: loadData,
      // dataSource: dcsDocOperationLog.value,
      rowKey: 'id',
      showActionColumn: false,
      pagination: true,
      immediate: true,
    },
  });

  //BasicTable绑定注册
  const [registerTable] = tableContext;
</script>
