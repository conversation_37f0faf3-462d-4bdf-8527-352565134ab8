<template>
  <PageWrapper :title="route.params.quotation_no" contentBackground>
    <template #extra>
      <a-button> 操作一 </a-button>
      <a-button> 操作二 </a-button>
      <a-button type="primary"> 主操作 </a-button>
    </template>
    <a-divider orientation="left">报价信息</a-divider>
    <div class="pt-4 m-4 desc-wrap">
      <a-descriptions size="middle" :column="2">
        <a-descriptions-item label="客户名称"> {{ quotation.customerId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="客户联系人"> {{ quotation.customerContactId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="周期"> {{ quotation.tat }}个工作日 </a-descriptions-item>
        <a-descriptions-item label="加急类别">{{ quotation.serviceTypeId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="创建人">{{ quotation.createBy }}</a-descriptions-item>
        <a-descriptions-item label="创建日期">{{ quotation.createTime }}</a-descriptions-item>
        <a-descriptions-item label="总金额">¥{{ quotation.totalAmount }}</a-descriptions-item>
        <a-descriptions-item label=""><a @click="handleQuotationEdit(quotation)">编辑报价</a></a-descriptions-item>
      </a-descriptions>
    </div>
    <a-divider orientation="left">样品信息</a-divider>
    <div :style="{ marginBottom: '16px', marginLeft: '16px' }">
      <a-button type="primary" @click="handleSampleAdd" preIcon="ant-design:plus-outlined">新增样品</a-button>
    </div>
    <a-tabs v-model:activeKey="activeKey" type="editable-card" @edit="onEdit" hide-add>
      <a-tab-pane v-for="sample in samples" :key="sample.id" :tab="sample.name" :closable="sample.closable">
        <a-card>
          <a-descriptions size="middle" :column="2">
            <a-descriptions-item label="样品名称"> {{ sample.name }} </a-descriptions-item>
            <a-descriptions-item label="样品编号"> {{ sample.sampleNo }} </a-descriptions-item>
            <a-descriptions-item label="样品批号"> {{ sample.lotNo }} </a-descriptions-item>
            <a-descriptions-item label="样品规格">{{ sample.spec }}</a-descriptions-item>
            <a-descriptions-item label="样品原价">{{ sample.originalPrice }}</a-descriptions-item>
            <a-descriptions-item label="样品折扣">{{ sample.discount }}</a-descriptions-item>
            <a-descriptions-item label="最终价格">{{ sample.finalPrice }}</a-descriptions-item>
            <a-descriptions-item label=""><a @click="handleSampleEdit(sample)">编辑样品</a></a-descriptions-item>
          </a-descriptions>
        </a-card>
        <a-divider orientation="left">测试信息</a-divider>
      </a-tab-pane>
    </a-tabs>
    <BasicTable v-if="activeKey" @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'lims_order:test:add'" @click="handleTestAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!--            <a-button  type="primary" v-auth="'lims_order:test:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>-->
        <!--            <j-upload-button type="primary" v-auth="'lims_order:test:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>-->
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_order:test:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
  </PageWrapper>
  <!-- 表单区域 -->
  <SampleModal @register="registerModal" @success="handleSampleSuccess" />
  <TestModal @register="registerTestModal" @success="handleSuccess" />
  <QuotationModal @register="registerQuotationModal" @success="handleSampleSuccess" />
  <JPopupOnlReportModal @register="regModal" code="product_selector" multi="true" @ok="callBack" />
</template>

<script setup lang="ts">
  import { columns, searchFormSchema, superQuerySchema } from './Test.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, addTest } from './Test.api';
  import SampleModal from '../sample/SampleTableModal.vue';
  import TestModal from './components/TestModal.vue';
  import { list as quotationList } from '../Quotation.api';
  import { list as sampleList, deleteOne as deleteOneSample } from '../sample/SampleTable.api';
  import { useUserStore } from '/@/store/modules/user';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { PageWrapper } from '@/components/Page';
  import { ref, reactive, computed, unref, onMounted, watch } from 'vue';
  import { useRoute } from 'vue-router';
  import { message, Modal } from 'ant-design-vue';
  import JPopupOnlReportModal from '@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';
  import QuotationModal from '@/views/lims_order/components/QuotationModal.vue';
  const actionName = ref('');
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const route = useRoute();
  const quotation = ref({});
  //注册model
  const [registerModal, { openModal }] = useModal();
  const [registerTestModal, { openModal: openTestModal }] = useModal();
  const [registerQuotationModal, { openModal: openQuotationModal }] = useModal();
  //注册model
  const [regModal, { openModal: openProductModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '试验标本',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '试验标本',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  async function handleSuccess() {
    console.log('handleSuccess');

    (selectedRowKeys.value = []) && reload();
    await initData(false);
  }
  async function handleSampleSuccess() {
    console.log('handleSampleSuccess');

    (selectedRowKeys.value = []) && reload();
    await initData(true);
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_order:test:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'lims_order:test:delete',
      },
    ];
  }
  /**
   * 编辑样品事件
   */
  function handleSampleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openTestModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 编辑报价事件
   */
  function handleQuotationEdit(record: Recordable) {
    openQuotationModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openTestModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  const samples = ref<{ name: string; id: string; closable?: boolean }[]>([]);
  const activeKey = ref(samples.value[0] ? samples.value[0].id : '');
  const newTabIndex = ref(0);
  /**
   * 新增tab
   */
  const handleSampleAdd = () => {
    actionName.value = 'add';
    openModal(true, {
      quotation: quotation.value,
      isUpdate: false,
      showFooter: true,
    });
  };
  /**
   * 移除tab
   * @param targetKey
   */
  const remove = async (targetKey: string) => {
    await deleteOneSample({ id: targetKey }, handleSampleSuccess);
  };
  /**
   * tab切换
   * @param key
   */
  const onEdit = (targetKey: string | MouseEvent, action: string) => {
    console.log(targetKey, action);
    if (action === 'add') {
    } else {
      actionName.value = 'remove';
      Modal.confirm({
        title: '删除',
        content: '确定删除该样品吗？',
        onOk() {
          remove(targetKey as string);
        },
      });
    }
  };

  /**
   * 初始化数据
   */
  const initData = async (changeKey: boolean) => {
    const quotationReq = await quotationList({
      column: 'createTime',
      order: 'desc',
      pageNo: 1,
      pageSize: 10,
      quotationNo: route.params.quotation_no,
    });
    quotation.value = quotationReq.records[0];

    const sampleReq = await sampleList({
      column: 'createTime',
      order: 'asc',
      pageNo: 1,
      pageSize: 10,
      quotationId: quotation.value.id,
    });
    if (changeKey) {
      activeKey.value = sampleReq.records[0].id;
      if (actionName.value.length > 0) {
        activeKey.value = sampleReq.records[sampleReq.records.length - 1].id;
      }
    }
    samples.value = sampleReq.records.map((item) => {
      return {
        ...item,
        closable: true,
      };
    });
  };

  const handleTestAdd = () => {
    openProductModal(true);
  };

  const callBack = (rows) => {
    //取出rows中的id,成为新数组
    const ids = rows.map((row) => row.id);
    addTest({ ids: ids, sampleId: activeKey.value }, handleSuccess);
  };

  onMounted(() => {
    initData(true);
  });

  watch(activeKey, (value, oldValue, onCleanup) => {
    queryParam.sampleId = value;
    initData(false);
    (selectedRowKeys.value = []) && reload();
  });
</script>

<style scoped lang="less"></style>
