import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { createImgPreview } from '/@/components/Preview';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '源表名',
    align: 'center',
    dataIndex: 'sourceTable',
    ifShow: false,
  },
  {
    title: '源表id',
    align: 'center',
    dataIndex: 'sourceId',
    ifShow: false,
  },
  {
    title: '文件地址',
    align: 'left',
    width: 200,
    dataIndex: 'url',
    customRender: ({ text }) => {
      return (
        <span
        >
          {text?.split('/').pop()}
        </span>
      );
    },
  },
  {
    title: '预览',
    align: 'center',
    width:80,
    dataIndex: 'url',
    customRender: ({ text }) => {
      return (
        <a-button
          type="link"
          onClick={() => {
            if (text.indexOf('.pdf') > -1) {
              window.open('/pdfjs/web/viewer.html?file='+encodeURIComponent(text), '', `width=800,height=600,left=0,top=0,menubar=no,toolbar=no,location=no,status=no,resizable=yes`);
            } else if(text.indexOf('.doc') > -1 || text.indexOf('.docx') > -1) {
              window.open(text,'_blank');
            }else{
              createImgPreview({ imageList: [text], maskClosable: true });
            }
          }}
        >
          预览
        </a-button>
      );
    },
  },
  {
    title: '类型',
    width:100,
    align: 'center',
    dataIndex: 'type',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '源表名',
    field: 'sourceTable',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '源表id',
    field: 'sourceId',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '文件地址(可多个文件)',
    field: 'url',
    component: 'JUpload',
  },
  {
    label: '类型',
    field: 'type',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  sourceTable: { title: '源表名', order: 0, view: 'text', type: 'string' },
  sourceId: { title: '源表id', order: 1, view: 'text', type: 'string' },
  url: { title: '文件地址', order: 2, view: 'text', type: 'string' },
  type: { title: '类型', order: 3, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
