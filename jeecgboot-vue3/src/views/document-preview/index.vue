<template>
  <div class="document-preview-container">
    <DocumentViewer
      :documentServerUrl="documentServerUrl"
      :documentUrl="documentUrl"
      :documentTitle="documentTitle"
      :documentType="documentType"
      :screenId="screenId"
      :readOnly="true"
      :editorConfig="editorConfig"
      :floatButtons="floatButtons"
      @button-click="handleDocEditorButtonClick"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import DocumentViewer from '/@/components/oo/DocumentViewer.vue';
  import { getOOEditorConfig, getOOEditorConfigWithBiz, sign } from '/@/views/lims_lab/reportCentre/Report.api';
  import { getOnlyOfficeGlobalConfig } from '@/utils/onlyOfficeGlobalConfig';

  // 路由和参数
  const route = useRoute();

  // 文档属性
  const documentServerUrl = ref(getOnlyOfficeGlobalConfig().onlyOffice.documentServerUrl);
  const documentUrl = ref('');
  const documentTitle = ref('文档预览');
  const documentType = ref('word');
  const screenId = ref('primary');
  const editorConfig = ref<Record<string, any>>({});
  const documentId = ref('');
  const documentSource = ref('report');

  // 浮动按钮配置
  const floatButtons = ref([
    {
      tooltip: '发送邮件',
      icon: 'MailOutlined',
      type: 'primary',
      action: 'email',
    },
    {
      tooltip: '签发',
      icon: 'EditTwoTone',
      type: 'primary',
      action: 'sign',
    },
    {
      tooltip: '审批',
      icon: 'CheckOutlined',
      type: 'primary',
      action: 'approve',
    },
  ]);

  // 更新文档配置和显示
  const updateDocumentConfig = (config: any) => {
    if (!config?.document?.url) {
      console.error('无效的文档配置');
      return;
    }

    console.log('更新前的 config:', config);
    documentUrl.value = config.document.url;
    documentType.value = config.documentType || 'word';
    editorConfig.value = config;

    // 更新标题
    const fileName = config.document.title || config.document.url.split('/').pop() || '文档';
    documentTitle.value = `国标医药数智平台: ${fileName}`;
    document.title = documentTitle.value;
  };

  // 处理审批和签发
  const handleApprove = async (reportIds: string[], status: string) => {
    try {
      console.log(`开始${status === 'approve' ? '审批' : '签发'}文档`, { reportIds, status });
      const res = await sign({ ids: reportIds, status: status });
      console.log('res', res);
      if (res[0]) {
        console.log(`${status === 'approve' ? '审批' : '签发'}成功，更新文档配置`);
        //刷新当前页面(模拟F5)
        if (status === 'approve') {
          window.location.reload();
        } else {
          //更改当前url
          const newUrl = new URL(window.location.href);
          newUrl.searchParams.set('source', 'signreport');
          window.history.pushState({}, '', newUrl.toString());
          window.location.reload();
        }
      }
    } catch (error) {
      console.error(`${status === 'approve' ? '审批' : '签发'}失败`, error);
    }
  };

  // 处理按钮点击
  const handleDocEditorButtonClick = (event: any) => {
    console.log('文档编辑器按钮点击:', event);

    if (event.action === 'close') {
      window.close();
    } else if (event.action === 'email') {
      console.log('发送邮件:', event.documentId);
    } else if (event.action === 'approve') {
      console.log('审批文档:', event.allKeys);
      handleApprove(event.allKeys || [], 'approve');
    } else if (event.action === 'sign') {
      console.log('签发文档:', event.allKeys);
      handleApprove(event.allKeys || [], 'sign');
    }
  };

  // 从URL参数加载文档
  const loadDocumentFromParams = async () => {
    try {
      const id = route.query.id as string;
      if (!id) {
        console.error('未提供文档ID');
        return;
      }

      documentId.value = id;
      documentSource.value = (route.query.source as string) || 'report';
      screenId.value = (route.query.screen as string) || 'primary';

      console.log('加载文档', { id, source: documentSource.value });

      // 初始加载时从后端获取配置
      const ooConfig = await getOOEditorConfigWithBiz(id, documentSource.value);
      if (ooConfig) {
        updateDocumentConfig(ooConfig);
      }

      console.log('文档加载完成', {
        documentId: documentId.value,
        documentUrl: documentUrl.value,
        documentType: documentType.value,
        screenId: screenId.value,
      });
    } catch (error) {
      console.error('加载文档时出错:', error);
    }
  };

  // 组件挂载时初始化
  onMounted(() => {
    loadDocumentFromParams();
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.overflow = 'hidden';
  });
</script>

<style lang="less" scoped>
  .document-preview-container {
    width: 100%;
    height: 100vh;
    overflow: hidden;
    background-color: #f5f5f5;
  }
</style>
