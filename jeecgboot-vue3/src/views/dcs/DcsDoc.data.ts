import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { preview } from 'vite';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '编号',
    align: 'center',
    dataIndex: 'docNo',
    width: '200px',
    resizable: true,
    fixed: 'left',
  },
  {
    title: '名称',
    align: 'center',
    dataIndex: 'docName',
    resizable: true,
    fixed: 'left',
  },
  {
    title: '版本',
    align: 'center',
    dataIndex: 'ver',
    resizable: true,
    fixed: 'left',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'typeId_dictText',
    resizable: true,
  },

  {
    title: '涉及部门',
    align: 'center',
    dataIndex: 'relatedDeptId',
    resizable: true,
    customRender: ({ text }) => {
      return render.renderCategoryTree(text, 'sys_depart');
    },
  },
  {
    title: '审批结果',
    align: 'center',
    dataIndex: 'status_dictText',
    resizable: true,
  },
  {
    title: '起草人',
    align: 'center',
    dataIndex: 'createBy',
    resizable: true,
  },
  {
    title: '起草时间',
    align: 'center',
    dataIndex: 'createTime',
    resizable: true,
  },
  {
    title: '协同起草人',
    align: 'center',
    dataIndex: 'coDrafters_dictText',
    resizable: true,
  },
  {
    title: '生效日期',
    align: 'center',
    dataIndex: 'effectiveDate',
    resizable: true,
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '废止日期',
    align: 'center',
    dataIndex: 'abandonDate',
    resizable: true,
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'effectiveStatus_dictText',
    resizable: true,
  },
  {
    title: '审批类型',
    align: 'center',
    dataIndex: 'applyTypeId_dictText',
    resizable: true,
  },
  {
    title: '复审时间',
    align: 'center',
    dataIndex: 'reviewTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '状态',
    field: 'effectiveStatus',
    component: 'JSelectMultiple',
    componentProps: {
      dictCode: 'dcs_doc_status',
    },
    //colProps: {span: 6},
  },
  {
    label: '编号',
    field: 'docNo',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '名称',
    field: 'docName',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '类型',
    field: 'typeId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'system_doc_type,type_name,id',
    },
    //colProps: {span: 6},
  },
  {
    label: '协同起草人',
    field: 'coDrafters',
    component: 'JSelectUser',
    componentProps: {},
  },
  {
    label: '生效日期',
    field: 'effectiveDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
  {
    label: '废止日期',
    field: 'abandonDate',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'docName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
  },
  {
    label: '版本',
    field: 'ver',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入版本!' }];
    },
  },
  {
    label: '类型',
    field: 'typeId',
    component: 'JTreeSelect',
    componentProps: {
      dict: 'system_doc_type,type_name,id',
      pidField: 'parent_id',
      hasChildField: 'has_child',
      pidValue: '0',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入类型!' }];
    },
  },
  {
    label: '存储地址',
    field: 'url',
    component: 'JUpload',
    componentProps: {
      maxCount: 1,
    },
  },
  {
    label: '涉及部门',
    field: 'relatedDeptId',
    component: 'JSelectDept',
    componentProps: {},
  },
  {
    label: '协同起草人',
    field: 'coDrafters',
    component: 'JSelectUser',
    componentProps: {},
  },
  // {
  //   label: '审批结果',
  //   field: 'status',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'wework_bpm_status',
  //   },
  //   dynamicDisabled: true,
  // },
  {
    label: '生效日期',
    field: 'effectiveDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '废止日期',
    field: 'abandonDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '状态',
    field: 'effectiveStatus',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'dcs_doc_status',
      type: 'radio',
    },
    defaultValue: 'effective',
  },
  // {
  //   label: '审批类型',
  //   field: 'applyTypeId',
  //   component: 'JDictSelectTag',
  //   componentProps: {
  //     dictCode: 'wework_apply_type',
  //   },
  //   dynamicDisabled: true,
  // },

  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  docNo: { title: '编号', order: 0, view: 'text', type: 'string' },
  docName: { title: '名称', order: 1, view: 'text', type: 'string' },
  ver: { title: '版本', order: 2, view: 'number', type: 'number' },
  typeId: { title: '类型', order: 4, view: 'sel_search', type: 'string', dictTable: 'system_doc_type', dictCode: 'id', dictText: 'type_name' },
  coDrafters: { title: '协同起草人', order: 5, view: 'sel_user', type: 'string' },
  relatedDeptId: { title: '涉及部门', order: 5, view: 'sel_depart', type: 'string' },
  status: { title: '审批结果', order: 6, view: 'number', type: 'number', dictCode: 'wework_bpm_status' },
  createBy: { title: '起草人', order: 7, view: 'text', type: 'string' },
  createTime: { title: '起草时间', order: 8, view: 'datetime', type: 'string' },
  effectiveDate: { title: '生效日期', order: 9, view: 'date', type: 'string' },
  abandonDate: { title: '废止日期', order: 10, view: 'date', type: 'string' },
  effectiveStatus: { title: '状态', order: 11, view: 'radio', type: 'string', dictCode: 'dcs_doc_status' },
  applyTypeId: { title: '审批类型', order: 15, view: 'list', type: 'string', dictCode: 'wework_apply_type' },
  reviewTime: { title: '复审时间', order: 16, view: 'datetime', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
