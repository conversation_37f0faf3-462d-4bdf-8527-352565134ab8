<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" v-auth="'dcs:training:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" v-auth="'dcs:training:exportXls'" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button type="primary" v-auth="'dcs:training:importExcel'" preIcon="ant-design:import-outlined" @click="onImportXls"
          >导入</j-upload-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
              <a-menu-item key="2" @click="batchGenerateTrainingRecord">
                <Icon icon="ant-design:file-text-twotone" />
                生成培训记录
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'dcs:training:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <TrainingModal @register="registerModal" @success="handleSuccess" />
    <DocEditor @register="registerModalDocEditor" />
  </div>
</template>

<script lang="ts" name="dcs-training" setup>
  import { ref, reactive, computed, unref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useModal } from '/@/components/Modal';
  import TrainingModal from './components/TrainingModal.vue';
  import DocEditor from '@/views/dcs/components/DocEditor.vue';
  import { columns, searchFormSchema, superQuerySchema } from './Training.data';
  import {
    list,
    deleteOne,
    batchDelete,
    getImportUrl,
    getExportUrl,
    createMeeting,
    raiseCheackIn,
    geneOneTraingRecord,
    batchGeneTraingRecord,
    getOOEditorConfig,
  } from './Training.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '培训',
      api: list,
      columns,
      canResize: false,
      clickToRowSelect: true,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [['startTime', ['startTime_begin', 'startTime_end'], 'YYYY-MM-DD HH:mm:ss']],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '培训',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  async function generateTrainingRecord(record) {
    await geneOneTraingRecord({ id: record.id }).then((res) => {
      if (res === 'success') {
        preview(record);
      }
    });
  }

  async function batchGenerateTrainingRecord() {
    await batchGeneTraingRecord({ ids: selectedRowKeys.value }).then((res) => {
      if (res === 'success') {
        previewBatch();
      }
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 预览
   */
  async function preview(record) {
    (selectedRowKeys.value = []) && reload();
    const ooConfig = await getOOEditorConfig(record.id);
    openDocEditorModal(true, ooConfig);
  }

  /**
   * 预览
   */
  async function previewBatch() {
    if (selectedRowKeys.value.length > 0) {
      for (let i = 0; i < selectedRowKeys.value.length; i++) {
        const ooConfig = await getOOEditorConfig(selectedRowKeys.value[i]);
        openDocEditorModal(true, ooConfig);
      }
      (selectedRowKeys.value = []) && reload();
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'dcs:training:edit',
      },
    ];
  }

  /**
   * 发起会议
   */
  async function handleCreateMeeting(record) {
    console.log('发起会议', record);
    await createMeeting({ id: record.id }, handleSuccess);
  }

  /**
   * 发起签到
   */
  async function handleRaiseCheackIn(record) {
    console.log('发起签到', record);
    await raiseCheackIn({ id: record.id }, handleSuccess);
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '发起会议',
        popConfirm: {
          title: '是否确认发起会议',
          confirm: handleCreateMeeting.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'dcs:training:edit',
      },
      {
        label: '发起签到',
        popConfirm: {
          title: '是否确认发起签到',
          confirm: handleRaiseCheackIn.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'dcs:training:edit',
      },
      {
        label: '生成培训记录',
        onClick: generateTrainingRecord.bind(null, record),
        auth: 'dcs:training:generateTrainingRecord',
      },
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
        auth: 'dcs:training:delete',
      },
    ];
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
