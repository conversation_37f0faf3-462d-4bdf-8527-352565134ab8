import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '测试ID',
    align:"center",
    dataIndex: 'testId'
   },
   {
    title: '过程参数名',
    align:"center",
    dataIndex: 'paraId_dictText'
   },
   {
    title: '参数值',
    align:"center",
    dataIndex: 'paraValue'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: "测试ID",
    field: 'testId',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: "参数",
    field: 'paraId',
    component: 'JSearchSelect',
    componentProps:{
      dict:"sys_method_testing_para,name,id"
    },
    //colProps: {span: 6},
  },
  {
    label: "参数值",
    field: 'paraValue',
    component: 'Input',
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '测试ID',
    field: 'testId',
    component: 'Input',
  },
  {
    label: '参数',
    field: 'paraId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"sys_method_testing_para,name,id"
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入参数!'},
          ];
     },
  },
  {
    label: '参数值',
    field: 'paraValue',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入参数值!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  testId: {title: '测试ID',order: 0,view: 'text', type: 'string',},
  paraId: {title: '参数',order: 1,view: 'sel_search', type: 'string',dictTable: "sys_method_testing_para", dictCode: 'id', dictText: 'name',},
  paraValue: {title: '参数值',order: 2,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
