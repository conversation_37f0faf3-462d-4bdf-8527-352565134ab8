import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
    sorter: true,
  },
  {
    title: '部门',
    align: 'center',
    dataIndex: 'deptId_dictText',
    sorter: true,
  },
  {
    title: '描述',
    align: 'center',
    dataIndex: 'description',
    sorter: true,
  },
  {
    title: '标准工期',
    align: 'center',
    dataIndex: 'leadTime',
    sorter: true,
  },
  {
    title: '标准收费',
    align: 'center',
    dataIndex: 'stdPrice',
    sorter: true,
  },
  {
    title: '报告模板',
    align: 'center',
    dataIndex: 'reportTemplate',
    sorter: true,
  },
  {
    title: '原始记录模板',
    align: 'center',
    dataIndex: 'testTemplate',
    sorter: true,
  },
  {
    title: '可用状态',
    align: 'center',
    dataIndex: 'isActive',
    customRender: ({ text }) => {
      return render.renderSwitch(text, [
        { text: '是', value: 'Y' },
        { text: '否', value: 'N' },
      ]);
    },
    sorter: true,
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
    sorter: true,
  },
  {
    title: '创建时间',
    align: 'center',
    dataIndex: 'createTime',
    sorter: true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'JInput',
  },
  {
    label: '创建时间',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '部门',
    field: 'deptId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_depart where is_lab = 1,depart_name,id',
    },
  },
  {
    label: '描述',
    field: 'description',
    component: 'Input',
  },
  {
    label: '标准工期',
    field: 'leadTime',
    component: 'Input',
  },
  {
    label: '标准收费',
    field: 'stdPrice',
    component: 'InputNumber',
  },
  {
    label: '报告模板',
    field: 'reportTemplate',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '原始记录模板',
    field: 'testTemplate',
    component: 'JUpload',
    componentProps: {},
  },
  {
    label: '可用状态',
    field: 'isActive',
    component: 'JSwitch',
    componentProps: {
      options: ['Y', 'N'],
    },
    defaultValue: 'Y',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '名称', order: 0, view: 'text', type: 'string' },
  description: { title: '描述', order: 1, view: 'text', type: 'string' },
  leadTime: { title: '标准工期', order: 2, view: 'text', type: 'string' },
  stdPrice: { title: '标准收费', order: 3, view: 'number', type: 'number' },
  isActive: { title: '可用状态', order: 4, view: 'switch', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
