<template>
  <a-list :data-source="progressList">
    <template #renderItem="{ item }">
      <a-list-item>
        <div style="width: 100%">
          <a-row>
            <a-col :span="3">
              <a-space direction="vertical" align="center">
                <span style="font-weight: bold; color: #e6c07b">{{ item.sampleNo }}</span>
                <span style="color: #0a8fe9">{{ item.method }}</span>
              </a-space>
            </a-col>
            <a-col :span="21"
              ><a-steps style="margin-top: 8px" :current="item.current" :status="item.status" :items="item.items">
                <template #progressDot="{ description, status, index, prefixCls }">
                  <a-popover>
                    <template #content>
                      <span> {{ description }}</span>
                    </template>
                    <span :class="`${prefixCls}-icon-dot`"></span>
                  </a-popover>
                </template> </a-steps
            ></a-col>
          </a-row>
        </div>
      </a-list-item>
    </template>
  </a-list>
</template>
<script lang="ts" setup>
  import { defineProps, onMounted, ref, watch } from 'vue';
  /**
   * props声明
   */
  const progressList = ref([])
  const props = defineProps({
    progress: {
      type: Array,
    },
  });
  watch(
    () => props.progress,
    (val) => {
      progressList.value = props.progress;
    }
  )
</script>
