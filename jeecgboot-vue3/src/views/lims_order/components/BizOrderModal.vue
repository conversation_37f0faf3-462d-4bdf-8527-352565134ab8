<template>
  <BasicDrawer
    :bodyStyle="{ backgroundColor: '#efeff2' }"
    v-bind="$attrs"
    @register="registerModal"
    destroyOnClose
    :title="title"
    width="100%"
    @ok="handleSubmit"
    showFooter
    :isDetail="true"
  >
    <template #titleToolbar>
      <a-space>
        <a-button size="small" v-auth="'lims_order:sample:add'" @click="generateContract">生成合同</a-button>
        <a-button size="small" v-auth="'lims_order:sample:add'" @click="handleApply" v-if="bizOrder.statusId != '1' && bizOrder.isLocked != 1"
          >发起审核</a-button
        >
        <a-button size="small" v-auth="'lims_order:sample:add'" @click="handleUnlock" v-if="bizOrder.isLocked == 1">解锁</a-button>
      </a-space>
    </template>
    <a-card :style="{ marginBottom: '10px' }" size="small" :bordered="false" v-if="isDetail">
      <a-descriptions size="small" :column="3" bordered>
        <a-descriptions-item label="客户信息">{{ bizOrder.customerId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="签订日期"> {{ bizOrder.signingDate }} </a-descriptions-item>
        <a-descriptions-item label="负责人"> {{ bizOrder.responsiblePerson_dictText }} </a-descriptions-item>
        <a-descriptions-item label="业务分类"> {{ bizOrder.bizTypeId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="状态"> {{ bizOrder.statusId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="合同编号"> {{ bizOrder.contractNo }} </a-descriptions-item>
      </a-descriptions>
    </a-card>
    <a-row>
      <a-col :span="4" :push="20">
        <div class="gutter-box">
          <Card :bordered="false">
            <a-tabs activeKey="record" animated>
              <a-tab-pane tab="操作记录" key="record" :forceRender="true">
                <Timeline>
                  <TimelineItem v-for="record in timeline" :key="record.id" color="green">
                    <div v-html="record.dataContent"></div>
                    操作人:{{ record.createBy }}
                    <p>操作时间:{{ record.createTime }}</p>
                  </TimelineItem>
                </Timeline>
              </a-tab-pane>
            </a-tabs>
          </Card>
        </div>
      </a-col>
      <a-col :span="20" :pull="4">
        <div class="gutter-box">
          <a-card :bordered="false">
            <a-tabs v-model:activeKey="activeKey" animated>
              <a-tab-pane tab="详细信息" key="detail" :forceRender="true">
                <BasicForm @register="registerForm" name="BizOrderForm" >
                  <template #leadTime="{ model, field }">
                    <a-row :gutter="8">
                      <a-col :span="12">
                        <a-input-number
                          v-model:value="model[field]"
                          :min="0"
                          :max="9999"
                          :style="{ width: '100%' }"
                          :placeholder="field"
                          :disabled="true"
                        />
                      </a-col>
                      <a-col :span="12">
                        <a-radio-group v-model:value="model['dayType']" :disabled="true">
                          <a-radio-button value="工作日">工作日</a-radio-button>
                          <a-radio-button value="自然日">自然日</a-radio-button>
                        </a-radio-group>
                      </a-col>
                    </a-row>
                  </template>
                </BasicForm>
              </a-tab-pane>
              <a-tab-pane tab="订单产品" key="sample" :forceRender="true">
                <SampleTable
                  :isLocked="bizOrder.isLocked == 1"
                  :isDetial="false"
                  :quotationId="bizOrder?.quotationId || '1'"
                  :bizTypeId="bizOrder?.bizTypeId"
                />
              </a-tab-pane>
              <a-tab-pane tab="报告信息" key="report" :forceRender="true">
                <ReportTable :isLocked="bizOrder.isLocked == 1" :isDetial="false" :orderId="bizOrder?.id || '1'" :bizTypeId="bizOrder?.bizTypeId" />
              </a-tab-pane>
              <a-tab-pane tab="进度查询" key="progress" :forceRender="true">
                <ProgressList :progress="progress" />
              </a-tab-pane>
              <a-tab-pane tab="回款计划" key="payment" :forceRender="true">
                <PaymentCollectionTable :order="bizOrder" />
              </a-tab-pane>
            </a-tabs>
          </a-card>
        </div>
      </a-col>
    </a-row>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../BizOrder.data';
  import { generateBizNo, saveOrUpdate, listDataLog, unlock, apply, transferToLab } from '../BizOrder.api';
  import { initDictOptions } from '@/utils/dict';
  import ReportTable from './ReportTable.vue';
  import { Card, Timeline, TimelineItem } from 'ant-design-vue';
  import ProgressList from './ProgressList.vue';
  import PaymentCollectionTable from '../payment_collection/PaymentCollectionTable.vue';
  import SampleTable from '@/views/lims_order/sample/SampleTable.vue';
  import { listProgress } from '@/views/lims_order/TestTaskFlow.api';
  // Emits声明
  const activeKey = ref('detail');
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const bizOrder = ref({});
  const timeline = ref([]);
  const progress = ref([]);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, scrollToField }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  //表单赋值
  const [registerModal, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    activeKey.value = 'detail';

    //重置表单
    await resetFields();
    setDrawerProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    if (unref(isUpdate) || data.record) {
      if (data.record && !isUpdate.value) {
        let result = await generateBizNo({ id: data.record.customerId });
        data.record.contractNo = result;
        data.record.standardPrice = data.record.pmPrice;
        data.record.quotationId = data.record.id;
        data.record.contractAmount = data.record.applyPrice;
        data.record.contractAmountRmb = data.record.applyPrice;
        data.record.plannedPaymentAmount = data.record.applyPrice;
        data.record.actualInvoicedAmount = data.record.applyPrice;
        data.record.responsiblePerson = data.record.responsiblePerson;
        data.record.createBy = null;
        data.record.createTime = null;
        data.record.id = null;
        data.record.isLocked = '0';
        const customer = await initDictOptions(`sys_customer where id = '${data.record.customerId}',name,id`);
        data.record.reportClientName = customer[0].title;
        const customerContactName = await initDictOptions(`sys_customer_contact where id = '${data.record.customerContactId}',name,id`);
        data.record.customerContactName = customerContactName[0].title;
        const customerContactEmail = await initDictOptions(`sys_customer_contact where id = '${data.record.customerContactId}',email,id`);
        data.record.customerContactEmail = customerContactEmail[0].title;
        const customerContactPhone = await initDictOptions(`sys_customer_contact where id = '${data.record.customerContactId}',phone,id`);
        data.record.customerContactPhone = customerContactPhone[0].title;
      }
      bizOrder.value = data.record;
      if (data.record.id) {
        listDataLog({ id: data.record.id }).then((res) => {
          timeline.value = res;
        });
      }

      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: bizOrder.value.isLocked == 1||!data?.showFooter });

    listProgress({ orderId: data.record.id }).then((res) => {
      progress.value = res;
    });
  });
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      setDrawerProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success');
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
  /**
   * 解锁
   */
  function handleUnlock() {
    unlock({ id: bizOrder.value.id }, handleUnlockSuccess);
  }
  /**
   * 解锁成功回调
   */
  function handleUnlockSuccess() {
    //bizOrder.value.isLocked = 0;
    //setProps({ disabled: bizOrder.value.isLocked == 1 });

    activeKey.value = 'detail';
  }

  async function generateContract() {
    alert('开发中');
  }

  /**
   * 发起审核
   */
  async function handleApply() {
    let values = await validate();
    setDrawerProps({ confirmLoading: true });
    saveOrUpdate(values, isUpdate.value)
      .then(() => {
        apply({ contractNo: values.contractNo }, handleApplySuccess).catch(()=>{
          activeKey.value = "payment";
        });
      })
      .finally(() => {
        setDrawerProps({ confirmLoading: false });
      });
  }

  /**
   * 发起审核成功回调..
   */
  function handleApplySuccess() {
    bizOrder.value.isLocked = 1;
    bizOrder.value.statusId = '1';
    setProps({ disabled: bizOrder.value.isLocked == 1 });
    closeDrawer();
    emit('success');
  }

  /**
   * 进单
   */
  function handleTransferToLab() {
    transferToLab({ id: bizOrder.value.id }, handleTransferToLabSuccess);
  }

  /**
   * 进单成功回调
   */
  function handleTransferToLabSuccess() {
    bizOrder.value.projectStatus = '1';
    closeDrawer();
    emit('success');
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
  .gutter-box {
    padding: 10px 6px;
  }
</style>
