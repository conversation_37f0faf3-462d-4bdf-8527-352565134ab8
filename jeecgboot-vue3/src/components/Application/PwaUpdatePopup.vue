<template>
  <div v-if="showReload" class="pwa-update-popup">
    <div class="pwa-update-content">
      <div class="pwa-update-title">
        <span>发现新版本</span>
        <CloseOutlined class="pwa-update-close" @click="close" />
      </div>
      <div class="pwa-update-message">
        <p>应用有新版本可用，点击刷新按钮更新。</p>
      </div>
      <div class="pwa-update-actions">
        <a-button type="primary" @click="reload">立即刷新</a-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref } from 'vue';
  import { CloseOutlined } from '@ant-design/icons-vue';
  import { registerSW } from 'virtual:pwa-register';

  const showReload = ref(false);
  const needRefresh = ref(false);
  const updateSW = registerSW({
    onNeedRefresh() {
      showReload.value = true;
      needRefresh.value = true;
    },
    onOfflineReady() {
      showReload.value = true;
      needRefresh.value = false;
    },
  });

  function close() {
    showReload.value = false;
  }

  function reload() {
    updateSW(true);
  }
</script>

<style lang="less" scoped>
  .pwa-update-popup {
    position: fixed;
    right: 20px;
    bottom: 20px;
    z-index: 9999;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    width: 300px;
    overflow: hidden;
    animation: slide-up 0.3s ease;
  }

  .pwa-update-content {
    padding: 16px;
  }

  .pwa-update-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-weight: 500;
    font-size: 16px;
  }

  .pwa-update-close {
    cursor: pointer;
    color: rgba(0, 0, 0, 0.45);
    transition: color 0.3s;

    &:hover {
      color: rgba(0, 0, 0, 0.75);
    }
  }

  .pwa-update-message {
    margin-bottom: 16px;
    color: rgba(0, 0, 0, 0.65);
  }

  .pwa-update-actions {
    display: flex;
    justify-content: flex-end;
  }

  @keyframes slide-up {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
</style>
