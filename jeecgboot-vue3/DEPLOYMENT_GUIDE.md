# 部署指南 - 解决 MIME 类型错误

## 问题描述

部署后出现以下错误：
```
Failed to load module script: Expected a JavaScript-or-Wasm module script but the server responded with a MIME type of "text/html". Strict MIME type checking is enforced for module scripts per HTML spec.
```

## 解决方案

### 方案一：使用自动修复脚本（推荐）

1. 构建并自动修复：
```bash
npm run build:deploy
```

这个命令会：
- 执行正常的构建过程
- 自动生成服务器配置文件
- 复制必要的配置到 dist 目录

### 方案二：手动配置服务器

#### Apache 服务器

1. 确保启用 `mod_rewrite` 模块
2. 将以下内容添加到 `.htaccess` 文件：

```apache
RewriteEngine On

# 设置正确的 MIME 类型
<IfModule mod_mime.c>
  AddType application/javascript .js
  AddType application/javascript .mjs
  AddType text/css .css
</IfModule>

# SPA 路由处理
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule ^.*$ /index.html [L]
```

#### Nginx 服务器

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/dist;
    index index.html;

    # 设置正确的 MIME 类型
    location ~* \.(js|mjs)$ {
        add_header Content-Type application/javascript;
    }

    # SPA 路由处理
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API 代理（根据需要调整）
    location /api/ {
        proxy_pass http://your-backend;
    }
}
```

#### IIS 服务器

在 `web.config` 文件中添加：

```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <system.webServer>
    <staticContent>
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <mimeMap fileExtension=".mjs" mimeType="application/javascript" />
    </staticContent>
    <rewrite>
      <rules>
        <rule name="SPA Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/index.html" />
        </rule>
      </rules>
    </rewrite>
  </system.webServer>
</configuration>
```

## 常见问题

### 1. 仍然出现错误

- 清除浏览器缓存
- 检查 Service Worker 缓存
- 确认服务器配置已生效

### 2. CDN 缓存问题

如果使用 CDN，需要：
- 清除 CDN 缓存
- 确保 CDN 正确转发 MIME 类型

### 3. 开发环境正常，生产环境异常

- 检查生产环境的服务器配置
- 确认构建产物的文件结构正确
- 验证静态资源路径配置

## 验证部署

1. 打开浏览器开发者工具
2. 查看 Network 面板
3. 确认 JS 文件的 Content-Type 为 `application/javascript`
4. 确认路由跳转正常工作

## 技术支持

如果问题仍然存在，请检查：
1. 服务器错误日志
2. 浏览器控制台错误
3. 网络请求的响应头信息
