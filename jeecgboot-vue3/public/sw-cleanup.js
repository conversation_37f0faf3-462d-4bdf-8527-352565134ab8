// 清理旧版Service Worker
if ('serviceWorker' in navigator) {
  navigator.serviceWorker.getRegistrations().then(function(registrations) {
    for(let registration of registrations) {
      console.log('正在注销Service Worker:', registration);
      registration.unregister();
    }
    console.log('所有Service Worker已注销');
    // 清除缓存
    if ('caches' in window) {
      caches.keys().then(function(cacheNames) {
        return Promise.all(
          cacheNames.map(function(cacheName) {
            console.log('正在删除缓存:', cacheName);
            return caches.delete(cacheName);
          })
        );
      }).then(function() {
        console.log('所有缓存已清除');
        // 可选：刷新页面以确保干净状态
        // window.location.reload();
      });
    }
  });
}