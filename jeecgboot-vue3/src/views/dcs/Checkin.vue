<template>
  <div class="checkin-container">
    <h1>培训签到</h1>
    <form @submit.prevent="handleSubmit">
      <div v-if="record" class="form-group">
        <label for="name">Name: {{ record.name }}</label>
      </div>
      <div v-if="record" class="form-group">
        <label for="startTime">Start Time: {{ record.startTime }}</label>
      </div>
      <div v-if="record" class="form-group">
        <label for="startTime">UserName: {{ userinfo.realname }}</label>
      </div>
      <button type="submit" @click="handleSubmit">Check In</button>
    </form>
    <div v-if="submitted" class="confirmation">
      <p>Thank you for checking in, {{ record.name }}!</p>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { useUserStore } from '/@/store/modules/user';
  import { list, checkin } from './Training.api';
  const userinfo = useUserStore().getUserInfo;
  const record = ref('');
  const submitted = ref(false);

  const handleSubmit = async () => {
    await checkin({ id: record.value.id }, handleSuccess);
  };
  /**
   * 成功回调
   */
  function handleSuccess() {
    submitted.value = true;
  }
  const route = useRoute();

  onMounted(async () => {
    const id = route.query.id;
    console.log('ID from URL:', id);
    await list(`?id=${id}&column=createTime&order=desc&pageNo=1&pageSize=10&`).then((res) => {
      console.log('Training List:', res.records);
      record.value = res.records[0];
    });
  });
</script>

<style scoped>
  .checkin-container {
    max-width: 400px;
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    background-color: #f9f9f9;
  }

  h1 {
    text-align: center;
    color: #333;
  }

  .form-group {
    margin-bottom: 20px;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
    color: #555;
  }

  input {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
  }

  button {
    width: 100%;
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease;
  }

  button:hover {
    background-color: #0056b3;
  }

  .confirmation {
    margin-top: 20px;
    padding: 15px;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 5px;
    color: #155724;
    text-align: center;
    font-size: 16px;
  }
</style>
