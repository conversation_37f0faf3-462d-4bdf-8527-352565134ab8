<template>
  <BasicModal v-bind="$attrs" @register="myregisterModal" destroyOnClose :title="title" :width="500" @ok="handleSubmit">
    <BasicForm @register="registerForm" name="TestTaskFlowForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from '../myTestTaskFlow.data';
  import { saveOrUpdate,submit } from '../TestTaskFlow.api';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  let batchs = ref();
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, scrollToField }] = useForm({
    labelWidth: 150,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 24 },
  });
  //表单赋值
  const [myregisterModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await resetFields();
    setModalProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    batchs.value = data?.batchs;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //设置标题
  const title = "测试任务流转";
  //表单提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      setModalProps({confirmLoading: true});
      const taskIds = batchs.value.split(',').map(id => id.trim()).filter(id => id);
      const baseParams = {
        column: 'createTime',
        flowStep: values.flowStep,
        order: 'desc',
        pageNo: '1',
        pageSize: '10',
      };
      for (const taskId of taskIds) {
        const params = {
          ...baseParams,
          taskId,
        };
        await submit(params);
      }
      emit('success');
      closeModal();
    } catch (error) {
      console.error('Submission failed:', error);
      setModalProps({ confirmLoading: false }); // Reset loading state
    }
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
