<template>
  <BasicModal
    ref="modalRef"
    destroyOnClose
    wrapClassName="j-cgform-tab-modal"
    v-bind="$attrs"
    @register="registerModal"
    :width="896"
    :maskClosable="false"
    @ok="handleSubmit"
  >
    <!-- 标题区域 -->
    <template #title>
      <div class="titleArea">
        <div class="title">{{ title }}</div>
        <div class="right">
          <a-radio-group v-model:value="activeKey">
            <template v-for="(item, index) in tabNav" :key="index">
              <a-radio-button :value="item.tableName">{{ item.tableTxt }}</a-radio-button>
            </template>
          </a-radio-group>
        </div>
      </div>
    </template>
    <!--表单区域 -->
    <div class="contentArea">
      <a-space style="margin: 5px">
        <a-button preIcon="ant-design:printer-outlined" type="primary" @click="handleTranslate" v-show="activeKey == refKeys[0]">处理翻译</a-button>
      </a-space>
      <!--主表区域 -->
      <BasicForm @register="registerForm" ref="formRef" v-show="activeKey == refKeys[0]" name="SysStandardForm" />
      <!--子表区域 -->
      <div class="table-container" v-show="activeKey == 'sysStandardEvaluationLimt'">
        <JVxeTable
          dragSort
          sortKey="sortNum"
          :sortBegin="1"
          dragSortFixed="none"
          keep-source
          resizable
          ref="sysStandardEvaluationLimt"
          :loading="sysStandardEvaluationLimtTable.loading"
          :columns="sysStandardEvaluationLimtTable.columns"
          :dataSource="sysStandardEvaluationLimtTable.dataSource"
          :disabled="formDisabled"
          :rowSelection="true"
          :toolbar="true"
          :menu-config="menuConfig"
          @menu-click="menuClickEvent"
          :linkageConfig="linkageConfig"
          :row-config="{ keyField: 'id' }"
          :tree-config="{
            transform: true,
            rowField: 'id',
            parentField: 'parentId',
          }"
          @blur="handleValueChange"
        >
          <template #toolbarSuffix>
            <a-button @click="handleOpen">+ 批量添加</a-button>
            <a-button @click="refhandleOpen">+ 添加引用</a-button>
            <a-button preIcon="ant-design:printer-outlined" type="primary" @click="handleTranslateJvxe">处理翻译</a-button>
          </template>
          <template #action="{ row }">
            <a-button v-if="row.sortNum != null" type="link" size="small" @click="addChildRow(row)"> 添加下级 </a-button>
          </template>
        </JVxeTable>
      </div>
    </div>
  </BasicModal>
  <JPopupOnlReportModal
    showAdvancedButton="true"
    @register="regModal"
    code="sys_analyte_selector_with_method"
    valueFiled="id"
    :multi="true"
    :groupId="''"
    :param="popupParam"
    :selectKeys="selectKeys"
    @ok="callBack"
  />
  <JPopupOnlReportModal
    showAdvancedButton="true"
    @register="regrefModal"
    code="sys_limt_selector_with_method"
    valueFiled="id"
    :multi="true"
    :groupId="''"
    @ok="refcallBack"
  />
  <TranslationModal @register="registerTranslationModal" />
  <TranslationJvxeModal @register="registerTranslationJvxeModal" />
</template>

<script lang="ts" setup>
  import { buildUUID } from '/@/utils/uuid';
  import { ref, computed, unref, reactive, onMounted, onUnmounted } from 'vue';
  import { BasicModal, useModalInner, useModal } from '/@/components/Modal';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { JVxeTable } from '/@/components/jeecg/JVxeTable';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods.ts';
  import { formSchema, sysStandardEvaluationLimtColumns } from '../SysStandard.data';
  import { saveOrUpdate, sysStandardEvaluationLimtList, fetch } from '../SysStandard.api';
  import { JVxeLinkageConfig } from '@/components/jeecg/JVxeTable/src/types';
  import { initDictOptions } from '@/utils/dict';
  import JPopupOnlReportModal from '@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';
  import { VxeTableEvents, VxeTablePropTypes } from 'vxe-table';
  import { message } from 'ant-design-vue';
  import { useSuperSubscript } from '@/hooks/setting';
  import TranslationModal from '@/views/lims_core/components/TranslationModal.vue';
  import TranslationJvxeModal from '@/views/lims_core/components/TranslationJvxeModal.vue';

  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const formDisabled = ref(false);
  const modalRef = ref();
  const refKeys = ref(['sysStandard', 'sysStandardEvaluationLimt']);
  const tabNav = ref<any>([
    { tableName: 'sysStandard', tableTxt: '标准' },
    { tableName: 'sysStandardEvaluationLimt', tableTxt: '标准指标评定要求' },
  ]);
  const activeKey = ref('sysStandard');
  const sysStandardEvaluationLimt = ref();
  const tableRefs = { sysStandardEvaluationLimt };
  const sysStandardEvaluationLimtTable = reactive({
    loading: false,
    dataSource: [],
    columns: sysStandardEvaluationLimtColumns,
  });
  const popupParam = ref({});
  const linkageConfig = ref<JVxeLinkageConfig[]>([{ requestData: requestMethods, key: 'analyteId' }]);
  async function requestMethods(parent) {
    console.log('parent', parent);
    let result = await fetch({ id: parent });
    console.log('result', result);
    return result;
  }
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    //重置表单
    await reset();
    setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    formDisabled.value = !data?.showFooter;
    popupParam.value.standardname = data.record.name;
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
      // 请求子表数据并确保树结构正确加载
      requestSubTableData(sysStandardEvaluationLimtList, { id: data?.record?.id }, sysStandardEvaluationLimtTable, fetchEvaluationLimtSuccess);
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: !data?.showFooter });
  });
  //方法配置
  const [handleSubmit, requestSubTableData, formRef] = useJvxeMethod(requestAddOrEdit, classifyIntoFormData, tableRefs, activeKey, refKeys);
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));
  //重置
  async function reset() {
    await resetFields();
    activeKey.value = 'sysStandard';
    sysStandardEvaluationLimtTable.dataSource = [];
  }
  function classifyIntoFormData(allValues) {
    let main = Object.assign({}, allValues.formValue);

    // 先同步数据确保最新状态
    syncTableDataBeforeSubmit();

    // 直接使用dataSource作为提交数据源，确保包含子节点
    return {
      ...main, // 展开
      sysStandardEvaluationLimtList: sysStandardEvaluationLimtTable.dataSource,
    };
  }

  // 在提交前确保数据源是最新的
  function syncTableDataBeforeSubmit() {
    try {
      const $table = sysStandardEvaluationLimt.value?.getXTable();
      if ($table) {
        // 获取表格中的全部数据（包括新添加的行）
        const fullData = $table.getTableData().fullData;
        // 确保数据源包含所有行
        sysStandardEvaluationLimtTable.dataSource = [...fullData];
      }
    } catch (error) {
      console.error('同步数据失败:', error);
    }
  }

  const menuConfig = reactive<VxeTablePropTypes.MenuConfig>({
    body: {
      options: [[{ code: 'btnCopydown', name: '向下复制' }]],
    },
  });

  const menuClickEvent: VxeTableEvents.MenuClick<any> = ({ menu, type, row, rowIndex, column, columnIndex, $event }) => {
    switch (menu.code) {
      case 'btnCopydown':
        const field = column.field;
        if (!field) {
          message.warning('无法确定要复制的列');
          return;
        }
        const currentValue = row[field];
        const $table = sysStandardEvaluationLimt.value.getXTable();
        if (!$table) {
          message.error('无法获取表格实例');
          return;
        }
        const selectedRows = $table.getCheckboxRecords();
        if (!selectedRows || selectedRows.length === 0) {
          message.warning('请先选择要复制的行');
          return;
        }
        const { fullData: allTableData } = $table.getTableData();
        const currentRowIndex = allTableData.findIndex((item) => item.id === row.id);
        if (currentRowIndex === -1) {
          message.warning('无法确定当前行位置');
          return;
        }
        const rowsToUpdate = selectedRows.filter((item) => {
          const itemIndex = allTableData.findIndex((tableRow) => item.id === tableRow.id);
          return itemIndex > currentRowIndex;
        });
        console.log('rowsToUpdate', rowsToUpdate);
        if (rowsToUpdate.length === 0) {
          message.warning('没有可更新的行，请选择当前行之后的行');
          return;
        }
        rowsToUpdate.forEach((item) => {
          item[field] = currentValue;
        });
        message.success(`已将 ${column.title || field} 列的值向下复制到 ${rowsToUpdate.length} 行`);
        break;
    }
  };

  const fetchEvaluationLimtSuccess = async (res) => {
    console.log('fetchEvaluationLimtSuccess', res);
    // await initDictOptions(res.result[0].paraType).then((res) => {
    //   sysStandardEvaluationLimtTable.columns[3].options = res;
    // });
    // 修改这里，使用 nextTick 替代 setTimeout
    import('vue').then(({ nextTick }) => {
      nextTick(() => {
        const $table = sysStandardEvaluationLimt.value?.getXTable();
        if ($table) {
          // 重新加载数据，确保树结构正确
          $table.loadData(sysStandardEvaluationLimtTable.dataSource);
          // 展开所有父节点
          $table.setAllTreeExpand(true);
        }
      });
    });
  };

  //表单提交事件
  async function requestAddOrEdit(values) {
    try {
      // 先同步数据确保最新状态
      //syncTableDataBeforeSubmit();
      console.log('values', values);
      setModalProps({ confirmLoading: true });

      //遍历values,如果存在children,将children的对象加到数组里(平铺数据)
      const flatChildren = [];
      values.sysStandardEvaluationLimtList.forEach((item) => {
        if (item.children && item.children.length > 0) {
          flatChildren.push(...item.children);
        }
      });
      //将平铺后的数据添加到sysStandardEvaluationLimtList
      values.sysStandardEvaluationLimtList.push(...flatChildren);

      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  // 添加子行
  async function addChildRow(currRow) {
    console.log('currRow', currRow);
    const $table = sysStandardEvaluationLimt.value.getXTable();
    const record = {
      id: buildUUID(),
      analyteId: currRow.analyteId,
      methodIds: currRow.methodIds,
      elimit: currRow.elimit,
      unitId: currRow.unitId,
      parentId: currRow.id,
    };
    const { row: newRow } = await $table.insertAt(record, -1);
    await $table.setTreeExpand(currRow, true);
    await $table.setEditRow(newRow); // 插入子节点
    sysStandardEvaluationLimtTable.dataSource.push(newRow);
  }

  // const handleParaTypeChange = async (data) => {
  //   if (data.col.key == 'paraType') {
  //     await initDictOptions(data.value).then((res) => {
  //       data.target.columns[3].options = res;
  //       data.row.paraValue = null;
  //     });
  //   }
  // };

  // 添加监听窗口变化的代码
  onMounted(() => {
    // 监听窗口大小变化
    window.addEventListener('resize', handleWindowResize);
    if (modalRef.value) {
      // 监听模态窗口状态变化
      const modalDom = modalRef.value.$el;
      if (modalDom) {
        // 使用 MutationObserver 监听DOM变化
        const observer = new MutationObserver(() => {
          // 模态窗口状态发生变化，刷新表格
          handleTableRefresh();
        });

        // 开始监听
        observer.observe(modalDom, {
          attributes: true,
          attributeFilter: ['class', 'style'],
        });
      }
    }
  });

  // 在组件卸载时移除事件监听器
  onUnmounted(() => {
    // 清理事件监听器
    window.removeEventListener('resize', handleWindowResize);
  });

  // 处理窗口大小变化
  function handleWindowResize() {
    // 延迟执行以避免频繁触发
    if (activeKey.value === 'sysStandardEvaluationLimt') {
      handleTableRefresh();
    }
  }

  // 刷新表格
  function handleTableRefresh() {
    const $table = sysStandardEvaluationLimt.value?.getXTable();
    if ($table) {
      // 强制刷新表格布局
      $table.refreshColumn();
      $table.recalculate(true);
    }
  }

  const [regModal, { openModal: openAnalyteSelectModal }] = useModal();
  const [regrefModal, { openModal: openLimtSelectModal }] = useModal();
  const selectKeys = ref('');
  function handleOpen() {
    selectKeys.value = '';
    console.log(sysStandardEvaluationLimt.value.getTableData());
    sysStandardEvaluationLimt.value.getTableData().forEach((row) => {
      selectKeys.value += row.analyteId + ',';
    });
    // 如果最后一个字符是逗号就去掉
    if (selectKeys.value.charAt(selectKeys.value.length - 1) === ',') {
      selectKeys.value = selectKeys.value.substring(0, selectKeys.value.length - 1);
    }
    console.log(selectKeys.value);
    openAnalyteSelectModal(true);
  }
  function refhandleOpen() {
    openLimtSelectModal(true);
  }
  function refcallBack(data) {
    console.log(data);
    let tableData = sysStandardEvaluationLimt.value.getTableData();
    //增
    data.forEach((row) => {
      tableData = sysStandardEvaluationLimt.value.getTableData();
      const index = tableData.findIndex((item) => item.analyteId === row.id);
      if (index === -1) {
        sysStandardEvaluationLimt.value.addRows(
          [{ analyteId: row.id, methodIds: row.methodid, refId: row.refid, id: buildUUID(), elimit: row.elimit }],
          {
            setActive: false,
          }
        );
      }
    });
  }

  function callBack(data) {
    console.log(data);
    let tableData = sysStandardEvaluationLimt.value.getTableData();
    //增
    data.forEach((row) => {
      tableData = sysStandardEvaluationLimt.value.getTableData();
      const index = tableData.findIndex((item) => item.analyteId === row.id);
      if (index === -1) {
        sysStandardEvaluationLimt.value.addRows([{ analyteId: row.id, methodIds: row.methodid, id: buildUUID(), elimit: '/' }], {
          setActive: false,
        });
      }
    });
    //删
    // tableData.forEach((row, _) => {
    //   //removeRowsById
    //   const index = data.findIndex((item) => item.id === row.analyteId);
    //   console.log(index);
    //   if (index === -1) {
    //     console.log(row);
    //     sysStandardEvaluationLimt.value.removeRows(row);
    //   }
    // });
  }
  const unitOptions = ref([]);
  async function loadUnitOptions() {
    const res = await initDictOptions('sys_unit,unit_name,id');
    unitOptions.value = res;
  }
  const handleValueChange = async (data) => {
    if (data.col.key === 'elimit') {
      const elimitValue = data.value || '';
      const row = data.row;
      const $table = sysStandardEvaluationLimt.value?.getXTable();
      const rangeMatch = elimitValue.match(/^\d*\.?\d+～\d*\.?\d+%$/);
      if (rangeMatch) {
        row.unitId = '';
        row.elimit = elimitValue;
        const fullData = $table.getTableData().fullData;
        const rowIndex = fullData.findIndex((item) => item.id === row.id);
        if (rowIndex !== -1) {
          fullData[rowIndex].unitId = '';
          fullData[rowIndex].elimit = elimitValue;
          sysStandardEvaluationLimtTable.dataSource = [...fullData];
        }
      } else {
        const numericUnitMatch = elimitValue.match(/(-?\d*\.?\d+)([a-zA-Z%µ\/]+)$/);
        if (numericUnitMatch) {
          const extractedUnit = numericUnitMatch[2];
          const unit = unitOptions.value.find((option) => option.label === extractedUnit);
          if (unit) {
            let cleanedElimit = elimitValue.replace(new RegExp(`${extractedUnit}$`), '').trim();
            cleanedElimit = cleanedElimit.replace(new RegExp(extractedUnit, 'g'), '').trim();
            row.unitId = unit.value;
            row.elimit = cleanedElimit;
            const fullData = $table.getTableData().fullData;
            const rowIndex = fullData.findIndex((item) => item.id === row.id);
            if (rowIndex !== -1) {
              fullData[rowIndex].unitId = unit.value;
              fullData[rowIndex].elimit = cleanedElimit;
              sysStandardEvaluationLimtTable.dataSource = [...fullData];
            }
          } else {
            row.unitId = '';
            row.elimit = elimitValue;
            const fullData = $table.getTableData().fullData;
            const rowIndex = fullData.findIndex((item) => item.id === row.id);
            if (rowIndex !== -1) {
              fullData[rowIndex].unitId = '';
              fullData[rowIndex].elimit = elimitValue;
              sysStandardEvaluationLimtTable.dataSource = [...fullData];
            }
          }
        }
      }
    }
    if (data.col.key === 'paraType') {
      await initDictOptions(data.value).then((res) => {
        data.target.columns[3].options = res;
        data.row.paraValue = null;
      });
    }
  };
  onMounted(async () => {
    await loadUnitOptions();
    window.addEventListener('resize', handleWindowResize);
  });

  const [registerTranslationModal, { openModal: openTranslationModal }] = useModal();

  const handleTranslate = async () => {
    let values = await validate();
    try {
      await saveOrUpdate(values, isUpdate.value);
      openTranslationModal(true, {
        baseTable: 'sys_standard',
        baseId: values.id,
        recordvalue: { ...values },
        formSchema: formSchema,
      });
    } catch (e) {
      message.error(e);
    }
  };

  const [registerTranslationJvxeModal, { openModal: openTranslationJvxeModal }] = useModal();
  const handleTranslateJvxe = async () => {
    let values = await validate();
    try {
      await saveOrUpdate(values, isUpdate.value);
      openTranslationJvxeModal(true, {
        baseTable: 'sys_standard_evaluation_limt',
        dataSource: sysStandardEvaluationLimtTable.dataSource,
        formSchema: sysStandardEvaluationLimtTable.columns,
      });
    } catch (e) {
      message.error(e);
    }
  };
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }

  .table-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .titleArea {
    display: flex;
    align-content: center;
    padding-right: 70px;
    .title {
      margin-right: 16px;
      line-height: 32px;
    }
    .right {
      overflow-x: auto;
      overflow-y: hidden;
      flex: 1;
      white-space: nowrap;
      .ant-radio-group {
        font-weight: normal;
      }
    }
  }

  html[data-theme='light'] {
    .right {
      .ant-radio-group {
        :deep(.ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked)) {
          color: #555;
        }
      }
    }
  }
</style>

<style lang="less">
  // Online表单Tab风格专属样式
  .j-cgform-tab-modal {
    .contentArea {
      //padding: 20px 1.5% 0;
    }

    //.ant-modal-header {
    //  padding-top: 8px;
    //  padding-bottom: 8px;
    //  border-bottom: none !important;
    //}

    .ant-modal .ant-modal-body > .scrollbar,
    .ant-tabs-nav .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-tabs-top-bar {
      width: calc(100% - 55px);
      position: relative;
      left: -14px;
    }

    .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
      overflow: hidden auto;
    }
  }
</style>
