import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '研发项目编号',
    align: 'center',
    dataIndex: 'rdNo',
  },
  {
    title: '研发项目名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '订单',
    align: 'center',
    dataIndex: 'orderId',
  },
  {
    title: '报价',
    align: 'center',
    dataIndex: 'quotationId',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '研发项目编号',
    field: 'rdNo',
    component: 'Input',
  },
  {
    label: '研发项目名称',
    field: 'name',
    component: 'Input',
  },
  {
    label: '订单',
    field: 'orderId',
    component: 'Input',
  },
  {
    label: '报价',
    field: 'quotationId',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  rdNo: { title: '研发项目编号', order: 0, view: 'text', type: 'string' },
  orderId: { title: '订单', order: 1, view: 'text', type: 'string' },
  quotationId: { title: '报价', order: 2, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
