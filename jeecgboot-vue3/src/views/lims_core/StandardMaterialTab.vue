<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #expandedRowRender="{ record }">
        <a-tabs tabPosition="top" v-model:activeKey="record.activeTabKey" animated>
          <a-tab-pane tab="出入库记录" key="TestResultTable" :forceRender="true">
            <warehouseInOutSubTable :articleNo="record.code" :key="record.id" />`
          </a-tab-pane>
        </a-tabs>
      </template>
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" preIcon="ant-design:check-outlined" @click="handleOutInventory">入库</a-button>
        <a-button type="primary" preIcon="ant-design:user-delete-outlined" @click="handleOutApply">发起出库申请</a-button>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :dropDownActions="getDropDownAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <StandardMaterialModal @register="registerModal" @success="handleSuccess" />
    <InventoryModal @register="registerInventoryModal" @success="handleSuccess" />
    <WarehouseOutApplyModal @register="registerApplyModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="lims_core-standardMaterial" setup>
import {ref, reactive, computed, unref, defineProps} from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useModal } from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage';
  import StandardMaterialModal from './components/StandardMaterialModal.vue';
  import { columns, superQuerySchema } from './StandardMaterialTab.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './StandardMaterial.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  import { hiprint } from 'vue-plugin-hiprint';
  import barcode from '@/templates/StandardMaterialbarcode';
  import InventoryModal from '@/views/warehouse/components/InventoryModal.vue';
  import WarehouseOutApplyModal from '@/views/warehouse/components/WarehouseOutApplyModal.vue';
  import warehouseInOutSubTable from "@/views/warehouse/Subtable/warehouseInOutSubTable.vue";
  import {message, Modal} from 'ant-design-vue';
  import {inventoryQtyAndUnit} from "@/views/lims_order/sample/Sample.api";
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册model
  const [registerApplyModal, { openModal: ApplyopenModal }] = useModal();

  const [registerInventoryModal, { openModal: InventoryregModal }] = useModal();
  const props = defineProps({
    sampleId: {
      type: [String, Number],
    },
  });
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '标准物质台账',
      api: list,
      columns,
      canResize: false,
      clickToRowSelect: true,
      formConfig: {
        //labelWidth: 120,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [
          ['purchaseDate', ['purchaseDate_begin', 'purchaseDate_end'], 'YYYY-MM-DD'],
          ['createTime', ['createTime_begin', 'createTime_end'], 'YYYY-MM-DD HH:mm:ss'],
        ],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      scroll: {
        y: 'calc(100vh - 380px)',
      },
      beforeFetch: (params) => {
        queryParam.sampleId = props.sampleId;
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '标准物质台账',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'lims_core:standard_material:edit',
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  /**
   * 打印
   */
  function print() {
    if (selectedRows.value.length == 0) {
      alert('请选择要打印的数据');
      return;
    }
    let hiprintTemplate = new hiprint.PrintTemplate({ template: barcode });
    let printDatas = [];
    selectedRows.value.forEach((item) => {
      console.log(item);

      let printData = {
        barcode: item.code,
        name: item.name,
        code: item.code,
        lotNo: item.lotNo,
        spec: item.spec,
        createBy: item.createBy,
        purity: item.purity,
        purchaseDate: item.purchaseDate,
        createTime: item.createTime,
        vaildDate: item.vaildDate,
        storageCondition: item.storageCondition,
      };
      printDatas.push(printData);
    });
    // 打印
    hiprintTemplate.print(printDatas);
  }
  async function handleOutApply() {
    if (selectedRows.value.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    const articleNos = [];
    let isValid = true;
    selectedRows.value.forEach((item, index) => {
      const selectedRow = item;
      articleNos.push(selectedRow.code);
    });
    if (!isValid) {
      return;
    }
    //查询库存和单位
    const list = await inventoryQtyAndUnit({ articleNo: articleNos.join(',') });
    const record = {
      articleNo: articleNos.join(','),
      articleTypeId: 'BP',
      amount: list.amount,
    };
    ApplyopenModal(true, {
      record,
      isUpdate: false,
      showFooter: true,
      isInitializationPart: true,
    });
  }
  function handleOutInventory() {
    if (selectedRows.value.length === 0) {
      message.error('请至少选择一条数据');
      return;
    }
    const articleNos = [];
    const receiveCounts = [];
    const receiveCountUnits = [];
    let isValid = true;
    selectedRows.value.forEach((item, index) => {
      const selectedRow = item;
      if (selectedRow.spec == null) {
        message.error('请先填写数量');
        isValid = false;
        return;
      }
      if (selectedRow.specUnit == null) {
        message.error('请先填写单位');
        isValid = false;
        return;
      }
      articleNos.push(selectedRow.code);
      receiveCounts.push(Number(selectedRow.spec));
      receiveCountUnits.push(selectedRow.specUnit);
    });
    if (!isValid) {
      return;
    }
    const record = {
      articleNo: articleNos.join(','),
      articleTypeId: 'BP',
      amount:receiveCounts.join(','),
      unitId:receiveCountUnits.join(','),
    };
    InventoryregModal(true, {
      record,
      isUpdate: false,
      showFooter: true,
      isInitializationPart: true,
    });
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
  :deep(.ant-table-wrapper) {
    overflow: auto; // 确保表格容器允许滚动
  }
</style>
