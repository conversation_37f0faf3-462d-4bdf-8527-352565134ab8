<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mail Sender</title>
    <link rel="stylesheet" href="resources/css/styles.css">
    <script type="text/javascript" src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins.js"></script>
    <script type="text/javascript" src="https://onlyoffice.github.io/sdkjs-plugins/v1/plugins-ui.js"></script>
    <script src="https://unpkg.com/@wangeditor/editor@latest/dist/index.js" referrerpolicy="origin"></script>
    <link href="https://unpkg.com/@wangeditor/editor@latest/dist/css/style.css" rel="stylesheet">
    <script src="resources/js/app.js"></script>
    <style>
        /* 添加页面滚动样式 */
        body, html {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: auto;
        }
        #mail-sender-container {
            max-height: 100vh;
            overflow-y: auto;
            padding: 16px;
        }
        /* 调整表单布局，使label和input在同一行 */
        .form-group {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .form-group label {
            width: 80px;
            flex-shrink: 0;
            margin-bottom: 0;
            margin-right: 10px;
        }
        .form-group input, 
        .form-group textarea {
            flex: 1;
        }
        /* SMTP设置部分的特殊处理 */
        .smtp-settings {
            width: 100%;
        }
        /* 附件容器的特殊处理 */
        .attachment-container {
            width: 100%;
            border: none;
            box-shadow: none;
            background: transparent;
            padding: 0;
            margin: 0;
        }
        
        /* 添加附件信息区域样式 */
        .attachment-info {
            border: none;
            box-shadow: none;
            background: transparent;
            padding: 0;
            margin: 0;
        }
    </style>
</head>
<body>
    <div id="mail-sender-container">
        <div class="form-container">
            <div class="form-group">
                <label for="to">收件人:</label>
                <input type="text" id="to" placeholder="请输入收件人邮箱，多个邮箱用分号(;)分隔">
            </div>
            
            <div class="form-group">
                <label for="cc">抄送:</label>
                <input type="text" id="cc" placeholder="请输入抄送邮箱，多个邮箱用分号(;)分隔">
            </div>
            
            <div class="form-group">
                <label for="bcc">密送:</label>
                <input type="text" id="bcc" placeholder="请输入密送邮箱，多个邮箱用分号(;)分隔">
            </div>
            
            <div class="form-group">
                <label for="subject">主题:</label>
                <input type="text" id="subject" placeholder="请输入邮件主题">
            </div>
            
            <div class="form-group" style="align-items: flex-start;">
                <label>附件:</label>
                <div class="attachment-container">
                    <div class="attachment-info">
                        <span id="current-document-name">当前文档</span>
                    </div>
                </div>
            </div>

            <div class="form-group" style="align-items: flex-start;">
                <label for="message">正文:</label>
                <!-- TinyMCE 将会替换这个 textarea -->
                <textarea id="message-editor"></textarea> 
            </div>
            
            <!-- <div class="form-group" style="align-items: flex-start;">
                <label for="smtp-server">SMTP:</label>
                <div class="smtp-settings">
                    <div class="setting-row">
                        <input type="text" id="smtp-server" placeholder="SMTP服务器地址">
                        <input type="text" id="smtp-port" placeholder="端口" value="25">
                    </div>
                    <div class="setting-row">
                        <input type="text" id="smtp-username" placeholder="用户名">
                        <input type="password" id="smtp-password" placeholder="密码">
                    </div>
                    <div class="setting-row">
                        <label>
                            <input type="checkbox" id="smtp-ssl"> 使用SSL
                        </label>
                        <button id="save-smtp-settings" class="secondary-button">保存设置</button>
                    </div>
                </div>
            </div> -->
            
            <!-- <div class="form-actions">
                <button id="send-mail" class="primary-button">发送邮件</button>
                <button id="cancel" class="secondary-button">取消</button>
            </div> -->
        </div>
        
        <!-- 加载遮罩 -->
        <div id="loading-overlay" style="display: none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(255, 255, 255, 0.8); z-index: 1000; justify-content: center; align-items: center; flex-direction: column;">
            <div class="spinner"></div>
            <p>正在发送邮件...</p>
        </div>
    </div>
</body>
</html>