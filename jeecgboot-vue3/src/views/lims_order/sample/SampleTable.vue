<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="isLocked ? null : rowSelection" @edit-end="handleEditEnd" ref="tableRef">
      <!--插槽:table标题-->
      <template #tableTitle v-if="!isLocked">
        <a-button type="primary" v-auth="'lims_order:sample:add'" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" v-auth="'lims_order:sample:add'" @click="handleTestTask" preIcon="ant-design:plus-outlined"> 测试项目管理</a-button>
        <a-button type="primary" v-auth="'lims_order:sample:add'" @click="handleCopySample" preIcon="ant-design:plus-outlined"> 复制检品</a-button>
        <a-button type="primary" v-auth="'lims_order:sample:add'" @click="handleCopyOldSample" preIcon="ant-design:plus-outlined">
          从旧产品复制</a-button
        >
        <a-button
          v-if="selectedRowKeys.length > 0 && selectedRows[0].testControlStatus == '正常'"
          type="warning"
          v-auth="'lims_order:sample:add'"
          @click="handleCancelSample"
          preIcon="ant-design:plus-outlined"
        >
          取消样品</a-button
        >
        <a-button
          v-if="selectedRowKeys.length > 0 && selectedRows[0].testControlStatus == '取消'"
          type="primary"
          v-auth="'lims_order:sample:add'"
          @click="handleResumSample"
          preIcon="ant-design:plus-outlined"
        >
          恢复样品</a-button
        >
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button v-auth="'lims_order:sample:deleteBatch'"
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }" v-if="!isLocked">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <SampleTableModal @register="registerModal" @success="handleSuccess" />
    <ProductSelector @register="regProductSelector" @success="handleProductSelectorSuccess" />
    <JPopupOnlReportModal @register="regPopupModal" :param="popupParam" code="sys_method_by_cid" @ok="callBack" />
    <JPopupOnlReportModal @register="regCapabilityModal" code="sys_capability_selector" :multi="true" @ok="callBackCapability" />
    <a-modal v-model:open="open" title="请选择添加方式" width="300px">
      <a-card hoverable :style="{ textAlign: 'center' }" @click="handleAddByProduct">按营销产品添加</a-card>
      <a-card hoverable :style="{ marginTop: '10px', textAlign: 'center' }" @click="handleAddByCapability">按检测能力添加</a-card>
    </a-modal>
    <SampleModal @register="registerSampleModal" @success="handleSuccess" />
    <TestTaskTableModal @register="regTestTaskTableModal" @success="handleSuccess" />
    <JPopupOnlReportModal
      :showAdvancedButton="true"
      @register="regCopyOldSampleModal"
      code="copy_old_sample"
      :multi="true"
      @ok="callbackCopyOldSampleModal"
    />
  </div>
</template>

<script lang="tsx" setup>
  import { ref, reactive, computed, unref, watch } from 'vue';
  import { BasicTable, useTable, TableAction } from '/src/components/Table';
  import { useModal } from '/src/components/Modal';
  import { useListPage } from '/src/hooks/system/useListPage';
  import SampleTableModal from './SampleTableModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './SampleTable.data';
  import {
    list,
    deleteOne,
    batchDelete,
    getImportUrl,
    getExportUrl,
    addProduct,
    listVo,
    editLevel,
    addCapability,
    cancelSample,
    resumSample,
    copyOldSample,
  } from './SampleTable.api';
  import { downloadFile } from '/src/utils/common/renderUtils';
  import { useUserStore } from '/src/store/modules/user';
  import { defineProps, defineEmits } from 'vue';
  import ProductSelector from '@/views/lims_order/product_selector/index.vue';
  import { useDrawer } from '@/components/Drawer';
  import JPopupOnlReportModal from '@/components/Form/src/jeecg/components/modal/JPopupOnlReportModal.vue';
  import { addTest } from '@/views/lims_order/details/Test.api';
  import { changeMethod } from '@/views/lims_order/TestTask.api';
  import SampleModal from '@/views/lims_order/sample/components/SampleModal.vue';
  import TestTaskTableModal from '@/views/lims_order/TestTaskTableModal.vue';
  import { message, Modal } from 'ant-design-vue';
  import { copySample } from '@/views/lims_order/sample/Sample.api';

  columns.forEach((column) => {
    console.log('column', column);
    if (column.dataIndex === 'sampleNo') {
      column.customRender = ({ text, record }) => {
        return (
          <a-button
            type="link"
            onClick={() => {
              console.log('sampleNo', text);
              console.log('record', record);
              openEdit(record);
            }}
          >
            {text}
          </a-button>
        );
      };
    }
  });
  const openEdit = (record) => {
    if (record.id) {
      openDrawer(true, {
        record,
        isUpdate: true,
        hideFooter: false,
      });
    }
  };
  /**
   * props声明
   */
  const props = defineProps({
    isLocked: {
      type: Boolean,
      required: false,
    },
    quotationId: {
      type: [String, Number],
    },
    opportunityId: {
      type: [String, Number],
    },
    bizTypeId: {
      type: String,
    },
    customerId: {
      type: [String, Number],
    },
    customerContactId: {
      type: [String, Number],
    },
  });
  const open = ref<boolean>(false);

  const showModal = () => {
    open.value = true;
  };
  const hideModal = () => {
    open.value = false;
  };
  // Define emits
  const emit = defineEmits(['sampleModalCallback']);

  //表格实例
  const tableRef = ref();
  const popupParam = ref({});
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const expandedSampleId = ref(null);
  //注册model
  const [regPopupModal, { openModal: openPopupModal }] = useModal();
  const [registerModal, { openModal }] = useModal();
  const [regProductSelector, { openModal: openProductSelector }] = useModal();
  const [regCapabilityModal, { openModal: openCapabilitySelector }] = useModal();
  const [regCopyOldSampleModal, { openModal: openCopyOldSampleModal }] = useModal();
  const [registerSampleModal, { openDrawer }] = useDrawer();
  const [regTestTaskTableModal, { openModal: openTestTaskTableModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      tableSetting: {
        cacheKey: 'sample_table',
      },
      title: '药品信息',
      isTreeTable: true,
      api: listVo,
      rowKey: 'id',
      size: 'small',
      columns,
      canResize: true,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.opportunityId = props.opportunityId;
        queryParam.quotationId = props.quotationId;
        queryParam.pid = '0' + '';
        queryParam.column = 'sampleNo';
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '样品',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload, setLoading }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  /**
   * 新增事件
   */
  function handleAdd() {
    console.log('bizTypeId', props.bizTypeId);
    console.log('customerId', props.customerId);
    console.log('customerContactId', props.customerContactId);
    openModal(true, {
      opportunityId: props.opportunityId,
      quotationId: props.quotationId,
      bizTypeId: props.bizTypeId,
      customerContactId: props.customerContactId,
      customerId: props.customerId,
      isUpdate: false,
      showFooter: true,
    });
  }
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    record.initial_productIds = record.productIds;
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 添加测试
   */
  const curSample = ref();
  function handleAddItem(record: Recordable) {
    curSample.value = record.id;
    showModal();
  }

  const currentEditRow = ref(null);
  /**
   * 指定方法
   */
  function handleChangeMethod(record: Recordable) {
    console.log('12123123213123', record);
    popupParam.value.cid = record.capabilityId;
    currentEditRow.value = record;
    openPopupModal(true);
  }
  const callBack = (rows) => {
    console.log('callBack', rows);
    console.log('currentEditRow', currentEditRow.value);
    let record = {
      id: currentEditRow.value.id,
      methodId: rows[0].id,
    };
    setLoading(true);
    changeMethod(record)
      .then((res) => {
        handleSuccess();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id, level: record.level }, handleSuccess);
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    // await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
    selectedRowKeys.value.forEach(async (id) => {
      await deleteOne({ id: id, level: 0 }, handleSuccess);
    });
    calcPrice();
  }
  /**
   * 成功回调
   */
  async function handleSuccess() {
    console.log(121232132);
    selectedRowKeys.value = [];
    await reload();
    calcPrice();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    if (record.level == 0) {
      return [
        // {
        //   label: '添加',
        //   onClick: handleAddItem.bind(null, record),
        //   auth: 'lims_order:sample:edit',
        // },
        {
          label: '编辑',
          onClick: openEdit.bind(null, record),
          auth: 'lims_order:sample:edit',
        },
        {
          label: '删除',
          popConfirm: {
            title: '是否确认删除',
            confirm: handleDelete.bind(null, record),
            placement: 'topLeft',
          },
          auth: 'lims_order:sample:delete',
        },
      ];
    } else if (record.level == 1) {
      return [
        {
          label: '指定方法',
          onClick: handleChangeMethod.bind(null, record),
          auth: 'lims_order:sample:edit',
        },
        {
          label: '删除',
          popConfirm: {
            title: '是否确认删除',
            confirm: handleDelete.bind(null, record),
            placement: 'topLeft',
          },
          auth: 'lims_order:sample:delete',
        },
      ];
    } else {
      return [];
    }
  }
  function handleEditEnd({ record, index, key, value }: Recordable) {
    editLevel({ id: record.id, level: record.level, key, value }).then(() => {
      handleSuccess();
    });
    return false;
  }

  function calcPrice() {
    let filter = tableRef.value.getDataSource().filter((record) => record.level == 0);
    console.log('///////////', filter);
    let pmPrice = filter.reduce((total, item) => total + item.pmPrice, 0);
    let applyPrice = filter.reduce((total, item) => total + item.applyPrice, 0);
    let standardPrice = filter.reduce((total, item) => total + item.standardPrice, 0);
    //拿到最大leadtime
    let leadTime = Math.max(...filter.map((item) => item.pmLeadTime));
    console.log('calcPrice', pmPrice, applyPrice);
    emit('sampleModalCallback', { pmPrice, applyPrice, leadTime, standardPrice });
  }

  function getData() {
    return tableRef.value.getDataSource();
  }

  //监听props.quotationId
  watch(
    () => props.quotationId,
    (value) => {
      queryParam.quotationId = value;
      reload();
    }
  );
  watch(
    () => props.bizTypeId,
    (value) => {
      console.log('bizTypeId', value);
    }
  );

  defineExpose({
    getData: getData,
  });

  const handleProductSelectorSuccess = (data) => {
    console.log('handleProductSelectorSuccess', data);
    setLoading(true);
    addProduct(data, curSample.value)
      .then((res) => {
        handleSuccess();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const callBackCapability = (data) => {
    console.log('callBackCapability', data);
    data = data.map((item) => {
      return item.id;
    });
    console.log(data);
    setLoading(true);
    addCapability(data, curSample.value)
      .then((res) => {
        handleSuccess();
      })
      .finally(() => {
        setLoading(false);
      });
  };
  function handleAddByProduct() {
    console.log('按营销产品添加');
    hideModal();
    openProductSelector(true);
  }

  function handleAddByCapability() {
    console.log('按检测能力添加');
    hideModal();
    openCapabilitySelector(true);
  }

  function handleTestTask() {
    if (selectedRowKeys.value.length == 0) {
      message.error('请至少选择一条记录');
      return;
    }
    openTestTaskTableModal(true, {
      sampleId: selectedRowKeys.value,
      sampleNo: selectedRows.value[0].sampleNo,
      disabled: selectedRows.value[0].orderId ? true : false,
    });
  }

  function handleCancelSample() {
    if (selectedRowKeys.value.length == 0) {
      message.error('请至少选择一条记录');
      return;
    }
    if (selectedRows.value.some((item) => item.testControlStatus == '取消').length > 0) {
      message.error('选中的样品中存在取消样品');
      return;
    }
    Modal.confirm({
      title: '确认取消样品',
      content: '是否确认取消选中的样品？',
      onOk() {
        cancelSample({ ids: selectedRowKeys.value.join(',') }).then(() => {
          handleSuccess();
        });
      },
    });
  }

  function handleResumSample() {
    if (selectedRowKeys.value.length == 0) {
      message.error('请至少选择一条记录');
      return;
    }
    if (selectedRows.value.some((item) => item.testControlStatus == '正常').length > 0) {
      message.error('选中的样品中存在正常样品');
      return;
    }
    Modal.confirm({
      title: '确认恢复样品',
      content: '是否确认恢复选中的样品？',
      onOk() {
        resumSample({ ids: selectedRowKeys.value.join(',') }).then(() => {
          handleSuccess();
        });
      },
    });
  }
  async function handleCopySample() {
    if (selectedRowKeys.value.length == 0 || selectedRowKeys.value.length > 1) {
      message.error('请选择一条记录');
      return;
    }
    Modal.confirm({
      title: '确认复制样品',
      content: '是否确认复制选中的样品？',
      onOk() {
        setLoading(true);
        copySample({ id: selectedRows.value[0].id }, handleSuccess).finally(() => {
          setLoading(false);
        });
      },
    });
  }
  function handleCopyOldSample() {
    openCopyOldSampleModal(true);
  }
  function callbackCopyOldSampleModal(value) {
    if (!props.quotationId) {
      message.error('报价单未选择,请退出当前页面重新进入');
    }
    if (value.length == 0) {
      message.error('请至少选择一条记录');
      return;
    }
    console.log('callbackCopyOldSampleModal', value);
    let sampleIds = value.map((item) => item.id).join(',');
    setLoading(true);
    copyOldSample({ quotationId: props.quotationId, sampleIds: sampleIds })
      .then((res) => {
        handleSuccess();
      })
      .catch((err) => {
        console.error(err);
      }).finally(() => {
        setLoading(false);
      });
  }
</script>

<style lang="less" scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }

  .custom-list {
    text-align: center;
  }

  .custom-list-item {
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .custom-list-item:hover {
    background-color: #f0f0f0;
  }
</style>
