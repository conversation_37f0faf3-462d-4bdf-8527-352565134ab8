import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '标准',
    align: 'center',
    dataIndex: 'standardName',
    width: 150,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '名称',
    align: 'center',
    dataIndex: 'name',
    width: 120,
    resizable: true,
    fixed: 'left',
  },
  {
    title: '标准价格',
    align: 'center',
    dataIndex: 'stdPrice',
  },
  {
    title: '平行数量',
    align: 'center',
    dataIndex: 'duplicateQty',
  },
  {
    title: '空白数量',
    align: 'center',
    dataIndex: 'blkQty',
  },
  {
    title: '加标数量',
    align: 'center',
    dataIndex: 'stdQty',
  },
  {
    title: '检测能力',
    align: 'center',
    dataIndex: 'cid_dictText',
  },
  {
    title: '实验步骤',
    align: 'center',
    dataIndex: 'labSteps',
  },
  {
    title: '记录模板',
    align: 'center',
    dataIndex: 'templateId_dictText',
  },
  {
    title: '状态',
    align: 'center',
    dataIndex: 'effectiveStatus_dictText',
  },
  {
    title: '作废日期',
    align: 'center',
    dataIndex: 'abandonedDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '适用性说明',
    align: 'center',
    dataIndex: 'usedScope',
  },
  {
    title: '创建人',
    align: 'center',
    dataIndex: 'createBy_dictText',
  },
  {
    title: '创建日期',
    align: 'center',
    dataIndex: 'createTime',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '标准',
    field: 'standardId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_standard,name,id',
    },
    //colProps: {span: 6},
  },
  {
    label: '名称',
    field: 'name',
    component: 'JInput', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '标准',
    field: 'standardId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_standard,name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入标准!' }];
    },
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入名称!' }];
    },
    needTran: true,
  },
  {
    label: '标准价格',
    field: 'stdPrice',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入标准价格!' }];
    },
  },
  {
    label: '平行数量',
    field: 'duplicateQty',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入平行数量!' }];
    },
  },
  {
    label: '空白数量',
    field: 'blkQty',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入空白数量!' }];
    },
  },
  {
    label: '加标数量',
    field: 'stdQty',
    defaultValue: 0,
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入加标数量!' }];
    },
  },
  {
    label: '检测能力',
    field: 'cid',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'sys_capability,name,id',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入检测能力!' }];
    },
  },
  {
    label: '实验步骤',
    field: 'labSteps',
    component:'InputTextArea',// 'JMarkdownEditor', //注意string转换问题
  },
  {
    label: '记录模板',
    field: 'templateId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_template,name,id',
    },
  },
  {
    label: '状态',
    field: 'effectiveStatus',
    defaultValue: '1',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'standard_status',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入状态!' }];
    },
  },
  {
    label: '作废日期',
    field: 'abandonedDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '适用性说明',
    field: 'usedScope',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表表格配置
export const sysMethodConsumptiveColumns: JVxeColumn[] = [
  {
    title: '耗材类型',
    key: 'consumptiveTypeId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_consumptive_type,name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '单次用量',
    key: 'amount',
    type: JVxeTypes.inputNumber,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '计量单位',
    key: 'unitId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_unit,unit_name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '?需要配制',
    key: 'needMakeup',
    type: JVxeTypes.checkbox,
    customValue: [1, 0],
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '配制溶液类型',
    key: 'solutionTypeId',
    type: JVxeTypes.selectSearch,
    dictCode: 'solution_type',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '配制要求',
    key: 'makeupReq',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];
export const sysMethodStdMaterialColumns: JVxeColumn[] = [
  {
    title: '标品类型',
    key: 'stdMaterilaTypeId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_standard_material_type,name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '?需要配制',
    key: 'needMakeup',
    type: JVxeTypes.checkbox,
    customValue: [1, 0],
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '配制溶液类型',
    key: 'solutionTypeId',
    type: JVxeTypes.selectSearch,
    dictCode: 'solution_type',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '配制要求',
    key: 'makeupReq',
    type: JVxeTypes.textarea,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];
export const sysMethodInstrumentTypeColumns: JVxeColumn[] = [
  {
    title: '仪器类型',
    key: 'instrumentTypeId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_instrument_type,abbr_name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '要求',
    key: 'req',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];
export const sysMethodTestingParaColumns: JVxeColumn[] = [
  {
    title: '名称',
    key: 'name',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '类别',
    key: 'typeId',
    type: JVxeTypes.selectSearch,
    dictCode: 'method_testing_para_type',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '要求',
    key: 'req',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '计量单位',
    key: 'unitId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_unit,unit_name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
];
export const sysMethodRepeatTypeColumns: JVxeColumn[] = [
  {
    title: '排序',
    key: 'sortNum',
    type: JVxeTypes.input,
    width: '100px',
    placeholder: '请输入${title}',
    defaultValue: '',
    disabled: true,
  },
  {
    title: '重复类型',
    key: 'repeatType',
    type: JVxeTypes.selectSearch,
    dictCode: 'method_repeat_type',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '重复名称',
    key: 'repeatName',
    type: JVxeTypes.selectMultiple,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '重复周期',
    key: 'cycleName',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '条件要求',
    key: 'conditionReq',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];
export const sysMethodWorkflowColumns: JVxeColumn[] = [
  {
    title: '检测流程',
    key: 'workflowId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_workflow,name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '分包流程',
    key: 'subcontractedWorkflowId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_workflow,name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '岗位',
    key: 'deptId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_depart where is_lab = 1,depart_name,id',
    props: {},
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
];
export const sysMethodAnalyteColumns: JVxeColumn[] = [
  {
    title: '检测指标',
    key: 'analyteId',
    type: JVxeTypes.selectSearch,
    dictCode: '	sys_analyte,name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '结果类型',
    key: 'resultType',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'test_result_type',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '1',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '检测单位',
    key: 'unitId',
    type: JVxeTypes.selectSearch,
    dictCode: 'sys_unit,unit_name,id',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '计算公式',
    key: 'calcExpr',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '检出限',
    key: 'lod',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
  {
    title: '定量限',
    key: 'loq',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
  },
];

// 高级查询数据
export const superQuerySchema = {
  standardId: { title: '标准', order: 0, view: 'sel_search', type: 'string', dictTable: 'sys_standard', dictCode: 'id', dictText: 'name' },
  name: { title: '名称', order: 1, view: 'text', type: 'string' },
  duplicateQty: { title: '平行数量', order: 2, view: 'number', type: 'number' },
  blkQty: { title: '空白数量', order: 3, view: 'number', type: 'number' },
  stdQty: { title: '加标数量', order: 4, view: 'number', type: 'number' },
  productId: { title: '产品', order: 5, view: 'sel_search', type: 'string', dictTable: 'sys_product', dictCode: 'id', dictText: 'name' },
  labSteps: { title: '实验步骤', order: 6, view: 'markdown', type: 'string' },
  templateId: { title: '记录模板', order: 7, view: 'sel_search', type: 'string', dictTable: 'sys_template', dictCode: 'id', dictText: 'name' },
  effectiveStatus: { title: '状态', order: 8, view: 'list', type: 'string', dictCode: 'standard_status' },
  abandonedDate: { title: '作废日期', order: 9, view: 'date', type: 'string' },
  createBy: { title: '创建人', order: 10, view: 'text', type: 'string' },
  createTime: { title: '创建日期', order: 11, view: 'datetime', type: 'string' },
  //子表高级查询
  sysMethodConsumptive: {
    title: '试剂耗材',
    view: 'table',
    fields: {
      consumptiveTypeId: {
        title: '耗材类型',
        order: 0,
        view: 'sel_search',
        type: 'string',
        dictTable: 'sys_consumptive_type',
        dictCode: 'id',
        dictText: 'name',
      },
      amount: { title: '单次用量', order: 1, view: 'number', type: 'number' },
      unitId: { title: '计量单位', order: 2, view: 'sel_search', type: 'string', dictTable: 'sys_unit', dictCode: 'id', dictText: 'unit_name' },
      needMakeup: { title: '?需要配制', order: 3, view: 'number', type: 'number' },
      solutionTypeId: { title: '配制溶液类型', order: 4, view: 'sel_search', type: 'string', dictCode: 'solution_type' },
      makeupReq: { title: '配制要求', order: 5, view: 'text', type: 'string' },
      createBy: { title: '创建人', order: 6, view: 'text', type: 'string' },
      createTime: { title: '创建日期', order: 7, view: 'datetime', type: 'string' },
      updateBy: { title: '更新人', order: 8, view: 'text', type: 'string' },
      updateTime: { title: '更新日期', order: 9, view: 'datetime', type: 'string' },
    },
  },
  sysMethodStdMaterial: {
    title: '标品',
    view: 'table',
    fields: {
      stdMaterilaTypeId: {
        title: '标品类型',
        order: 0,
        view: 'sel_search',
        type: 'string',
        dictTable: 'sys_standard_material_type',
        dictCode: 'id',
        dictText: 'name',
      },
      needMakeup: { title: '?需要配制', order: 1, view: 'number', type: 'number' },
      solutionTypeId: { title: '配制溶液类型', order: 2, view: 'sel_search', type: 'string', dictCode: 'solution_type' },
      makeupReq: { title: '配制要求', order: 3, view: 'textarea', type: 'string' },
    },
  },
  sysMethodInstrumentType: {
    title: '设备类别',
    view: 'table',
    fields: {
      instrumentTypeId: {
        title: '仪器类型',
        order: 0,
        view: 'sel_search',
        type: 'string',
        dictTable: 'sys_instrument_type',
        dictCode: 'id',
        dictText: 'abbr_name',
      },
      req: { title: '要求', order: 1, view: 'text', type: 'string' },
    },
  },
  sysMethodTestingPara: {
    title: '实验过程参数',
    view: 'table',
    fields: {
      name: { title: '名称', order: 0, view: 'text', type: 'string' },
      typeId: { title: '类别', order: 1, view: 'sel_search', type: 'string', dictCode: 'method_testing_para_type' },
      req: { title: '要求', order: 2, view: 'text', type: 'string' },
      unitId: { title: '计量单位', order: 3, view: 'sel_search', type: 'string', dictTable: 'sys_unit', dictCode: 'id', dictText: 'unit_name' },
      createBy: { title: '创建人', order: 4, view: 'text', type: 'string' },
      createTime: { title: '创建日期', order: 5, view: 'datetime', type: 'string' },
      updateBy: { title: '更新人', order: 6, view: 'text', type: 'string' },
      updateTime: { title: '更新日期', order: 7, view: 'datetime', type: 'string' },
    },
  },
  sysMethodRepeatType: {
    title: '重复性',
    view: 'table',
    fields: {
      repeatType: { title: '重复类型', order: 0, view: 'sel_search', type: 'string', dictCode: 'method_repeat_type' },
      repeatName: { title: '重复名称', order: 1, view: 'text', type: 'string' },
      cycleName: { title: '重复周期', order: 2, view: 'text', type: 'string' },
      conditionReq: { title: '条件要求', order: 3, view: 'text', type: 'string' },
    },
  },
  sysMethodWorkflow: {
    title: '检测流程',
    view: 'table',
    fields: {
      workflowId: { title: '检测流程', order: 0, view: 'sel_search', type: 'string', dictTable: 'sys_workflow', dictCode: 'id', dictText: 'name' },
      subcontractedWorkflowId: {
        title: '分包流程',
        order: 1,
        view: 'sel_search',
        type: 'string',
        dictTable: 'sys_workflow',
        dictCode: 'id',
        dictText: 'name',
      },
      deptId: { title: '岗位', order: 2, view: 'sel_depart', type: 'string' },
    },
  },
  sysMethodAnalyte: {
    title: '检测指标',
    view: 'table',
    fields: {
      analyteId: { title: '检测指标', order: 0, view: 'sel_search', type: 'string', dictTable: '	sys_analyte', dictCode: 'id', dictText: 'name' },
      unitId: { title: '检测单位', order: 1, view: 'sel_search', type: 'string', dictTable: 'sys_unit', dictCode: 'id', dictText: 'unit_name' },
      calcExpr: { title: '计算公式', order: 2, view: 'text', type: 'string' },
      lod: { title: '检出限', order: 3, view: 'text', type: 'string' },
      loq: { title: '定量限', order: 4, view: 'text', type: 'string' },
    },
  },
};
