import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [

  {
    title: '样品编号',
    align: 'center',
    dataIndex: 'sampleId_dictText',
  },
  {
    title: '营销产品',
    align: 'center',
    dataIndex: 'productId_dictText',
  },
  {
    title: '方法名称',
    align: 'center',
    dataIndex: 'methodId_dictText',
  },
  {
    title: '重复名称',
    align: 'center',
    dataIndex: 'repeatName',
  },
  {
    title: '重复周期',
    align: 'center',
    dataIndex: 'repeatCycle',
  },
  {
    title: '原价',
    align: 'center',
    dataIndex: 'originalPrice',
  },
  {
    title: '折扣',
    align: 'center',
    dataIndex: 'discount',
  },
  {
    title: '最终价格',
    align: 'center',
    dataIndex: 'finalPrice',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '样品ID',
    field: 'sampleId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sample,sample_no,id',
    },
    dynamicDisabled: true,
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入样品ID!' }];
    },
  },
  {
    label: '营销产品ID',
    field: 'productId',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'sys_product,name,id',
    },
    dynamicDisabled: true,
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入营销产品ID!' }];
    },
  },
  {
    label: '方法ID',
    field: 'methodId',
    component: 'JSearchSelect',
    componentProps: ({ schema, formModel }) => {
      console.log('form:', schema);
      console.log('formModel:', formModel);
      return {
        dict: `sys_method where product_id = ${formModel.productId ? formModel.productId : '""'} ,name,id`,
      };
    },
  },
  {
    label: '试样类型',
    field: 'testTypeId',
    defaultValue: '0',
    component: 'JSearchSelect',
    componentProps: {
      dict: 'test_type',
    },
    dynamicDisabled: true,
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入试样类型!' }];
    },
  },
  {
    label: '原价',
    field: 'originalPrice',
    component: 'InputNumber',
    dynamicDisabled: true,
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入原价!' }];
    },
  },
  {
    label: '折扣',
    field: 'discount',
    component: 'InputNumber',
    componentProps: ({ schema, formModel }) => {
      console.log('form:', schema);
      console.log('formModel:', formModel);
      return {
        min: 0,
        max: 1,
        step: 0.01,
        onChange: (e: any) => {
          formModel.finalPrice = formModel.originalPrice * e;
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入折扣!' }];
    },
  },
  {
    label: '最终价格',
    field: 'finalPrice',
    component: 'InputNumber',
    componentProps: ({ schema, formModel }) => {
      console.log('form:', schema);
      console.log('formModel:', formModel);
      return {
        step: 0.01,
        onChange: (e: any) => {
          formModel.discount = e / formModel.originalPrice;
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入最终价格!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  sampleId: { title: '样品ID', order: 0, view: 'text', type: 'string' },
  productId: { title: '营销产品ID', order: 1, view: 'text', type: 'string' },
  methodId: { title: '方法ID', order: 2, view: 'text', type: 'string' },
  testTypeId: { title: '试样类型', order: 3, view: 'text', type: 'string' },
  trialNo: { title: '试样序号', order: 4, view: 'number', type: 'number' },
  retestNo: { title: '复测序号', order: 5, view: 'number', type: 'number' },
  repeatNo: { title: '重复序号', order: 6, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
