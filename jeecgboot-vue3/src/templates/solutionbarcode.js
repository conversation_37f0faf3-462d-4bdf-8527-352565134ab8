export default {
  panels: [
    {
      index: 0,
      name: 1,
      height: 40,
      width: 60,
      paperHeader: 49.5,
      paperFooter: 105,
      printElements: [
        {
          options: {
            left: 0,
            top: 0,
            height: 102,
            width: 168,
            right: 168,
            bottom: 90,
            vCenter: 84,
            hCenter: 45,
          },
          printElementType: {
            title: '矩形',
            type: 'rect',
          },
        },
        {
          options: {
            left: 0,
            top: 30,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 42,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 54,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 66,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 78,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 0,
            top: 90,
            height: 9,
            width: 167,
            borderWidth: 0.75,
          },
          printElementType: {
            title: '横线',
            type: 'hline',
          },
        },
        {
          options: {
            left: 32,
            top: 30,
            height: 72,
            width: 9,
          },
          printElementType: {
            title: '竖线',
            type: 'vline',
          },
        },
        {
          options: {
            left: 80,
            top: 10,
            height: 9.75,
            width: 167,
            title: '溶液标签',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 6,
            top: 31,
            height: 9.75,
            width: 33,
            title: '名称/浓度',
            right: 124.5,
            bottom: 21.75,
            vCenter: 64.5,
            hCenter: 16.875,
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 31,
            width: 120,
            testData: '名称/浓度',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'conc',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 6,
            top: 42,
            height: 9.75,
            width: 33,
            title: '编号',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 42,
            width: 120,
            testData: '编号',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'code',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 55,
            height: 9.75,
            width: 33,
            title: '配制日期',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 55,
            width: 120,
            testData: '配制日期',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'createTime',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 67,
            height: 9.75,
            width: 33,
            title: '有效期至',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 67,
            width: 120,
            testData: '有效期至',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'effectiveTo',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 79,
            height: 9.75,
            width: 33,
            title: '配置人',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 33,
            top: 79,
            width: 120,
            testData: '配置人',
            fontSize: 5,
            right: 148.5,
            bottom: 40.5,
            vCenter: 88.5,
            hCenter: 25.5,
            field: 'createBy',
            coordinateSync: false,
            widthHeightSync: false,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            fontSize: 5,
            left: 6,
            top: 92,
            height: 9.75,
            width: 33,
            title: '保存条件',
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 55,
            top: 95,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 60,
            top: 92,
            height: 9.75,
            width: 120,
            title: '常温',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 80,
            top: 95,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 85,
            top: 92,
            height: 9.75,
            width: 120,
            title: '阴凉',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 100,
            top: 95,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 105,
            top: 92,
            height: 9.75,
            width: 120,
            title: '冷藏',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
        {
          options: {
            left: 120,
            top: 95,
            height: 4,
            width: 4,
          },
          printElementType: {
            title: '勾选框',
            type: 'rect',
          },
        },
        {
          options: {
            left: 125,
            top: 92,
            height: 9.75,
            width: 120,
            title: '避光',
            fontSize: 5,
          },
          printElementType: {
            type: 'text',
          },
        },
      ],
      paperNumberContinue: false,
      paperNumberLeft: -10,
      paperNumberTop: -10,
      watermarkOptions: {},
      panelLayoutOptions: {},
    },
  ],
};
