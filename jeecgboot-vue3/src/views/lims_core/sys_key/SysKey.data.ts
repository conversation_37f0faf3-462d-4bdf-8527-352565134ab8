import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '前缀',
    align:"center",
    dataIndex: 'prefix'
   },
   {
    title: '中间序号',
    align:"center",
    dataIndex: 'midfix'
   },
   {
    title: '当前序号',
    align:"center",
    dataIndex: 'sn'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '前缀',
    field: 'prefix',
    component: 'Input',
  },
  {
    label: '中间序号',
    field: 'midfix',
    component: 'Input',
  },
  {
    label: '当前序号',
    field: 'sn',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  prefix: {title: '前缀',order: 0,view: 'text', type: 'string',},
  midfix: {title: '中间序号',order: 1,view: 'text', type: 'string',},
  sn: {title: '当前序号',order: 2,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}