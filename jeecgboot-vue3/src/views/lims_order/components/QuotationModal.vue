<template>
  <BasicDrawer
    :bodyStyle="{ backgroundColor: '#efeff2' }"
    v-bind="$attrs"
    @register="registerModal"
    destroyOnClose
    :title="title"
    width="90%"
    @ok="handleSubmit"
    showFooter
    :isDetail="true"
    :maskClosable="false"
  >
    <template #titleToolbar>
      <a-space>
        <!--        <a-button size="small" v-auth="'lims_order:sample:add'" @click="handleApply" v-if="quotation.statusId != '1' && quotation.isLocked != 1"-->
        <!--          >发起审核</a-button-->
        <!--        >-->
        <a-button size="small" v-auth="'lims_order:sample:add'" @click="generateQuotation">生成报价单</a-button>
        <a-button size="small" v-auth="'lims_order:sample:add'" @click="handleUnlock" v-if="quotation.isLocked == 1">解锁</a-button>
        <a-button size="small" v-auth="'lims_order:biz_order:add'" @click="handleAddContract">转合同</a-button>
      </a-space>
    </template>
    <Card :style="{ marginBottom: '10px' }" size="small" :bordered="false" v-if="quotation.isLocked == 1">
      <a-descriptions size="small" :column="3" bordered>
        <a-descriptions-item label="客户信息"> {{ quotation.customerId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="业务分类"> {{ quotation.bizTypeId_dictText }} </a-descriptions-item>
        <a-descriptions-item label="状态"> {{ quotation.statusId_dictText }} </a-descriptions-item>
      </a-descriptions>
    </Card>
    <a-row>
      <a-col :span="4" :push="20">
        <div class="gutter-box">
          <Card :bordered="false">
            <a-tabs activeKey="record" animated>
              <a-tab-pane tab="操作记录" key="record" :forceRender="true">
                <Timeline>
                  <TimelineItem v-for="record in timeline" :key="record.id" color="green">
                    <div v-html="record.dataContent"></div>
                    操作人:{{ record.createBy }}
                    <p>操作时间:{{ record.createTime }}</p>
                  </TimelineItem>
                </Timeline>
              </a-tab-pane>
            </a-tabs>
          </Card>
        </div>
      </a-col>
      <a-col :span="20" :pull="4">
        <div class="gutter-box">
          <Card :bordered="false">
            <a-tabs v-model:activeKey="activeKey" @change="onTabChange" animated>
              <a-tab-pane tab="报价信息" key="quotation" :forceRender="true">
                <BasicForm @register="registerForm" name="QuotationForm" >
                  <template #leadTime="{ model, field }">
                    <a-row :gutter="8">
                      <a-col :span="12">
                        <a-input-number
                          v-model:value="model[field]"
                          :min="0"
                          :max="9999"
                          :style="{ width: '100%' }"
                          :placeholder="field"
                          :disabled="quotation.isLocked == 1"
                        />
                      </a-col>
                      <a-col :span="12">
                        <a-radio-group v-model:value="model['dayType']" :disabled="quotation.isLocked == 1">
                          <a-radio-button value="工作日">工作日</a-radio-button>
                          <a-radio-button value="自然日">自然日</a-radio-button>
                        </a-radio-group>
                      </a-col>
                    </a-row>
                  </template>
                </BasicForm>
                <!-- 子表单区域 --> </a-tab-pane
              >T
              <a-tab-pane tab="产品信息" key="sample1" :forceRender="true" v-if="quotation?.isLocked == 0">
                <!--引用表格-->
                <SampleTable
                  :isLocked="quotation?.isLocked == 1"
                  :quotationId="quotation?.id"
                  :opportunityId="quotation?.opportunityId"
                  @sample-modal-callback="handleSampleModalCallback"
                  ref="sampleTableRef"
                  :bizTypeId="bizTypeId"
                  :customerId="customerId"
                  :customerContactId="customerContactId"
                />
              </a-tab-pane>
            </a-tabs>
          </Card>
        </div>
      </a-col>
    </a-row>
    <DocEditor @register="registerModalDocEditor" :floatButtons="[{ tooltip: '发送邮件', icon: 'MailOutlined', action: 'email' }]" />
    <BizOrderModal @register="regOrderModal" @success="handleOrderSuccess" />
    <!-- Loading Modal -->
    <a-modal v-model:visible="loadingModalVisible" :footer="null" :closable="false" :maskClosable="false" centered wrapClassName="loading-modal">
      <div style="text-align: center; padding: 20px">
        <a-spin size="large" />
        <p style="margin-top: 10px">生成报价中...</p>
      </div>
    </a-modal>
  </BasicDrawer>
</template>

<script lang="tsx" setup>
  import { Card, message, Timeline, TimelineItem } from 'ant-design-vue';
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicDrawer, useDrawerInner, useDrawer } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import SampleTable from '../sample/SampleTable.vue';

  import { formSchema } from '../Quotation.data';
  import { apply, unlock, listDataLog, saveVo, getOOEditorConfig, geneOneQuotion, saveOrUpdate, handOverToReceive, preCheck } from '../Quotation.api';
  import { useUserStore } from '@/store/modules/user';
  import { initDictOptions } from '@/utils/dict';
  import BizOrderModal from '@/views/lims_order/components/BizOrderModal.vue';
  import { useGo } from '/@/hooks/web/usePage';
  import { useLocaleStore } from '/@/store/modules/locale';
  import { useModal } from '@/components/Modal';
  import DocEditor from '@/views/dcs/components/DocEditor.vue';
  const go = useGo();
  const [regOrderModal, { openDrawer: openOrderDrawer }] = useDrawer();

  const [regProductSelector, { openDrawer: openProductSelector }] = useDrawer();
  const localeStore = useLocaleStore();
  const sampleTableRef = ref();
  const activeKey = ref('quotation');
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  const quotationNo = ref('');
  const quotation = ref({});
  const timeline = ref({});
  const bizTypeId = ref('');
  const customerId = ref('');
  const customerContactId = ref('');

  const [registerModalDocEditor, { openModal: openDocEditorModal }] = useModal();
  // Loading modal state
  const loadingModalVisible = ref(false);

  const onTabChange = async (key) => {
    console.log(key);
    if (key == 'sample1') {
      try {
        let values = await validate();
        console.log('values', values);
        if(values.dayType == null) {
          message.error("请选择工期类型!!工作日/自然日!!")
          throw new Error("请选择工期类型!!工作日/自然日!!");
        }
        //sample.value 跟 values 对比,values中的值跟sample.value中的值不一致时,sample的值可能多一些
        let needUpdate = false;
        for (let k in values) {
          if (values[k] != quotation.value[k]) {
            needUpdate = true;
            break;
          }
        }
        if (needUpdate) {
          await saveOrUpdate(values, isUpdate.value);
          for (let k in values) {
            if (values[k] != quotation.value[k]) {
              quotation.value[k] = values[k];
            }
          }
        }
      } catch (e) {
        activeKey.value = 'quotation';
        message.error('请完善必填内容再切换!!!');
      }
    }
  };

  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, getFieldsValue, validate, scrollToField, updateSchema }] = useForm({
    labelWidth: 120,
    schemas: formSchema,
    showActionButtonGroup: false,
    baseColProps: { span: 12 },
  });
  //表单赋值
  const [registerModal, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
    activeKey.value = 'quotation';
    //重置表单
    await resetFields();
    //设置订单号
    console.log('data?.record', data?.record);
    quotationNo.value = data?.record?.quotationNo;
    quotation.value = { isLocked: 0, id: 'New', ...data?.record };
    bizTypeId.value = data?.record?.bizTypeId;
    customerId.value = data?.record?.customerId;
    customerContactId.value = data?.record?.customerContactId;
    timeline.value = {};
    getTimeLine();
    console.log('quotation', quotation.value);
    setDrawerProps({ confirmLoading: false, showCancelBtn: !!data?.showFooter, showOkBtn: !!data?.showFooter });
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    //---- 从商机过来 ----
    if (data.record && !isUpdate.value) {
      console.log(111111112222222);
      console.log(data.samples);
      //data.samples sum the pmPrice
      let pmPrice = 0;
      let applyPrice = 0;
      let leadTime = 0;
      let standardPrice = 0;
      data.samples.forEach((sample) => {
        pmPrice += sample.pmPrice;
        applyPrice += sample.applyPrice;
        standardPrice += sample.standardPrice;
        if (sample.standardLeadTime > leadTime) {
          leadTime = sample.standardLeadTime;
        }
      });
      data.record.pmPrice = pmPrice;
      data.record.applyPrice = applyPrice;
      data.record.leadTime = leadTime;
      data.record.standardPrice = standardPrice;
      data.record.discount = applyPrice / pmPrice;
      data.record.createBy = null;
      data.record.createTime = null;
      // data.record.pmPrice= value.pmPrice;
      // data.record.applyPrice= value.applyPrice;
      // data.record.leadTime= value.leadTime;
      // data.record.discount= (value.applyPrice / value.pmPrice).toFixed(2);
      data.record.opportunityId = data.record.id;
      data.record.discount = 1;
      data.record.id = null;
      quotation.value = { isLocked: 0, id: 'New', ...data?.record };

      await setFieldsValue({
        ...data.record,
      });
    }
    if (unref(isUpdate)) {
      //表单赋值
      await setFieldsValue({
        ...data.record,
      });
    }
    // 隐藏底部时禁用整个表单
    setProps({ disabled: quotation.value.isLocked == 1 });

    //初始化字典数据
    let dictData = [];
    if (data?.record?.customerId) {
      dictData = await initDictOptions(`sys_customer_contact where customer_id = '${data.record.customerId}',name,id`);
    } else {
      dictData = await initDictOptions(`sys_customer_contact,name,id`);
    }
    updateSchema([
      {
        field: 'customerContactId',
        componentProps: {
          options: dictData,
        },
      },
    ]);
  });

  //设置标题
  const title = quotationNo.value;
  //表单提交事件
  async function handleSubmit(v) {
    try {
      let values = await validate();
      if(values.dayType == null) {
        message.error("请选择工期类型!!工作日/自然日!!");
        throw new Error("请选择工期类型!!工作日/自然日!!");
      }
      setDrawerProps({ confirmLoading: true });
      console.log(sampleTableRef.value.getData());
      console.log('values', values);
      //提交表单
      await saveOrUpdate(values, isUpdate.value);
      //关闭弹窗
      closeDrawer();

      //刷新列表
      emit('success');
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }

  /**
   * 处理SampleModal回调事件
   */
  async function handleSampleModalCallback(value) {
    //更新表单
    setFieldsValue({
      pmPrice: value.pmPrice,
      applyPrice: value.applyPrice,
      discount: (value.applyPrice / value.pmPrice).toFixed(2),
    });
    let values = await validate();
    if(values.dayType == null) {
      message.error("请选择工期类型!!工作日/自然日!!");
      throw new Error("请选择工期类型!!工作日/自然日!!");
    }
    await saveVo(values, isUpdate.value);
    //刷新列表
    emit('success');
  }

  /**
   * 发起审核
   */
  async function handleApply() {
    await handleSubmit();

    apply({ id: quotation.value.id }, handleApplySuccess);
  }

  /**
   * 转合同
   */
  async function handleAddContract() {
    let values = await validate();
    if(values.dayType == null) {
      message.error("请选择工期类型!!工作日/自然日!!")
      throw new Error("请选择工期类型!!工作日/自然日!!");
    }

    let record = { ...quotation.value, ...values };

    await preCheck({ id: quotation.value.id });

    openOrderDrawer(true, {
      record,
      isUpdate: false,
      showFooter: true,
    });
    emit('success');
  }

  /**
   * 订单成功回调
   */
  function handleOrderSuccess() {
    console.log(11111112222);

    localeStore.setPathTitle('/lims_order/bizOrderList', '订单管理');
    go('/lims_order/bizOrderList');
    closeDrawer();
  }

  /**
   * 解锁
   */
  function handleUnlock() {
    unlock({ id: quotation.value.id }, handleUnlockSuccess);
  }

  /**
   * 解锁成功回调
   */
  function handleUnlockSuccess() {
    //quotation.value.isLocked = 0;
    //setProps({ disabled: quotation.value.isLocked == 1 });
    activeKey.value = 'quotation';
  }

  /**
   * 发起审核成功回调
   */
  function handleApplySuccess() {
    quotation.value.isLocked = 1;
    quotation.value.statusId = '1';
    setProps({ disabled: quotation.value.isLocked == 1 });
    emit('success');
  }

  /**
   * 获取操作记录
   */
  const getTimeLine = () => {
    listDataLog({
      id: quotation.value.id,
    }).then((res) => {
      timeline.value = res;
      console.log(res);
    });
  };

  async function generateQuotation() {
    try {
      loadingModalVisible.value = true;
      let record = { ...quotation.value };
      await geneOneQuotion({ id: quotation.value.id }).then((res) => {
        if (res === '生成成功！') {
          preview(record);
        }
      });
    } catch ({ errorFields }) {
      console.log('errorFields', errorFields);
    } finally {
      loadingModalVisible.value = false;
    }
  }
  async function preview(record) {
    //(selectedRowKeys.value = []) && reload();
    const ooConfig = await getOOEditorConfig(record.id, 'quotation');
    openDocEditorModal(true, ooConfig);
  }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
  .gutter-box {
    padding: 10px 6px;
  }
  :deep(.loading-modal .ant-modal-content) {
    background: transparent;
    box-shadow: none;
  }

  :deep(.loading-modal .ant-modal-body) {
    color: #fff;
  }
</style>
