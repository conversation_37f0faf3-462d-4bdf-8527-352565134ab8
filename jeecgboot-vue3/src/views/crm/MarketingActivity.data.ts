import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '活动名称',
    align: 'center',
    dataIndex: 'activityName',
  },
  {
    title: '活动类型',
    align: 'center',
    dataIndex: 'activityType',
  },
  {
    title: '开始日期',
    align: 'center',
    dataIndex: 'startDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '结束日期',
    align: 'center',
    dataIndex: 'endDate',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
  {
    title: '省市区（县）',
    align: 'center',
    dataIndex: 'area',
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '活动名称',
    field: 'activityName',
    component: 'Input', //TODO 范围查询
    //colProps: {span: 6},
  },
  {
    label: '活动类型',
    field: 'activityType',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '开始日期',
    field: 'startDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    //colProps: {span: 6},
  },
  {
    label: '结束日期',
    field: 'endDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    //colProps: {span: 6},
  },
  {
    label: '省市区（县）',
    field: 'area',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
    //colProps: {span: 6},
  },
  {
    label: '创建人',
    field: 'createBy',
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: '创建日期',
    field: 'createTime',
    component: 'RangePicker',
    componentProps: {
      valueType: 'Date',
      showTime: true,
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '活动名称',
    field: 'activityName',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入活动名称!' }];
    },
  },
  {
    label: '活动类型',
    field: 'activityType',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入活动类型!' }];
    },
  },
  {
    label: '开始日期',
    field: 'startDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入开始日期!' }];
    },
  },
  {
    label: '结束日期',
    field: 'endDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入结束日期!' }];
    },
  },
  {
    label: '省市区（县）',
    field: 'area',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  activityName: { title: '活动名称', order: 0, view: 'text', type: 'string' },
  activityType: { title: '活动类型', order: 1, view: 'text', type: 'string' },
  startDate: { title: '开始日期', order: 2, view: 'date', type: 'string' },
  endDate: { title: '结束日期', order: 3, view: 'date', type: 'string' },
  area: { title: '省市区（县）', order: 4, view: 'pca', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
