<template>
  <div>
    <vxe-table ref="tableRef" :data="dataSource" :loading="loading" size="small" stripe :row-group-config="rowGroupConfig">
      <!-- 序号列 -->
      <vxe-column type="seq" width="60" />

      <!-- 样品编号列 -->
      <vxe-column field="sampleNo" title="样品编号" row-group-node />

      <!-- 检测指标列 -->
      <vxe-column field="analyte" title="检测指标" width="120" />

      <!-- 检测结果列 -->
      <vxe-column field="rawResult" title="检测结果" width="80" />

      <!-- 峰面积列 -->
      <vxe-column field="peakArea" title="峰面积" width="80" />

      <!-- 检测人列 -->
      <vxe-column field="updateBy" title="检测人" width="80" />

      <!-- 检测时间列 -->
      <vxe-column field="updateTime" title="检测时间" width="100" />

      <!-- 审核人列 -->
      <vxe-column field="checkedBy" title="审核人" width="80" />

      <!-- 审核时间列 -->
      <vxe-column field="checkedTime" title="审核时间" width="100" />
    </vxe-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, onMounted } from 'vue';
  import { getQCTestResults } from '../dataCentre.api';
  import 'xe-utils';
  import { VxeTableInstance, VxeTablePropTypes } from 'vxe-table';

  const props = defineProps({
    curTestIds: { require: true, type: String },
    qcTypes: { require: true, type: String },
    isActive: { type: Boolean, default: false },
  });

  const tableRef = ref<VxeTableInstance>();
  const loading = ref(false);
  const dataSource = ref<any[]>([]);
  const rowGroupConfig = reactive<VxeTablePropTypes.RowGroupConfig>({
    groupFields: ['analyte'],
  });

  // 加载数据的方法
  const reload = async () => {
    if (!props.curTestIds) {
      dataSource.value = [];
      return;
    }

    try {
      loading.value = true;
      const result = await getQCTestResults({ testIds: props.curTestIds, qcTypes: props.qcTypes });
      dataSource.value = result || [];
    } catch (error) {
      console.error('加载数据失败:', error);
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  };

  // 在组件挂载后初始化
  onMounted(() => {
    console.log('LinearityInfoTab 组件已挂载');
    // 如果当前tab是激活的且有测试ID，则加载数据
    if (props.isActive && props.curTestIds && props.qcTypes) {
      console.log('组件挂载时 - tab激活且有测试ID，准备加载数据');
      reload();
    }
  });

  // 监听curTestIds和isActive的变化
  watch(
    [() => props.curTestIds, () => props.qcTypes, () => props.isActive],
    ([newTestIds, newQcTypes, isActive]) => {
      console.log('监听到变化 - curTestIds:', newTestIds, 'qcTypes:', newQcTypes, 'isActive:', isActive);

      // 只有当tab激活且有测试ID时才加载数据
      if (isActive && newTestIds && newQcTypes) {
        console.log('线性tab激活且有测试ID，准备加载数据:', newTestIds, newQcTypes);
        reload();
      } else if (!newTestIds && !newQcTypes) {
        // 没有测试ID时清空数据
        dataSource.value = [];
      }
    },
    { immediate: true } // 立即执行一次，以便在组件挂载后检查条件
  );
</script>

<style lang="less" scoped>
  :deep(.group-row) {
    background-color: #f5f7fa !important;
    font-weight: bold;
    color: #1890ff;

    // 覆盖条纹样式
    &.even-row,
    &.odd-row {
      background-color: #f5f7fa !important;
    }

    // 鼠标悬停效果
    &:hover {
      background-color: #e6f7ff !important;
    }
  }
</style>
