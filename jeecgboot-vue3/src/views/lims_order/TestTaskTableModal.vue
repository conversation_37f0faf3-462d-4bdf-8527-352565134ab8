<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" width="80%">
    <TestTaskTable :sampleId="sampleId" :disabled="disabled" :sampleNo="sampleNo" />
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicModal, useModalInner } from '/src/components/Modal';
  import { BasicForm, useForm } from '/src/components/Form';
  import { formSchema } from '../TestTask.data';
  import { saveOrUpdate } from '../TestTask.api';
  import {initDictOptions} from "@/utils/dict";
  import TestTaskTable from '@/views/lims_order/TestTaskTable.vue';
  import TestTaskList from '@/views/lims_order/TestTaskList.vue';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const sampleId = ref("");
  const sampleNo = ref("");
  const disabled = ref(false);

  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    console.log(data.sampleId);
    console.log(data.disabled);
    sampleId.value = data.sampleId;
    disabled.value = data.disabled;
    sampleNo.value = data.sampleNo;

    console.log(data.sampleNo);

  });
  //设置标题
  const title = "测试项目管理";

</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
