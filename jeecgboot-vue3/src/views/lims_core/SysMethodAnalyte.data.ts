import { FormSchema } from '/@/components/Table';
import { JVxeTypes, JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
//列表数据
export const columns: JVxeColumn[] = [
  // {
  //   title: '顺序',
  //   key: 'sortNum',
  //   type: 'text',
  //   width: '70px',
  //   disabled: true,
  //   placeholder: '请输入${title}',
  //   defaultValue: '',
  //   show: false,
  // },
  {
    title: '检测指标',
    key: 'analyteId',
    type: JVxeTypes.select,
    dictCode: 'sys_analyte,name,id',
    width: '120px',
    align: 'center',
    fixed: 'left',
    disabled: true,
  },
  {
    title: '结果类型',
    key: 'resultType',
    type: JVxeTypes.select,
    dictCode: 'test_result_type',
    width: '120px',
    align: 'center',
    defaultValue: 1,
  },
  {
    title: '检测单位',
    key: 'unitId',
    type: JVxeTypes.select,
    dictCode: 'sys_unit,unit_name,id',
    width: '120px',
    align: 'center',
  },
  {
    title: '计算公式',
    key: 'calcExpr',
    type: JVxeTypes.input,
    width: '200px',
    align: 'center',
  },
  {
    title: '检出限',
    key: 'lod',
    type: JVxeTypes.input,
    width: '120px',
    align: 'center',
  },
  {
    title: '定量限',
    key: 'loq',
    type: JVxeTypes.input,
    width: '120px',
    align: 'center',
  },
  {
    title: '修约算法',
    key: 'roundingAlgorithmId',
    type: JVxeTypes.select,
    dictCode: 'rounding_algorithm',
    width: '200px',
    align: 'center',
  },
  {
    title: '修约方式',
    key: 'roundingWayId',
    type: JVxeTypes.select,
    dictCode: 'rounding_way',
    width: '120px',
    align: 'center',
  },
  {
    title: '修约精度',
    key: 'roundingPrecision',
    type: JVxeTypes.input,
    width: '120px',
    align: 'center',
  },
  {
    title: '精密度类型',
    key: 'precisionTypeId',
    type: JVxeTypes.select,
    dictCode: 'precision_type',
    width: '120px',
    align: 'center',
  },
  {
    title: '精密度要求',
    key: 'precisionReq',
    type: JVxeTypes.input,
    width: '120px',
    align: 'center',
  },
];

//子表列表数据
export const sysMethodAnalyteRoudingColumns: JVxeColumn[] = [
  {
    title: '修约算法',
    key: 'roundingAlgorithm',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'rounding_algorithm',
    width: '200px',
    align: 'center',
  },
  {
    title: '修约方式',
    key: 'roundingWay',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'rounding_way',
    width: '200px',
    align: 'center',
  },
  {
    title: '修约精度',
    key: 'roudingPrecision',
    type: JVxeTypes.input,
    width: '200px',
    dataIndex: 'roudingPrecision',
    align: 'center',
  },
];
//子表列表数据
export const sysMethodAnalytePrecisionColumns: JVxeColumn[] = [
  {
    title: '决定因素类型',
    key: 'paraTypeId',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'lab_dict',
    width: '200px',
    align: 'center',
  },
  {
    title: '决定因素',
    key: 'paraValue',
    type: JVxeTypes.select,
    options: [],
    dictCode: '',
    width: '200px',
    align: 'center',
  },
  {
    title: '精密度类型',
    key: 'precisionTypeId',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'precision_type',
    width: '200px',
    align: 'center',
  },
  {
    title: '精密度要求',
    key: 'precisionReq',
    type: JVxeTypes.input,
    width: '200px',
    align: 'center',
  },
];
//子表表格配置
export const sysMethodAnalyteRoudingJVxeColumns: JVxeColumn[] = [
  {
    title: '修约算法',
    key: 'roundingAlgorithm',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'rounding_algorithm',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '修约方式',
    key: 'roundingWay',
    type: JVxeTypes.select,
    options: [],
    dictCode: 'rounding_way',
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '修约精度',
    key: 'roudingPrecision',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
];
export const sysMethodAnalytePrecisionJVxeColumns: JVxeColumn[] = [
  {
    title: '决定因素类型',
    key: 'paraTypeId',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '0',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '决定因素',
    key: 'paraValue',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '0',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '精密度类型',
    key: 'precisionTypeId',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
  {
    title: '精密度要求',
    key: 'precisionReq',
    type: JVxeTypes.input,
    width: '200px',
    placeholder: '请输入${title}',
    defaultValue: '',
    validateRules: [{ required: true, message: '${title}不能为空' }],
  },
];

// /**
// * 流程表单调用这个方法获取formSchema
// * @param param
// */
// export function getBpmFormSchema(_formData): FormSchema[]{
//   // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
//   return formSchema;
// }
