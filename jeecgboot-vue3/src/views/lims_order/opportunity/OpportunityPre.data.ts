import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
import { initDictOptions } from '@/utils/dict';

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '编号',
    field: 'opportunityNo',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    label: '商机名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '商机名称!' }];
    },
  },
  {
    label: '业务分类',
    field: 'bizTypeId',
    component: 'JCategorySelect',
    componentProps: {
      pcode: 'C09', //TODO back和事件未添加，暂时有问题
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入业务分类!' }];
    },
  },
  {
    label: '客户',
    field: 'customerId',
    component: 'JSearchSelect',
    componentProps: ({ formModel, formActionType }) => {
      return {
        dict: 'sys_customer,name,id',
        onChange: async (val: any) => {
          const { updateSchema } = formActionType;
          const dictData = await initDictOptions(`sys_customer_contact where customer_id = '${val}',name,id`);
          formModel.customerContactId = undefined;
          console.log(dictData);
          updateSchema([
            {
              field: 'customerContactId',
              componentProps: {
                options: dictData,
              },
            },
          ]);
        },
      };
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户!' }];
    },
  },
  {
    label: '客户联系人',
    field: 'customerContactId',
    component: 'JSearchSelect',
    componentProps: {
      replaceFields: {
        title: 'name',
        key: 'id',
        value: 'id',
      },
    },
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入客户联系人!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '商机名称', order: 0, view: 'text', type: 'string' },
  responsiblePerson: { title: '负责人', order: 1, view: 'sel_user', type: 'string' },
  customerId: { title: '客户名称', order: 2, view: 'list', type: 'string', dictTable: 'sys_customer', dictCode: 'id', dictText: 'name' },
  endDate: { title: '结单日期', order: 3, view: 'date', type: 'string' },
  customerContactId: {
    title: '客户联系人姓名',
    order: 4,
    view: 'list',
    type: 'string',
    dictTable: 'sys_customer_contact',
    dictCode: 'id',
    dictText: 'name',
  },
  customerContactPhone: { title: '客户联系人电话', order: 5, view: 'text', type: 'string' },
  bizTypeId: { title: '业务分类', order: 6, view: 'cat_tree', type: 'string', pcode: 'C09' },
  opportunitySource: { title: '商机来源', order: 7, view: 'list', type: 'string', dictCode: 'opportunity_source' },
  sourceBy: { title: '源线索', order: 8, view: 'sel_user', type: 'string' },
  opportunityStage: { title: '商机阶段', order: 9, view: 'list', type: 'string', dictCode: 'opportunity_stage' },
  amount: { title: '商机金额', order: 10, view: 'number', type: 'number' },
  winRate: { title: '赢率', order: 11, view: 'number', type: 'number' },
  estimatedAmount: { title: '预测金额', order: 12, view: 'number', type: 'number' },
  importanceLevel: { title: '重要程度', order: 13, view: 'list', type: 'string', dictCode: 'importance_level' },
  decisionMaker: { title: '决策人', order: 14, view: 'sel_user', type: 'string' },
  competitor: { title: '竞争对手', order: 15, view: 'text', type: 'string' },
  salesProcess: { title: '销售流程', order: 16, view: 'text', type: 'string' },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
